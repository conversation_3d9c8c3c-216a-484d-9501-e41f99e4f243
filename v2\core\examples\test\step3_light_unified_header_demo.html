<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Step3: 浅色统一风格表格头部演示</title>
    <link rel="stylesheet" href="../../src/styles/gantt.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #f8f5ef 0%, #f3f0e8 100%);
            color: #616161;
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #e0e0e0;
        }

        .header h1 {
            margin: 0;
            font-size: 24px;
            color: #424242;
        }

        .header p {
            margin: 10px 0 0 0;
            opacity: 0.8;
        }

        .demo-info {
            padding: 20px;
            background: #fafafa;
            border-bottom: 1px solid #e0e0e0;
        }

        .demo-info h3 {
            margin: 0 0 15px 0;
            color: #424242;
            font-size: 18px;
        }

        .color-showcase {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }

        .color-item {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #e0e0e0;
            box-shadow: 0 1px 3px rgba(0,0,0,0.05);
        }

        .color-item h4 {
            margin: 0 0 10px 0;
            color: #424242;
            font-size: 14px;
        }

        .color-swatch {
            display: flex;
            gap: 5px;
            margin-bottom: 8px;
        }

        .color-box {
            width: 24px;
            height: 24px;
            border-radius: 4px;
            border: 1px solid rgba(0,0,0,0.1);
        }

        .color-description {
            font-size: 12px;
            color: #757575;
            line-height: 1.4;
        }

        .gantt-container {
            height: 600px;
            position: relative;
        }

        .controls {
            padding: 20px;
            background: #fafafa;
            border-top: 1px solid #e0e0e0;
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }

        .control-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .control-group label {
            font-size: 12px;
            font-weight: 600;
            color: #616161;
            text-transform: uppercase;
        }

        .control-group button,
        .control-group select {
            padding: 8px 16px;
            border: 1px solid #bdbdbd;
            border-radius: 6px;
            background: white;
            color: #424242;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .control-group button:hover,
        .control-group select:hover {
            background: #f5f5f5;
            border-color: #9e9e9e;
        }

        .theme-toggle {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 10px 15px;
            background: #616161;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            z-index: 1000;
            transition: background-color 0.2s ease;
        }

        .theme-toggle:hover {
            background: #424242;
        }

        .features-section {
            padding: 20px;
            background: #f8f8f8;
            border-top: 1px solid #e0e0e0;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 15px;
        }

        .feature-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #e0e0e0;
            box-shadow: 0 1px 3px rgba(0,0,0,0.05);
        }

        .feature-card h4 {
            margin: 0 0 10px 0;
            color: #424242;
            font-size: 16px;
        }

        .feature-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .feature-list li {
            padding: 5px 0;
            color: #616161;
            font-size: 14px;
            border-bottom: 1px solid #f0f0f0;
        }

        .feature-list li:last-child {
            border-bottom: none;
        }

        .feature-list li::before {
            content: "✓";
            color: #4caf50;
            font-weight: bold;
            margin-right: 8px;
        }

        /* 深色主题切换 */
        .dark-theme {
            filter: invert(1) hue-rotate(180deg);
        }

        .dark-theme .gantt-container {
            filter: invert(1) hue-rotate(180deg);
        }

        .comparison-highlight {
            background: linear-gradient(135deg, #f8f5ef 0%, #f3f0e8 100%);
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid #bdbdbd;
        }

        .comparison-highlight h5 {
            margin: 0 0 8px 0;
            color: #424242;
            font-size: 14px;
        }

        .comparison-highlight p {
            margin: 0;
            font-size: 13px;
            color: #616161;
            line-height: 1.4;
        }
    </style>
</head>
<body>
    <button class="theme-toggle" onclick="toggleTheme()">🌙 切换深色主题</button>

    <div class="container">
        <div class="header">
            <h1>🎨 浅色统一风格表格头部演示</h1>
            <p>展示清淡、统一、舒适的甘特图任务列表头部设计</p>
        </div>

        <div class="demo-info">
            <h3>浅色统一配色方案</h3>
            <div class="color-showcase">
                <div class="color-item">
                    <h4>主色调 (Primary Light)</h4>
                    <div class="color-swatch">
                        <div class="color-box" style="background: #fefefe;"></div>
                        <div class="color-box" style="background: #fdfcf9;"></div>
                        <div class="color-box" style="background: #f8f5ef;"></div>
                        <div class="color-box" style="background: #f3f0e8;"></div>
                        <div class="color-box" style="background: #ede8dd;"></div>
                    </div>
                    <div class="color-description">极浅的暖色调，提供温和舒适的视觉体验</div>
                </div>
                <div class="color-item">
                    <h4>辅助色调 (Secondary Neutral)</h4>
                    <div class="color-swatch">
                        <div class="color-box" style="background: #fafafa;"></div>
                        <div class="color-box" style="background: #f5f5f5;"></div>
                        <div class="color-box" style="background: #eeeeee;"></div>
                        <div class="color-box" style="background: #bdbdbd;"></div>
                        <div class="color-box" style="background: #757575;"></div>
                    </div>
                    <div class="color-description">中性灰色系，确保良好的可读性和层次感</div>
                </div>
                <div class="color-item">
                    <h4>状态色调 (Status Soft)</h4>
                    <div class="color-swatch">
                        <div class="color-box" style="background: #dcfce7;"></div>
                        <div class="color-box" style="background: #fef3c7;"></div>
                        <div class="color-box" style="background: #fee2e2;"></div>
                    </div>
                    <div class="color-description">柔和的状态色，不会过于突兀</div>
                </div>
            </div>

            <div class="comparison-highlight">
                <h5>设计理念</h5>
                <p>采用极浅的色调和统一的配色方案，减少视觉疲劳，提供长时间使用的舒适体验。去除过于鲜艳的颜色，营造专业、清爽的工作环境。</p>
            </div>
        </div>

        <div class="gantt-container" id="ganttContainer"></div>

        <div class="controls">
            <div class="control-group">
                <label>视图模式</label>
                <select id="viewModeSelect">
                    <option value="day">日视图</option>
                    <option value="week">周视图</option>
                    <option value="month">月视图</option>
                    <option value="quarter">季度视图</option>
                </select>
            </div>
            <div class="control-group">
                <label>测试功能</label>
                <button id="testSortBtn">测试排序</button>
            </div>
            <div class="control-group">
                <label>列宽调整</label>
                <button id="resetColumnsBtn">重置列宽</button>
            </div>
            <div class="control-group">
                <label>数据刷新</label>
                <button id="refreshDataBtn">刷新数据</button>
            </div>
        </div>

        <div class="features-section">
            <h3>浅色统一风格特性</h3>
            <div class="features-grid">
                <div class="feature-card">
                    <h4>视觉舒适性</h4>
                    <ul class="feature-list">
                        <li>极浅的背景色，减少眼部疲劳</li>
                        <li>统一的色调，避免视觉冲突</li>
                        <li>柔和的阴影效果</li>
                        <li>适中的对比度</li>
                    </ul>
                </div>
                <div class="feature-card">
                    <h4>专业性</h4>
                    <ul class="feature-list">
                        <li>简洁的设计语言</li>
                        <li>一致的视觉层次</li>
                        <li>清晰的信息架构</li>
                        <li>专业的配色方案</li>
                    </ul>
                </div>
                <div class="feature-card">
                    <h4>用户体验</h4>
                    <ul class="feature-list">
                        <li>长时间使用不疲劳</li>
                        <li>清晰的交互反馈</li>
                        <li>直观的状态指示</li>
                        <li>流畅的动画效果</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script type="module">
        import GanttChart from '../../src/GanttChart.js';
        import DateUtils from '../../src/utils/DateUtils.js';

        // 生成演示数据
        function generateDemoData() {
            const tasks = [];
            const startDate = new Date('2024-01-01');
            const priorities = ['高', '中', '低'];
            const statuses = ['计划中', '进行中', '已完成', '暂停'];
            const categories = ['开发', '设计', '测试', '文档', '部署'];
            
            for (let i = 0; i < 25; i++) {
                const taskStart = DateUtils.addDays(startDate, i * 10);
                const duration = 7 + Math.random() * 21;
                const taskEnd = DateUtils.addDays(taskStart, duration);
                
                tasks.push({
                    id: `light-task-${i}`,
                    name: `浅色风格任务 ${i + 1}`,
                    startDate: taskStart,
                    endDate: taskEnd,
                    progress: Math.random(),
                    priority: priorities[Math.floor(Math.random() * priorities.length)],
                    status: statuses[Math.floor(Math.random() * statuses.length)],
                    category: categories[Math.floor(Math.random() * categories.length)],
                    assignee: `团队成员${i + 1}`,
                    budget: Math.floor(Math.random() * 80000) + 20000
                });
            }
            
            return tasks;
        }

        // 初始化甘特图
        let ganttInstance = null;

        function initializeGantt() {
            const container = document.getElementById('ganttContainer');
            container.innerHTML = '';
            
            const demoData = generateDemoData();
            
            ganttInstance = new GanttChart('ganttContainer', {
                data: demoData,
                viewMode: 'day',
                pixelsPerDay: 35,
                taskList: {
                    columns: [
                        { 
                            key: 'name', 
                            title: '任务名称', 
                            width: 200,
                            minWidth: 150,
                            maxWidth: 300,
                            icon: '📋',
                            description: '项目任务的详细名称'
                        },
                        { 
                            key: 'category', 
                            title: '分类', 
                            width: 80,
                            minWidth: 60,
                            maxWidth: 120,
                            icon: '🏷️',
                            description: '任务分类标签'
                        },
                        { 
                            key: 'priority', 
                            title: '优先级', 
                            width: 90,
                            minWidth: 70,
                            maxWidth: 130,
                            icon: '⭐',
                            description: '任务优先级等级'
                        },
                        { 
                            key: 'status', 
                            title: '状态', 
                            width: 100,
                            minWidth: 80,
                            maxWidth: 140,
                            icon: '📊',
                            description: '当前执行状态'
                        },
                        { 
                            key: 'assignee', 
                            title: '负责人', 
                            width: 100,
                            minWidth: 80,
                            maxWidth: 150,
                            icon: '👤',
                            description: '任务负责人员'
                        },
                        { 
                            key: 'progress', 
                            title: '进度', 
                            width: 80,
                            minWidth: 60,
                            maxWidth: 100,
                            icon: '📈',
                            description: '完成进度百分比'
                        }
                    ]
                }
            });

            ganttInstance.on('ready', () => {
                console.log('浅色统一风格甘特图初始化完成');
            });
        }

        // 主题切换
        function toggleTheme() {
            document.body.classList.toggle('dark-theme');
            const button = document.querySelector('.theme-toggle');
            if (document.body.classList.contains('dark-theme')) {
                button.textContent = '☀️ 切换亮色主题';
            } else {
                button.textContent = '🌙 切换深色主题';
            }
        }

        // 事件绑定
        document.getElementById('viewModeSelect').addEventListener('change', (e) => {
            ganttInstance.changeViewMode(e.target.value);
        });

        document.getElementById('testSortBtn').addEventListener('click', () => {
            const columns = ['name', 'category', 'priority', 'status'];
            const column = columns[Math.floor(Math.random() * columns.length)];
            ganttInstance._handleColumnSort(column);
        });

        document.getElementById('resetColumnsBtn').addEventListener('click', () => {
            ganttInstance.options.taskList.columns.forEach(col => {
                col.width = (col.minWidth + col.maxWidth) / 2;
            });
            ganttInstance.renderTaskListHeader();
        });

        document.getElementById('refreshDataBtn').addEventListener('click', () => {
            const newData = generateDemoData();
            ganttInstance.setData(newData);
        });

        // 全局函数
        window.toggleTheme = toggleTheme;

        // 初始化
        document.addEventListener('DOMContentLoaded', () => {
            console.log('页面加载完成，初始化浅色统一风格甘特图...');
            initializeGantt();
        });
    </script>
</body>
</html>

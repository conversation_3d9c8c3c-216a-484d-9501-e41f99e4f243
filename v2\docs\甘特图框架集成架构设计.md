# 甘特图框架集成架构设计

## 架构概述

采用 **"核心无关 + 框架适配器"** 的分层架构，确保核心功能与具体框架解耦。

```
┌─────────────────────────────────────────┐
│           应用层(Application)            │
├─────────────────────────────────────────┤
│         框架适配层 (Adapters)            │
│  ┌──────────┐ ┌──────────┐ ┌──────────┐ │
│  │   Vue3   │ │  React   │ │ Angular  │ │
│  │ Wrapper  │ │ Wrapper  │ │ Wrapper  │ │
│  └──────────┘ └──────────┘ └──────────┘ │
├─────────────────────────────────────────┤
│            核心层 (Core)                │
│        甘特图原生实现 (无框架依赖)        │
└─────────────────────────────────────────┘
```

## 项目结构设计

```
gantt-chart/
├── core/                          # 核心层 (无构建工具)
│   ├── src/
│   │   ├── GanttChart.js         # 主要实现
│   │   ├── renderers/            # 渲染器
│   │   ├── utils/                # 工具函数
│   │   └── styles/gantt.css      # 样式
│   └── examples/basic.html       # 原生示例
│
├── adapters/                      # 适配器层 (需要构建工具)
│   ├── vue/
│   │   ├── package.json          # Vue 相关依赖
│   │   ├── vite.config.js        # Vue 构建配置
│   │   ├── src/
│   │   │   ├── GanttChart.vue    # Vue 组件封装
│   │   │   └── index.js          # 导出
│   │   └── examples/
│   │
│   ├── react/
│   │   ├── package.json          # React 相关依赖
│   │   ├── vite.config.js        # React 构建配置
│   │   ├── src/
│   │   │   ├── GanttChart.jsx    # React 组件封装
│   │   │   └── index.js          # 导出
│   │   └── examples/
│   │
│   └── angular/                  # 未来扩展
│
└── packages/                     # 发包配置
    ├── gantt-chart-core/         # 核心包
    ├── gantt-chart-vue/          # Vue 包
    └── gantt-chart-react/        # React 包
```

## 核心层实现 (无构建工具)

### 优势

- ✅ **零依赖**：可以在任何环境中运行
- ✅ **高性能**：没有框架抽象层开销
- ✅ **框架无关**：可以集成到任何技术栈
- ✅ **简单调试**：源码直接可读，无需 sourcemap

### 设计原则

```javascript
// core/src/GanttChart.js
export class GanttChart {
  constructor(container, options = {}) {
    // 纯原生实现，不依赖任何框架
    this.container = container;
    this.options = options;
    // ...
  }

  // 提供完整的 API 供适配器调用
  setData(data) {
    /* ... */
  }
  updateOptions(options) {
    /* ... */
  }
  on(event, callback) {
    /* ... */
  }
  off(event, callback) {
    /* ... */
  }
  destroy() {
    /* ... */
  }
}
```

## 框架适配层实现 (需要构建工具)

### Vue3 适配器

**packages.json**：

```json
{
  "name": "gantt-chart-vue",
  "dependencies": {
    "gantt-chart-core": "workspace:*"
  },
  "devDependencies": {
    "vue": "^3.3.0",
    "vite": "^4.4.0",
    "@vitejs/plugin-vue": "^4.2.0"
  }
}
```

**GanttChart.vue**：

```vue
<template>
  <div ref="containerRef" :class="containerClass"></div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch } from "vue";
import { GanttChart } from "gantt-chart-core";

const props = defineProps({
  data: Array,
  options: Object,
  // ... 其他 props
});

const emit = defineEmits(["taskClick", "dataChange"]);

const containerRef = ref(null);
let ganttInstance = null;

onMounted(() => {
  ganttInstance = new GanttChart(containerRef.value, {
    ...props.options,
    data: props.data,
    // 事件桥接
    onTaskClick: (task) => emit("taskClick", task),
    onDataChange: (data) => emit("dataChange", data),
  });
});

// 响应式数据更新
watch(
  () => props.data,
  (newData) => {
    ganttInstance?.setData(newData);
  },
  { deep: true }
);

watch(
  () => props.options,
  (newOptions) => {
    ganttInstance?.updateOptions(newOptions);
  },
  { deep: true }
);

onUnmounted(() => {
  ganttInstance?.destroy();
});
</script>
```

### React 适配器

**package.json**：

```json
{
  "name": "gantt-chart-react",
  "dependencies": {
    "gantt-chart-core": "workspace:*"
  },
  "devDependencies": {
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "vite": "^4.4.0",
    "@vitejs/plugin-react": "^4.0.0"
  }
}
```

**GanttChart.jsx**：

```jsx
import React, { useRef, useEffect, useCallback } from "react";
import { GanttChart as CoreGanttChart } from "gantt-chart-core";

export const GanttChart = React.forwardRef(
  ({ data, options = {}, onTaskClick, onDataChange, className, ...props }, ref) => {
    const containerRef = useRef(null);
    const ganttRef = useRef(null);

    // 将实例暴露给父组件
    React.useImperativeHandle(ref, () => ganttRef.current);

    useEffect(() => {
      ganttRef.current = new CoreGanttChart(containerRef.current, {
        ...options,
        data,
        onTaskClick,
        onDataChange,
      });

      return () => {
        ganttRef.current?.destroy();
      };
    }, []);

    // 数据更新
    useEffect(() => {
      ganttRef.current?.setData(data);
    }, [data]);

    // 配置更新
    useEffect(() => {
      ganttRef.current?.updateOptions(options);
    }, [options]);

    return <div ref={containerRef} className={className} {...props} />;
  }
);
```

## 使用方式对比

### 原生使用 (无构建工具)

```html
<script type="module">
  import { GanttChart } from './core/src/GanttChart.js'

  const gantt = new GanttChart(container, { data: [...] })
</script>
```

### Vue3 项目中使用 (需要构建工具)

```bash
npm install gantt-chart-vue
```

```vue
<template>
  <GanttChart :data="tasks" :options="ganttOptions" @task-click="handleTaskClick" />
</template>

<script setup>
import { GanttChart } from "gantt-chart-vue";
</script>
```

### React 项目中使用 (需要构建工具)

```bash
npm install gantt-chart-react
```

```jsx
import { GanttChart } from "gantt-chart-react";

function App() {
  return <GanttChart data={tasks} options={ganttOptions} onTaskClick={handleTaskClick} />;
}
```

## 总结

### 推荐策略：

1. **先开发核心层**：使用纯 HTML+JS+SVG，无构建工具
2. **核心功能稳定后**：再开发框架适配器
3. **按需发布**：可以只发布核心包，也可以发布框架特定包

### 构建工具需求：

- **核心层**：❌ 不需要
- **Vue/React 适配器**：✅ 需要 vite/webpack
- **最终用户项目**：取决于用户选择的集成方式

这样设计的好处是：

- 核心功能与框架解耦，维护成本低
- 用户可以选择最适合的集成方式
- 未来可以轻松支持更多框架

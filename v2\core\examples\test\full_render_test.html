<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>时间轴全渲染测试</title>
    <link rel="stylesheet" href="../../src/styles/gantt.css">
    <style>
        body {
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
        }

        .test-container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .test-header {
            background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }

        .test-header h1 {
            margin: 0 0 10px 0;
            font-size: 24px;
        }

        .test-header p {
            margin: 0;
            opacity: 0.9;
            font-size: 14px;
        }

        .test-info {
            background: #f8f9fa;
            padding: 20px;
            border-bottom: 1px solid #eee;
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .info-item {
            background: white;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid #6f42c1;
        }

        .info-label {
            font-size: 12px;
            color: #666;
            text-transform: uppercase;
            margin-bottom: 5px;
        }

        .info-value {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            font-family: monospace;
        }

        .gantt-container {
            height: 600px;
            margin: 0;
            border-radius: 0;
            border: none;
            box-shadow: none;
        }

        .performance-indicator {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(111, 66, 193, 0.9);
            color: white;
            padding: 15px;
            border-radius: 8px;
            font-family: monospace;
            font-size: 12px;
            z-index: 1000;
            min-width: 250px;
        }

        .performance-indicator h4 {
            margin: 0 0 10px 0;
            font-size: 14px;
            color: #fff;
        }

        .performance-indicator div {
            margin-bottom: 5px;
            display: flex;
            justify-content: space-between;
        }

        .performance-indicator div:last-child {
            margin-bottom: 0;
        }

        .performance-good {
            color: #28a745;
        }

        .performance-warning {
            color: #ffc107;
        }

        .performance-danger {
            color: #dc3545;
        }

        .test-instructions {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 6px;
            padding: 15px;
            margin: 20px;
            color: #004085;
        }

        .test-instructions h4 {
            margin: 0 0 10px 0;
            color: #004085;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>🚀 时间轴全渲染测试</h1>
            <p>测试移除虚拟化后的全渲染性能和效果</p>
        </div>

        <div class="test-instructions">
            <h4>🧪 测试说明：</h4>
            <p>此版本移除了时间轴的虚拟化渲染，改为一次性渲染所有时间刻度。观察滚动性能和初始渲染时间。</p>
        </div>

        <div class="test-info">
            <h3>📊 渲染信息</h3>
            <div class="info-grid">
                <div class="info-item">
                    <div class="info-label">总刻度数量</div>
                    <div class="info-value" id="totalScales">-</div>
                </div>
                <div class="info-item">
                    <div class="info-label">初始渲染时间</div>
                    <div class="info-value" id="renderTime">-</div>
                </div>
                <div class="info-item">
                    <div class="info-label">时间轴总宽度</div>
                    <div class="info-value" id="totalWidth">-</div>
                </div>
                <div class="info-item">
                    <div class="info-label">渲染模式</div>
                    <div class="info-value">全渲染</div>
                </div>
                <div class="info-item">
                    <div class="info-label">滚动位置</div>
                    <div class="info-value" id="scrollPos">0</div>
                </div>
                <div class="info-item">
                    <div class="info-label">内存使用</div>
                    <div class="info-value" id="memoryUsage">-</div>
                </div>
            </div>
        </div>

        <div id="gantt-container" class="gantt-container"></div>
    </div>

    <div class="performance-indicator" id="performanceIndicator">
        <h4>⚡ 性能监控</h4>
        <div>FPS: <span id="fps">-</span></div>
        <div>滚动延迟: <span id="scrollDelay">-</span></div>
        <div>DOM节点: <span id="domNodes">-</span></div>
        <div>状态: <span id="performanceStatus">监控中...</span></div>
    </div>

    <script type="module">
        import GanttChart from '../../src/GanttChart.js';

        // 性能监控
        let frameCount = 0;
        let lastTime = performance.now();
        let scrollStartTime = 0;

        function updateFPS() {
            frameCount++;
            const currentTime = performance.now();
            if (currentTime - lastTime >= 1000) {
                document.getElementById('fps').textContent = frameCount;
                frameCount = 0;
                lastTime = currentTime;
            }
            requestAnimationFrame(updateFPS);
        }
        updateFPS();

        // 生成测试数据
        function generateTestData() {
            const data = [];
            const startDate = new Date('2024-01-01');
            
            for (let i = 0; i < 25; i++) {
                const taskStartDate = new Date(startDate);
                taskStartDate.setDate(startDate.getDate() + i * 8);
                
                const taskEndDate = new Date(taskStartDate);
                taskEndDate.setDate(taskStartDate.getDate() + 6);
                
                data.push({
                    id: `task-${i + 1}`,
                    name: `任务 ${i + 1}`,
                    startDate: taskStartDate.toISOString().split('T')[0],
                    endDate: taskEndDate.toISOString().split('T')[0],
                    progress: Math.random(),
                    assignee: `用户${i + 1}`,
                    status: ['pending', 'in-progress', 'completed'][Math.floor(Math.random() * 3)],
                    level: 0
                });
            }
            
            return data;
        }

        // 初始化甘特图
        let ganttInstance;
        const initStartTime = performance.now();
        
        try {
            ganttInstance = new GanttChart('gantt-container', {
                data: generateTestData(),
                viewMode: 'day',
                pixelsPerDay: 40,
                taskList: {
                    width: 280,
                    columns: [
                        { key: 'name', title: '任务名称', width: 180 },
                        { key: 'assignee', title: '负责人', width: 100 }
                    ]
                },
                timeline: {
                    showWeekday: true,
                    adaptiveWidth: true,
                    weekdayFormat: 'short'
                }
            });

            const initEndTime = performance.now();
            const initTime = Math.round(initEndTime - initStartTime);
            
            console.log('甘特图初始化成功');
            
            // 更新渲染信息
            if (ganttInstance.timeScale) {
                const allScales = ganttInstance.timeScale.getAllScales();
                const totalWidth = ganttInstance.timeScale.getTotalWidth();
                
                document.getElementById('totalScales').textContent = allScales.length;
                document.getElementById('renderTime').textContent = `${initTime}ms`;
                document.getElementById('totalWidth').textContent = `${Math.round(totalWidth)}px`;
                
                // 估算内存使用
                const estimatedMemory = Math.round(allScales.length * 0.5); // 每个刻度约0.5KB
                document.getElementById('memoryUsage').textContent = `~${estimatedMemory}KB`;
            }

            // 监听滚动事件
            ganttInstance.on('scroll', (data) => {
                document.getElementById('scrollPos').textContent = Math.round(data.x);
                
                if (scrollStartTime === 0) {
                    scrollStartTime = performance.now();
                }
                
                // 计算滚动延迟
                const scrollDelay = Math.round(performance.now() - scrollStartTime);
                document.getElementById('scrollDelay').textContent = `${scrollDelay}ms`;
                
                setTimeout(() => {
                    scrollStartTime = 0;
                }, 100);
            });

            // 更新DOM节点数量
            setInterval(() => {
                const domNodes = document.querySelectorAll('.timeline-scale').length;
                document.getElementById('domNodes').textContent = domNodes;
                
                // 性能状态评估
                const fps = parseInt(document.getElementById('fps').textContent) || 0;
                let status = '良好';
                let statusClass = 'performance-good';
                
                if (fps < 30) {
                    status = '较差';
                    statusClass = 'performance-danger';
                } else if (fps < 50) {
                    status = '一般';
                    statusClass = 'performance-warning';
                }
                
                const statusElement = document.getElementById('performanceStatus');
                statusElement.textContent = status;
                statusElement.className = statusClass;
            }, 2000);

        } catch (error) {
            console.error('甘特图初始化失败:', error);
        }
    </script>
</body>
</html>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Step3: 主题控制测试</title>
    <link rel="stylesheet" href="../../src/styles/gantt.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            transition: all 0.3s ease;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .header {
            background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }

        .header h1 {
            margin: 0;
            font-size: 24px;
        }

        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
        }

        .theme-controls {
            padding: 20px;
            background: #f8fafc;
            border-bottom: 1px solid #e2e8f0;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }

        .control-section {
            background: white;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #e2e8f0;
        }

        .control-section h3 {
            margin: 0 0 15px 0;
            color: #374151;
            font-size: 16px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .theme-buttons {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 10px;
            margin-bottom: 15px;
        }

        .theme-btn {
            padding: 10px;
            border: 2px solid #e2e8f0;
            border-radius: 6px;
            background: white;
            cursor: pointer;
            text-align: center;
            transition: all 0.2s ease;
            font-size: 14px;
            font-weight: 500;
        }

        .theme-btn:hover {
            border-color: #6366f1;
            background: #f3f4f6;
        }

        .theme-btn.active {
            border-color: #6366f1;
            background: #eef2ff;
            color: #4f46e5;
        }

        .api-buttons {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .api-btn {
            padding: 10px 15px;
            border: 1px solid #6366f1;
            border-radius: 6px;
            background: white;
            color: #6366f1;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s ease;
        }

        .api-btn:hover {
            background: #6366f1;
            color: white;
        }

        .status-display {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #e2e8f0;
        }

        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #f3f4f6;
        }

        .status-item:last-child {
            border-bottom: none;
        }

        .status-label {
            font-size: 13px;
            color: #6b7280;
        }

        .status-value {
            font-size: 14px;
            font-weight: 600;
            color: #374151;
            font-family: monospace;
        }

        .gantt-container {
            height: 500px;
            position: relative;
            border: 2px solid #6366f1;
        }

        .demo-info {
            padding: 20px;
            background: #f1f5f9;
            border-top: 1px solid #e2e8f0;
        }

        .demo-info h3 {
            margin: 0 0 15px 0;
            color: #374151;
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .info-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #6366f1;
        }

        .info-card h4 {
            margin: 0 0 10px 0;
            color: #374151;
            font-size: 16px;
        }

        .feature-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .feature-list li {
            padding: 5px 0;
            color: #6b7280;
            font-size: 14px;
            border-bottom: 1px solid #f3f4f6;
        }

        .feature-list li:last-child {
            border-bottom: none;
        }

        .feature-list li::before {
            content: "✓";
            color: #10b981;
            font-weight: bold;
            margin-right: 8px;
        }

        /* 深色主题适配 */
        body.dark-mode {
            background-color: #1f2937;
            color: #f9fafb;
        }

        body.dark-mode .container {
            background: #374151;
            box-shadow: 0 2px 10px rgba(0,0,0,0.3);
        }

        body.dark-mode .theme-controls {
            background: #4b5563;
        }

        body.dark-mode .control-section,
        body.dark-mode .status-display,
        body.dark-mode .info-card {
            background: #6b7280;
            border-color: #9ca3af;
            color: #f9fafb;
        }

        body.dark-mode .demo-info {
            background: #4b5563;
        }

        body.dark-mode .theme-btn,
        body.dark-mode .api-btn {
            background: #6b7280;
            color: #f9fafb;
            border-color: #9ca3af;
        }

        body.dark-mode .theme-btn:hover,
        body.dark-mode .api-btn:hover {
            background: #9ca3af;
        }

        body.dark-mode .theme-btn.active {
            background: #4f46e5;
            color: white;
            border-color: #6366f1;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎨 主题控制测试</h1>
            <p>测试甘特图的主题配置和切换功能</p>
        </div>

        <div class="theme-controls">
            <div class="control-section">
                <h3>🌈 主题模式选择</h3>
                <div class="theme-buttons">
                    <button class="theme-btn active" data-theme="light">☀️ 浅色</button>
                    <button class="theme-btn" data-theme="dark">🌙 深色</button>
                    <button class="theme-btn" data-theme="auto">🔄 自动</button>
                </div>
                <div class="status-display">
                    <div class="status-item">
                        <span class="status-label">当前主题</span>
                        <span class="status-value" id="currentTheme">浅色</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">系统偏好</span>
                        <span class="status-value" id="systemPreference">-</span>
                    </div>
                </div>
            </div>

            <div class="control-section">
                <h3>⚡ API 操作</h3>
                <div class="api-buttons">
                    <button class="api-btn" onclick="testSetTheme()">setTheme() 测试</button>
                    <button class="api-btn" onclick="testToggleTheme()">toggleTheme() 测试</button>
                    <button class="api-btn" onclick="testPageTheme()">页面主题切换</button>
                    <button class="api-btn" onclick="resetToDefault()">重置为默认</button>
                </div>
            </div>

            <div class="control-section">
                <h3>📊 状态监控</h3>
                <div class="status-display">
                    <div class="status-item">
                        <span class="status-label">容器类名</span>
                        <span class="status-value" id="containerClasses">-</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">主题配置</span>
                        <span class="status-value" id="themeConfig">-</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">切换次数</span>
                        <span class="status-value" id="toggleCount">0</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="gantt-container" id="ganttContainer"></div>

        <div class="demo-info">
            <h3>功能说明</h3>
            <div class="info-grid">
                <div class="info-card">
                    <h4>配置选项</h4>
                    <ul class="feature-list">
                        <li>theme.mode: "light" | "dark" | "auto"</li>
                        <li>theme.customClass: 自定义CSS类名</li>
                        <li>默认为浅色主题</li>
                        <li>auto模式跟随系统偏好</li>
                    </ul>
                </div>
                <div class="info-card">
                    <h4>API 方法</h4>
                    <ul class="feature-list">
                        <li>setTheme(mode): 设置主题模式</li>
                        <li>toggleTheme(): 切换主题</li>
                        <li>applyTheme(): 应用主题</li>
                        <li>支持链式调用</li>
                    </ul>
                </div>
                <div class="info-card">
                    <h4>CSS 类名</h4>
                    <ul class="feature-list">
                        <li>.gantt-chart.light-theme: 浅色主题</li>
                        <li>.gantt-chart.dark-theme: 深色主题</li>
                        <li>自动移除冲突的类名</li>
                        <li>支持自定义类名扩展</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script type="module">
        import GanttChart from '../../src/GanttChart.js';
        import DateUtils from '../../src/utils/DateUtils.js';

        // 生成测试数据
        function generateTestData() {
            const tasks = [];
            const startDate = new Date('2024-01-01');
            
            for (let i = 0; i < 10; i++) {
                const taskStart = DateUtils.addDays(startDate, i * 5);
                const duration = 8 + Math.random() * 15;
                const taskEnd = DateUtils.addDays(taskStart, duration);
                
                tasks.push({
                    id: `theme-test-${i}`,
                    name: `主题测试任务 ${i + 1}`,
                    startDate: taskStart,
                    endDate: taskEnd,
                    progress: Math.random()
                });
            }
            
            return tasks;
        }

        // 初始化甘特图
        let ganttInstance = null;
        let toggleCount = 0;

        function initializeGantt() {
            const container = document.getElementById('ganttContainer');
            container.innerHTML = '';
            
            const testData = generateTestData();
            
            ganttInstance = new GanttChart('ganttContainer', {
                data: testData,
                viewMode: 'day',
                pixelsPerDay: 35,
                theme: {
                    mode: 'light', // 默认浅色主题
                    customClass: ''
                },
                timeline: {
                    showWeekday: true,
                    adaptiveWidth: true,
                    minScaleWidth: 30,
                    maxScaleWidth: 100,
                    weekdayFormat: 'short'
                },
                taskList: {
                    columns: [
                        { key: 'name', title: '任务名称', width: 180 },
                        { key: 'progress', title: '进度', width: 80 }
                    ]
                }
            });

            ganttInstance.on('ready', () => {
                updateStatus();
            });
        }

        // 更新状态显示
        function updateStatus() {
            if (!ganttInstance) return;

            const themeConfig = ganttInstance.options.theme;
            const container = ganttInstance.elements.container;
            const classes = Array.from(container.classList).join(' ');
            
            document.getElementById('currentTheme').textContent = getThemeName(themeConfig.mode);
            document.getElementById('containerClasses').textContent = classes || '无';
            document.getElementById('themeConfig').textContent = JSON.stringify(themeConfig);
            document.getElementById('toggleCount').textContent = toggleCount;
            
            // 检测系统偏好
            const systemPreference = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches ? '深色' : '浅色';
            document.getElementById('systemPreference').textContent = systemPreference;
        }

        function getThemeName(mode) {
            const names = {
                'light': '浅色',
                'dark': '深色',
                'auto': '自动'
            };
            return names[mode] || mode;
        }

        // 主题切换
        document.querySelectorAll('.theme-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                document.querySelectorAll('.theme-btn').forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
                
                const theme = btn.dataset.theme;
                ganttInstance.setTheme(theme);
                toggleCount++;
                updateStatus();
            });
        });

        // API 测试函数
        window.testSetTheme = function() {
            const modes = ['light', 'dark', 'auto'];
            const currentMode = ganttInstance.options.theme.mode;
            const currentIndex = modes.indexOf(currentMode);
            const nextMode = modes[(currentIndex + 1) % modes.length];
            
            ganttInstance.setTheme(nextMode);
            toggleCount++;
            updateStatus();
            
            // 更新按钮状态
            document.querySelectorAll('.theme-btn').forEach(b => b.classList.remove('active'));
            document.querySelector(`[data-theme="${nextMode}"]`).classList.add('active');
        };

        window.testToggleTheme = function() {
            const newMode = ganttInstance.toggleTheme();
            toggleCount++;
            updateStatus();
            
            // 更新按钮状态
            document.querySelectorAll('.theme-btn').forEach(b => b.classList.remove('active'));
            document.querySelector(`[data-theme="${newMode}"]`).classList.add('active');
        };

        window.testPageTheme = function() {
            document.body.classList.toggle('dark-mode');
        };

        window.resetToDefault = function() {
            ganttInstance.setTheme('light');
            document.body.classList.remove('dark-mode');
            toggleCount = 0;
            updateStatus();
            
            // 重置按钮状态
            document.querySelectorAll('.theme-btn').forEach(b => b.classList.remove('active'));
            document.querySelector('[data-theme="light"]').classList.add('active');
        };

        // 初始化
        document.addEventListener('DOMContentLoaded', () => {
            console.log('页面加载完成，初始化主题控制测试...');
            initializeGantt();
        });
    </script>
</body>
</html>

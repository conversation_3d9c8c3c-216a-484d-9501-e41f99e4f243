import TaskItem from './TaskItem.js';

/**
 * 多时间线布局管理器
 * 专门处理复杂的Y轴布局计算
 */
class TimelineLayoutManager {
  constructor(options = {}) {
    this.defaultRowHeight = options.defaultRowHeight || 40;
    this.timelineHeight = options.timelineHeight || 20;
    this.timelineSpacing = options.timelineSpacing || 8;
    this.labelHeight = options.labelHeight || 20;
  }

  /**
   * 计算任务列表的完整布局信息
   * @param {TaskItem[]} tasks - 任务列表
   * @returns {Object} 布局信息
   */
  calculateLayout(tasks) {
    const layoutInfo = {
      tasks: [],
      totalHeight: 0,
      maxRowHeight: this.defaultRowHeight
    };

    let currentY = 0;

    tasks.forEach(task => {
      const taskLayout = this.calculateTaskLayout(task, currentY);
      layoutInfo.tasks.push(taskLayout);
      
      currentY += taskLayout.height;
      layoutInfo.maxRowHeight = Math.max(layoutInfo.maxRowHeight, taskLayout.height);
    });

    layoutInfo.totalHeight = currentY;
    return layoutInfo;
  }

  /**
   * 计算单个任务的布局
   * @param {TaskItem} task - 任务对象
   * @param {number} baseY - 基础Y坐标
   * @returns {Object} 任务布局信息
   */
  calculateTaskLayout(task, baseY = 0) {
    const layout = {
      task,
      baseY,
      height: task.getRenderHeight(),
      timelines: []
    };

    if (task.collapsed || task.timelines.length === 0) {
      return layout;
    }

    // 计算每条时间线的布局
    let timelineY = baseY;
    
    task.getVisibleTimelines().forEach((timeline, index) => {
      const timelineLayout = {
        timeline,
        y: timelineY + timeline.yOffset,
        height: timeline.height,
        milestones: []
      };

      // 计算里程碑位置
      timeline.milestones.forEach(milestone => {
        timelineLayout.milestones.push({
          milestone,
          y: timelineLayout.y + (timeline.height / 2), // 里程碑位于时间线中心
          x: null // X坐标由时间轴计算器确定
        });
      });

      layout.timelines.push(timelineLayout);
    });

    return layout;
  }

  /**
   * 检查Y轴位置是否有重叠
   * @param {Object[]} layouts - 布局数组
   * @returns {boolean} 是否有重叠
   */
  checkOverlap(layouts) {
    for (let i = 0; i < layouts.length - 1; i++) {
      const current = layouts[i];
      const next = layouts[i + 1];
      
      if (current.baseY + current.height > next.baseY) {
        return true;
      }
    }
    return false;
  }

  /**
   * 优化时间线间距以避免重叠
   * @param {TaskItem} task - 任务对象
   */
  optimizeSpacing(task) {
    const visibleTimelines = task.getVisibleTimelines();
    if (visibleTimelines.length <= 1) return;

    // 计算最小间距
    const minSpacing = 5;
    let currentY = 0;

    visibleTimelines.forEach(timeline => {
      timeline.yOffset = currentY;
      currentY += timeline.height + Math.max(minSpacing, task.timelineSpacing);
    });

    task.updateTimelineLayout();
  }
}

export default TimelineLayoutManager;
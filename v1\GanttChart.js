class GanttChart {
  constructor(containerId, options = {}) {
    this.container = document.getElementById(containerId);
    
    // 检查是否已存在SVG元素，如果不存在则创建
    this.svg = document.getElementById("ganttSvg");
    if (!this.svg) {
      this.svg = document.createElementNS("http://www.w3.org/2000/svg", "svg");
      this.svg.setAttribute("id", "ganttSvg");
      this.svg.setAttribute("width", "1200");
      this.svg.setAttribute("height", "600");
      this.container.appendChild(this.svg);
    }
    
    this.tooltip = document.getElementById("tooltip");

    // 默认配置
    this.config = {
      width: 1200,
      height: 600,
      taskHeight: 30,
      taskMargin: 5,
      leftPanelWidth: 200,
      headerHeight: 60,
      pixelPerDay: 30,
      viewMode: "week",
      showMilestones: true,
      colors: {
        completed: "#28a745",
        inProgress: "#0366d6",
        notStarted: "#6f42c1",
        milestone: "#f66a0a",
      },
      ...options,
    };

    this.tasks = [];
    this.milestones = [];
    this.dateRange = { start: null, end: null };

    this.initializeEventListeners();
    this.loadSampleData();
    this.render();
  }

  // 初始化事件监听
  initializeEventListeners() {
    const viewModeSelect = document.getElementById("viewMode");
    const showMilestonesCheckbox = document.getElementById("showMilestones");
    const zoomInBtn = document.getElementById("zoomIn");
    const zoomOutBtn = document.getElementById("zoomOut");
    const resetViewBtn = document.getElementById("resetView");

    if (viewModeSelect) {
      viewModeSelect.addEventListener("change", (e) => {
        this.config.viewMode = e.target.value;
        this.updatePixelPerDay();
        this.render();
      });
    }

    if (showMilestonesCheckbox) {
      showMilestonesCheckbox.addEventListener("change", (e) => {
        this.config.showMilestones = e.target.checked;
        this.render();
      });
    }

    if (zoomInBtn) {
      zoomInBtn.addEventListener("click", () => {
        this.config.pixelPerDay *= 1.2;
        this.render();
      });
    }

    if (zoomOutBtn) {
      zoomOutBtn.addEventListener("click", () => {
        this.config.pixelPerDay /= 1.2;
        this.render();
      });
    }

    if (resetViewBtn) {
      resetViewBtn.addEventListener("click", () => {
        this.updatePixelPerDay();
        this.render();
      });
    }
  }

  // 根据视图模式更新像素比例
  updatePixelPerDay() {
    const modes = {
      day: 30,
      week: 20,
      month: 8,
    };
    this.config.pixelPerDay = modes[this.config.viewMode] || 20;
  }

  // 加载示例数据
  loadSampleData() {
    // 默认加载内置的示例数据
    this.loadBuiltInSampleData();
  }

  // 加载内置示例数据
  loadBuiltInSampleData() {
    const today = new Date();
    const addDays = (date, days) =>
      new Date(date.getTime() + days * 24 * 60 * 60 * 1000);

    this.tasks = [
      // 项目启动阶段
      {
        id: "task1",
        name: "项目启动",
        startDate: addDays(today, -30),
        endDate: addDays(today, -25),
        progress: 100,
        status: "completed",
        dependencies: [],
        assignee: "项目经理",
        priority: "high",
        timePoints: [
          {
            id: "p1",
            date: addDays(today, -30),
            name: "项目启动会议",
            type: "start",
          },
          {
            id: "p2",
            date: addDays(today, -28),
            name: "团队组建",
            type: "checkpoint",
          },
          {
            id: "p3",
            date: addDays(today, -26),
            name: "项目章程确认",
            type: "checkpoint",
          },
          { id: "p4", date: addDays(today, -25), name: "启动完成", type: "end" },
        ],
      },
      {
        id: "task2",
        name: "需求调研",
        startDate: addDays(today, -28),
        endDate: addDays(today, -20),
        progress: 100,
        status: "completed",
        dependencies: ["task1"],
        assignee: "业务分析师",
        priority: "high",
        timePoints: [
          {
            id: "p5",
            date: addDays(today, -28),
            name: "用户访谈",
            type: "start",
          },
          {
            id: "p6",
            date: addDays(today, -25),
            name: "竞品分析",
            type: "checkpoint",
          },
          {
            id: "p7",
            date: addDays(today, -22),
            name: "需求整理",
            type: "checkpoint",
          },
          { id: "p8", date: addDays(today, -20), name: "调研报告", type: "end" },
        ],
      },
      {
        id: "task3",
        name: "需求分析",
        startDate: addDays(today, -22),
        endDate: addDays(today, -15),
        progress: 100,
        status: "completed",
        dependencies: ["task2"],
        assignee: "系统分析师",
        priority: "high",
        timePoints: [
          {
            id: "p9",
            date: addDays(today, -22),
            name: "功能需求分析",
            type: "start",
          },
          {
            id: "p10",
            date: addDays(today, -19),
            name: "非功能需求分析",
            type: "checkpoint",
          },
          {
            id: "p11",
            date: addDays(today, -17),
            name: "需求评审",
            type: "checkpoint",
          },
          { id: "p12", date: addDays(today, -15), name: "需求确认", type: "end" },
        ],
      },
      {
        id: "task4",
        name: "系统设计",
        startDate: addDays(today, -18),
        endDate: addDays(today, -10),
        progress: 80,
        status: "inProgress",
        dependencies: ["task3"],
        assignee: "架构师",
        priority: "high",
        timePoints: [
          {
            id: "p13",
            date: addDays(today, -18),
            name: "技术选型",
            type: "start",
          },
          {
            id: "p14",
            date: addDays(today, -15),
            name: "架构设计",
            type: "checkpoint",
          },
          {
            id: "p15",
            date: addDays(today, -12),
            name: "架构评审",
            type: "checkpoint",
          },
          { id: "p16", date: addDays(today, -10), name: "架构确认", type: "end" },
        ],
      },
      {
        id: "task5",
        name: "前端开发",
        startDate: addDays(today, -12),
        endDate: addDays(today, 5),
        progress: 60,
        status: "inProgress",
        dependencies: ["task4"],
        assignee: "前端工程师",
        priority: "high",
        timePoints: [
          {
            id: "p17",
            date: addDays(today, -12),
            name: "环境搭建",
            type: "start",
          },
          {
            id: "p18",
            date: addDays(today, -8),
            name: "组件开发",
            type: "checkpoint",
          },
          {
            id: "p19",
            date: addDays(today, -4),
            name: "页面集成",
            type: "checkpoint",
          },
          { id: "p20", date: addDays(today, 5), name: "前端完成", type: "end" },
        ],
      },
      {
        id: "task6",
        name: "后端开发",
        startDate: addDays(today, -10),
        endDate: addDays(today, 8),
        progress: 40,
        status: "inProgress",
        dependencies: ["task4"],
        assignee: "后端工程师",
        priority: "high",
        timePoints: [
          {
            id: "p21",
            date: addDays(today, -10),
            name: "数据库设计",
            type: "start",
          },
          {
            id: "p22",
            date: addDays(today, -6),
            name: "API开发",
            type: "checkpoint",
          },
          {
            id: "p23",
            date: addDays(today, -2),
            name: "业务逻辑",
            type: "checkpoint",
          },
          { id: "p24", date: addDays(today, 8), name: "后端完成", type: "end" },
        ],
      },
      {
        id: "task7",
        name: "测试部署",
        startDate: addDays(today, 5),
        endDate: addDays(today, 15),
        progress: 0,
        status: "notStarted",
        dependencies: ["task5", "task6"],
        assignee: "测试工程师",
        priority: "medium",
        timePoints: [
          {
            id: "p25",
            date: addDays(today, 5),
            name: "测试计划",
            type: "start",
          },
          {
            id: "p26",
            date: addDays(today, 8),
            name: "功能测试",
            type: "checkpoint",
          },
          {
            id: "p27",
            date: addDays(today, 12),
            name: "部署上线",
            type: "checkpoint",
          },
          { id: "p28", date: addDays(today, 15), name: "项目完成", type: "end" },
        ],
      },
    ];

    this.milestones = this.tasks.filter(task => task.isMilestone);
    this.calculateDateRange();
    this.render();
  }

  // 从JSON文件加载数据
  async loadFromJSONFile(filePath) {
    try {
      const response = await fetch(filePath);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const data = await response.json();
      
      // 转换日期字符串为Date对象
      const tasks = data.tasks.map(task => ({
        ...task,
        startDate: new Date(task.startDate),
        endDate: new Date(task.endDate),
        timePoints: task.timePoints ? task.timePoints.map(point => ({
          ...point,
          date: new Date(point.date)
        })) : []
      }));

      this.tasks = tasks;
      this.milestones = tasks.filter(task => task.isMilestone);
      this.calculateDateRange();
      this.render();
      
      return {
        success: true,
        metadata: data.metadata,
        project: data.project
      };
    } catch (error) {
      console.error('加载JSON文件失败:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  // 从JSON对象加载数据
  loadFromJSONObject(data) {
    try {
      // 转换日期字符串为Date对象
      const tasks = data.tasks.map(task => ({
        ...task,
        startDate: new Date(task.startDate),
        endDate: new Date(task.endDate),
        timePoints: task.timePoints ? task.timePoints.map(point => ({
          ...point,
          date: new Date(point.date)
        })) : []
      }));

      this.tasks = tasks;
      this.milestones = tasks.filter(task => task.isMilestone);
      this.calculateDateRange();
      this.render();
      
      return {
        success: true,
        metadata: data.metadata,
        project: data.project
      };
    } catch (error) {
      console.error('加载JSON对象失败:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  // 计算日期范围
  calculateDateRange() {
    if (this.tasks.length === 0) return;

    let minDate = this.tasks[0].startDate;
    let maxDate = this.tasks[0].endDate;

    this.tasks.forEach((task) => {
      if (task.startDate < minDate) minDate = task.startDate;
      if (task.endDate > maxDate) maxDate = task.endDate;

      // 检查任务的时间点
      if (task.timePoints) {
        task.timePoints.forEach((point) => {
          if (point.date < minDate) minDate = point.date;
          if (point.date > maxDate) maxDate = point.date;
        });
      }
    });

    // 添加缓冲区
    const buffer = 7 * 24 * 60 * 60 * 1000; // 7天
    this.dateRange = {
      start: new Date(minDate.getTime() - buffer),
      end: new Date(maxDate.getTime() + buffer),
    };
  }

  // 日期转换为X坐标 - 精确对齐到网格
  dateToX(date) {
    // 将日期规范化到午夜
    const normalizedDate = new Date(date);
    normalizedDate.setHours(0, 0, 0, 0);

    const normalizedStart = new Date(this.dateRange.start);
    normalizedStart.setHours(0, 0, 0, 0);

    const daysDiff = (normalizedDate - normalizedStart) / (24 * 60 * 60 * 1000);
    return this.config.leftPanelWidth + daysDiff * this.config.pixelPerDay;
  }

  // 生成时间刻度
  generateTimeScale() {
    const ticks = [];
    const current = new Date(this.dateRange.start);

    // 确保从整天开始
    current.setHours(0, 0, 0, 0);

    while (current <= this.dateRange.end) {
      ticks.push(new Date(current));
      current.setDate(current.getDate() + 1); // 每天都显示，便于定位
    }

    return ticks;
  }

  // 格式化日期标签
  formatDateLabel(date) {
    const options = {
      day: { month: "short", day: "numeric" },
      week: { month: "short", day: "numeric" },
      month: { year: "numeric", month: "short" },
    };

    return date.toLocaleDateString("zh-CN", options[this.config.viewMode]);
  }

  // 更新任务高度以容纳时间点
  updateTaskHeight() {
    // 如果显示时间点，需要更多空间
    const baseHeight = 30;
    const extraHeight = this.config.showMilestones ? 25 : 0;
    this.config.taskHeight = baseHeight;
    this.config.taskMargin = 5 + extraHeight;
  }

  // 渲染主方法
  render() {
    if (!this.svg) return;

    this.svg.innerHTML = `
            <defs>
                <marker id="arrowhead" markerWidth="10" markerHeight="7" 
                        refX="9" refY="3.5" orient="auto">
                    <polygon points="0 0, 10 3.5, 0 7" fill="#586069" />
                </marker>
            </defs>
        `;

    // 更新任务高度
    this.updateTaskHeight();

    // 如果没有任务，设置默认日期范围
    if (!this.dateRange.start || !this.dateRange.end) {
      const today = new Date();
      this.dateRange = {
        start: new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000), // 30天前
        end: new Date(today.getTime() + 30 * 24 * 60 * 60 * 1000),   // 30天后
      };
    }

    // 计算SVG尺寸
    const totalDays =
      (this.dateRange.end - this.dateRange.start) / (24 * 60 * 60 * 1000);
    this.config.width =
      this.config.leftPanelWidth + totalDays * this.config.pixelPerDay;
    this.config.height =
      this.config.headerHeight +
      Math.max(this.tasks.length, 1) * (this.config.taskHeight + this.config.taskMargin) +
      100;

    this.svg.setAttribute("width", this.config.width);
    this.svg.setAttribute("height", this.config.height);

    this.renderGrid();
    this.renderTimeAxis();
    this.renderTasks();
    this.renderDependencies();
  }

  // 渲染网格
  renderGrid() {
    const timeScale = this.generateTimeScale();

    // 垂直网格线
    timeScale.forEach((date) => {
      const x = this.dateToX(date);
      const line = document.createElementNS(
        "http://www.w3.org/2000/svg",
        "line"
      );
      line.setAttribute("x1", x);
      line.setAttribute("y1", this.config.headerHeight);
      line.setAttribute("x2", x);
      line.setAttribute("y2", this.config.height);
      line.setAttribute("class", "grid-line");
      this.svg.appendChild(line);
    });

    // 水平网格线
    for (let i = 0; i <= this.tasks.length; i++) {
      const y =
        this.config.headerHeight +
        i * (this.config.taskHeight + this.config.taskMargin);
      const line = document.createElementNS(
        "http://www.w3.org/2000/svg",
        "line"
      );
      line.setAttribute("x1", 0);
      line.setAttribute("y1", y);
      line.setAttribute("x2", this.config.width);
      line.setAttribute("y2", y);
      line.setAttribute("class", "grid-line");
      this.svg.appendChild(line);
    }
  }

  // 渲染时间轴
  renderTimeAxis() {
    const timeScale = this.generateTimeScale();

    // 只显示主要时间标签（根据视图模式决定）
    const labelInterval =
      this.config.viewMode === "day"
        ? 1
        : this.config.viewMode === "week"
        ? 7
        : 30;

    timeScale.forEach((date, index) => {
      const x = this.dateToX(date);

      // 所有日期都画刻度线（用于对齐时间点）
      const tick = document.createElementNS(
        "http://www.w3.org/2000/svg",
        "line"
      );
      tick.setAttribute("x1", x);
      tick.setAttribute("y1", this.config.headerHeight - 5);
      tick.setAttribute("x2", x);
      tick.setAttribute("y2", this.config.headerHeight);
      tick.setAttribute("class", "time-tick");
      this.svg.appendChild(tick);

      // 只在指定间隔显示时间标签
      if (index % labelInterval === 0) {
        const label = document.createElementNS(
          "http://www.w3.org/2000/svg",
          "text"
        );
        label.setAttribute("x", x);
        label.setAttribute("y", this.config.headerHeight - 15);
        label.setAttribute("class", "time-label");
        label.textContent = this.formatDateLabel(date);
        this.svg.appendChild(label);
      }
    });

    // 时间轴基线
    const baseline = document.createElementNS(
      "http://www.w3.org/2000/svg",
      "line"
    );
    baseline.setAttribute("x1", this.config.leftPanelWidth);
    baseline.setAttribute("y1", this.config.headerHeight);
    baseline.setAttribute("x2", this.config.width);
    baseline.setAttribute("y2", this.config.headerHeight);
    baseline.setAttribute("class", "time-axis-line");
    this.svg.appendChild(baseline);
  }

  // 渲染任务
  renderTasks() {
    this.tasks.forEach((task, index) => {
      const y =
        this.config.headerHeight +
        index * (this.config.taskHeight + this.config.taskMargin) +
        this.config.taskMargin;
      const x = this.dateToX(task.startDate);
      const width = this.dateToX(task.endDate) - x;

      // 任务标签
      const label = document.createElementNS(
        "http://www.w3.org/2000/svg",
        "text"
      );
      label.setAttribute("x", 10);
      label.setAttribute("y", y + this.config.taskHeight / 2 + 5);
      label.setAttribute("class", "task-label");
      
      // 为里程碑添加特殊标识
      const taskName = task.isMilestone ? `🎯 ${task.name}` : task.name;
      label.textContent = taskName;
      this.svg.appendChild(label);

      // 任务条背景
      const taskBar = document.createElementNS(
        "http://www.w3.org/2000/svg",
        "rect"
      );
      taskBar.setAttribute("x", x);
      taskBar.setAttribute("y", y);
      taskBar.setAttribute("width", width);
      taskBar.setAttribute("height", this.config.taskHeight);
      
      // 里程碑使用特殊颜色
      if (task.isMilestone) {
        taskBar.setAttribute("fill", this.config.colors.milestone);
        taskBar.setAttribute("stroke", "#f66a0a");
        taskBar.setAttribute("stroke-width", "2");
      } else {
        taskBar.setAttribute("fill", this.config.colors[task.status]);
      }
      
      taskBar.setAttribute("rx", 4);
      taskBar.setAttribute("class", "task-bar");
      this.svg.appendChild(taskBar);

      // 任务进度
      if (task.progress > 0) {
        const progressBar = document.createElementNS(
          "http://www.w3.org/2000/svg",
          "rect"
        );
        progressBar.setAttribute("x", x);
        progressBar.setAttribute("y", y);
        progressBar.setAttribute("width", width * (task.progress / 100));
        progressBar.setAttribute("height", this.config.taskHeight);
        progressBar.setAttribute("fill", this.config.colors.completed);
        progressBar.setAttribute("rx", 4);
        this.svg.appendChild(progressBar);
      }

      // 渲染任务时间点和连线（如果启用）
      if (this.config.showMilestones) {
        this.renderTaskTimePoints(task, index);
      }

      // 添加鼠标事件
      this.addTaskEvents(taskBar, task);
    });
  }

  // 渲染任务时间点和连线
  renderTaskTimePoints(task, taskIndex) {
    if (!task.timePoints || task.timePoints.length === 0) return;

    const taskY =
      this.config.headerHeight +
      taskIndex * (this.config.taskHeight + this.config.taskMargin) +
      this.config.taskMargin;
    const pointY = taskY + this.config.taskHeight + 15; // 时间点在任务条下方

    // 按日期排序时间点
    const sortedPoints = [...task.timePoints].sort((a, b) => a.date - b.date);

    // 绘制连接线
    if (sortedPoints.length > 1) {
      const pathData = [];
      sortedPoints.forEach((point, index) => {
        const x = this.dateToX(point.date);
        if (index === 0) {
          pathData.push(`M ${x} ${pointY}`);
        } else {
          pathData.push(`L ${x} ${pointY}`);
        }
      });

      const path = document.createElementNS(
        "http://www.w3.org/2000/svg",
        "path"
      );
      path.setAttribute("d", pathData.join(" "));
      path.setAttribute("class", "time-line");
      this.svg.appendChild(path);
    }

    // 绘制时间点
    sortedPoints.forEach((point) => {
      const x = this.dateToX(point.date);

      // 时间点圆圈
      const circle = document.createElementNS(
        "http://www.w3.org/2000/svg",
        "circle"
      );
      circle.setAttribute("cx", x);
      circle.setAttribute("cy", pointY);
      circle.setAttribute("r", 5);
      circle.setAttribute("class", `time-point ${point.type}`);
      this.svg.appendChild(circle);

      // 添加时间点事件
      this.addTimePointEvents(circle, point, task);
    });
  }

  // 渲染依赖关系
  renderDependencies() {
    this.tasks.forEach((task, index) => {
      if (task.dependencies && task.dependencies.length > 0) {
        task.dependencies.forEach((depId) => {
          const depTask = this.tasks.find((t) => t.id === depId);
          if (depTask) {
            const depIndex = this.tasks.findIndex((t) => t.id === depId);
            this.drawDependencyLine(depIndex, index);
          }
        });
      }
    });
  }

  // 绘制依赖线
  drawDependencyLine(fromIndex, toIndex) {
    const fromTask = this.tasks[fromIndex];
    const toTask = this.tasks[toIndex];

    const fromY =
      this.config.headerHeight +
      fromIndex * (this.config.taskHeight + this.config.taskMargin) +
      this.config.taskMargin +
      this.config.taskHeight / 2;
    const toY =
      this.config.headerHeight +
      toIndex * (this.config.taskHeight + this.config.taskMargin) +
      this.config.taskMargin +
      this.config.taskHeight / 2;

    const fromX = this.dateToX(fromTask.endDate);
    const toX = this.dateToX(toTask.startDate);

    const path = document.createElementNS("http://www.w3.org/2000/svg", "path");
    const midX = (fromX + toX) / 2;
    const d = `M ${fromX} ${fromY} L ${midX} ${fromY} L ${midX} ${toY} L ${toX} ${toY}`;

    path.setAttribute("d", d);
    path.setAttribute("class", "dependency-line");
    this.svg.appendChild(path);
  }

  // 添加任务事件
  addTaskEvents(element, task) {
    element.addEventListener("mouseenter", (e) => {
      const assignee = task.assignee || "未分配";
      const priority = task.priority || "普通";
      const milestoneText = task.isMilestone ? "<br>🎯 里程碑任务" : "";
      
      this.showTooltip(
        e,
        `
                <strong>${task.name}</strong>${milestoneText}<br>
                负责人: ${assignee}<br>
                优先级: ${priority}<br>
                开始: ${task.startDate.toLocaleDateString("zh-CN")}<br>
                结束: ${task.endDate.toLocaleDateString("zh-CN")}<br>
                进度: ${task.progress}%<br>
                状态: ${this.getStatusLabel(task.status)}
            `
      );
    });

    element.addEventListener("mouseleave", () => {
      this.hideTooltip();
    });

    element.addEventListener("mousemove", (e) => {
      this.updateTooltipPosition(e);
    });
  }

  // 添加时间点事件
  addTimePointEvents(element, timePoint, task) {
    element.addEventListener("mouseenter", (e) => {
      this.showTooltip(
        e,
        `
                <strong>${task.name}</strong><br>
                <strong>${timePoint.name}</strong><br>
                日期: ${timePoint.date.toLocaleDateString("zh-CN")}<br>
                类型: ${this.getTimePointTypeLabel(timePoint.type)}
            `
      );
    });

    element.addEventListener("mouseleave", () => {
      this.hideTooltip();
    });

    element.addEventListener("mousemove", (e) => {
      this.updateTooltipPosition(e);
    });

    element.addEventListener("click", () => {
      console.log("时间点被点击:", timePoint);
    });
  }

  // 获取时间点类型标签
  getTimePointTypeLabel(type) {
    const labels = {
      start: "开始节点",
      checkpoint: "检查点",
      end: "结束节点",
      milestone: "里程碑",
    };
    return labels[type] || type;
  }

  // 获取状态标签
  getStatusLabel(status) {
    const labels = {
      completed: "已完成",
      inProgress: "进行中",
      notStarted: "未开始",
    };
    return labels[status] || status;
  }

  // 显示提示框
  showTooltip(event, content) {
    if (!this.tooltip) return;
    this.tooltip.innerHTML = content;
    this.tooltip.style.display = "block";
    this.updateTooltipPosition(event);
  }

  // 隐藏提示框
  hideTooltip() {
    if (!this.tooltip) return;
    this.tooltip.style.display = "none";
  }

  // 更新提示框位置
  updateTooltipPosition(event) {
    if (!this.tooltip || !this.container) return;
    const rect = this.container.getBoundingClientRect();
    this.tooltip.style.left = event.clientX - rect.left + 10 + "px";
    this.tooltip.style.top = event.clientY - rect.top - 10 + "px";
  }

  // 公共API：设置数据
  setData(tasks, milestones = []) {
    this.tasks = tasks;
    this.milestones = milestones;
    this.calculateDateRange();
    this.render();
  }

  // 公共API：添加任务
  addTask(task) {
    this.tasks.push(task);
    this.calculateDateRange();
    this.render();
  }

  // 公共API：更新任务
  updateTask(taskId, updates) {
    const taskIndex = this.tasks.findIndex((t) => t.id === taskId);
    if (taskIndex !== -1) {
      this.tasks[taskIndex] = { ...this.tasks[taskIndex], ...updates };
      this.calculateDateRange();
      this.render();
    }
  }

  // 公共API：删除任务
  removeTask(taskId) {
    this.tasks = this.tasks.filter((t) => t.id !== taskId);
    this.calculateDateRange();
    this.render();
  }
}

// 如果在浏览器环境中，暴露到全局
if (typeof window !== "undefined") {
  window.GanttChart = GanttChart;
}

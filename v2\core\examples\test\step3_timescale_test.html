<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Step3: TimeScale 时间轴计算引擎测试</title>
    <link rel="stylesheet" href="../../src/styles/gantt.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }

        .header h1 {
            margin: 0;
            font-size: 24px;
        }

        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
        }

        .test-controls {
            padding: 20px;
            border-bottom: 1px solid #eee;
            background: #f9f9f9;
        }

        .control-group {
            display: inline-block;
            margin-right: 20px;
            margin-bottom: 10px;
        }

        .control-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #333;
        }

        .control-group select,
        .control-group input,
        .control-group button {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        .control-group button {
            background: #007bff;
            color: white;
            border: none;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .control-group button:hover {
            background: #0056b3;
        }

        .stats-panel {
            padding: 20px;
            background: #f8f9fa;
            border-bottom: 1px solid #eee;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .stat-item {
            background: white;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid #007bff;
        }

        .stat-label {
            font-size: 12px;
            color: #666;
            text-transform: uppercase;
            margin-bottom: 5px;
        }

        .stat-value {
            font-size: 18px;
            font-weight: 600;
            color: #333;
        }

        .gantt-container {
            height: 600px;
            position: relative;
        }

        .test-log {
            padding: 20px;
            background: #f8f9fa;
            border-top: 1px solid #eee;
            max-height: 200px;
            overflow-y: auto;
        }

        .test-log h3 {
            margin: 0 0 10px 0;
            color: #333;
        }

        .log-entry {
            padding: 5px 0;
            border-bottom: 1px solid #eee;
            font-family: monospace;
            font-size: 12px;
        }

        .log-entry.success { color: #28a745; }
        .log-entry.warning { color: #ffc107; }
        .log-entry.error { color: #dc3545; }
        .log-entry.info { color: #17a2b8; }

        .performance-indicator {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 10px;
            border-radius: 6px;
            font-family: monospace;
            font-size: 12px;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Step3: TimeScale 时间轴计算引擎测试</h1>
            <p>测试高精度时间轴计算、智能网格系统、可视区域优化等核心功能</p>
        </div>

        <div class="test-controls">
            <div class="control-group">
                <label>视图模式</label>
                <select id="viewModeSelect">
                    <option value="day">日视图</option>
                    <option value="week">周视图</option>
                    <option value="month">月视图</option>
                    <option value="quarter">季度视图</option>
                </select>
            </div>

            <div class="control-group">
                <label>缩放级别</label>
                <input type="range" id="zoomSlider" min="0.3" max="3.0" step="0.1" value="1.0">
                <span id="zoomValue">100%</span>
            </div>

            <div class="control-group">
                <label>动画切换</label>
                <input type="checkbox" id="animateToggle" checked>
            </div>

            <div class="control-group">
                <button id="testPerformanceBtn">性能测试</button>
                <button id="testAccuracyBtn">精度测试</button>
                <button id="resetBtn">重置</button>
            </div>
        </div>

        <div class="stats-panel">
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-label">总刻度数</div>
                    <div class="stat-value" id="totalScales">-</div>
                </div>
                <div class="stat-item">
                    <div class="stat-label">可见刻度数</div>
                    <div class="stat-value" id="visibleScales">-</div>
                </div>
                <div class="stat-item">
                    <div class="stat-label">时间轴宽度</div>
                    <div class="stat-value" id="totalWidth">-</div>
                </div>
                <div class="stat-item">
                    <div class="stat-label">渲染时间</div>
                    <div class="stat-value" id="renderTime">-</div>
                </div>
                <div class="stat-item">
                    <div class="stat-label">内存使用</div>
                    <div class="stat-value" id="memoryUsage">-</div>
                </div>
                <div class="stat-item">
                    <div class="stat-label">FPS</div>
                    <div class="stat-value" id="fps">-</div>
                </div>
            </div>
        </div>

        <div class="gantt-container" id="ganttContainer"></div>

        <div class="test-log">
            <h3>测试日志</h3>
            <div id="logContainer"></div>
        </div>
    </div>

    <div class="performance-indicator" id="perfIndicator">
        <div>FPS: <span id="fpsCounter">60</span></div>
        <div>Memory: <span id="memoryCounter">0MB</span></div>
        <div>Scales: <span id="scalesCounter">0</span></div>
    </div>

    <script type="module">
        import GanttChart from '../../src/GanttChart.js';
        import DateUtils from '../../src/utils/DateUtils.js';

        // 测试数据生成
        function generateTestData(count = 50) {
            const tasks = [];
            const startDate = new Date('2024-01-01');
            
            for (let i = 0; i < count; i++) {
                const taskStart = DateUtils.addDays(startDate, Math.random() * 365);
                const duration = Math.random() * 30 + 5; // 5-35天
                const taskEnd = DateUtils.addDays(taskStart, duration);
                
                tasks.push({
                    id: `task-${i}`,
                    name: `任务 ${i + 1}`,
                    startDate: taskStart,
                    endDate: taskEnd,
                    progress: Math.random(),
                    status: ['pending', 'in-progress', 'completed'][Math.floor(Math.random() * 3)],
                    priority: ['low', 'medium', 'high'][Math.floor(Math.random() * 3)]
                });
            }
            
            return tasks;
        }

        // 初始化甘特图
        let ganttInstance = null;
        let performanceMonitor = null;

        function initializeGantt() {
            const container = document.getElementById('ganttContainer');
            container.innerHTML = '';
            
            const testData = generateTestData(100);
            
            ganttInstance = new GanttChart('ganttContainer', {
                data: testData,
                viewMode: 'day',
                pixelsPerDay: 30,
                taskList: {
                    columns: [
                        { key: 'name', label: '任务名称', width: 200 },
                        { key: 'startDate', label: '开始日期', width: 100 },
                        { key: 'endDate', label: '结束日期', width: 100 },
                        { key: 'progress', label: '进度', width: 80 }
                    ]
                }
            });

            // 监听事件
            ganttInstance.on('ready', () => {
                log('甘特图初始化完成', 'success');
                updateStats();
                startPerformanceMonitoring();
            });

            ganttInstance.on('viewChange', (data) => {
                log(`视图模式切换: ${data.from} -> ${data.to}`, 'info');
                updateStats();
            });

            ganttInstance.on('zoomChange', (zoomLevel) => {
                log(`缩放级别变更: ${Math.round(zoomLevel * 100)}%`, 'info');
                updateStats();
            });
        }

        // 日志记录
        function log(message, type = 'info') {
            const logContainer = document.getElementById('logContainer');
            const entry = document.createElement('div');
            entry.className = `log-entry ${type}`;
            entry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            logContainer.appendChild(entry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        // 更新统计信息
        function updateStats() {
            if (!ganttInstance || !ganttInstance.timeScale) return;

            const stats = ganttInstance.timeScale.getPerformanceStats();
            const visibleRange = ganttInstance.timeScale.getDetailedVisibleRange();

            document.getElementById('totalScales').textContent = stats.totalScales;
            document.getElementById('visibleScales').textContent = visibleRange.performance.visibleScales;
            document.getElementById('totalWidth').textContent = `${Math.round(stats.totalWidth)}px`;
            
            // 更新性能指示器
            document.getElementById('scalesCounter').textContent = stats.totalScales;
        }

        // 性能监控
        function startPerformanceMonitoring() {
            let frameCount = 0;
            let lastTime = performance.now();

            function updatePerformance() {
                frameCount++;
                const currentTime = performance.now();
                
                if (currentTime - lastTime >= 1000) {
                    const fps = Math.round(frameCount * 1000 / (currentTime - lastTime));
                    document.getElementById('fpsCounter').textContent = fps;
                    document.getElementById('fps').textContent = fps;
                    
                    frameCount = 0;
                    lastTime = currentTime;
                }

                // 内存使用（如果支持）
                if (performance.memory) {
                    const memoryMB = Math.round(performance.memory.usedJSHeapSize / 1024 / 1024);
                    document.getElementById('memoryCounter').textContent = `${memoryMB}MB`;
                    document.getElementById('memoryUsage').textContent = `${memoryMB}MB`;
                }

                requestAnimationFrame(updatePerformance);
            }

            updatePerformance();
        }

        // 事件绑定
        document.getElementById('viewModeSelect').addEventListener('change', (e) => {
            const animate = document.getElementById('animateToggle').checked;
            ganttInstance.changeViewMode(e.target.value, { animate, duration: 500 });
        });

        document.getElementById('zoomSlider').addEventListener('input', (e) => {
            const zoomLevel = parseFloat(e.target.value);
            document.getElementById('zoomValue').textContent = `${Math.round(zoomLevel * 100)}%`;
            ganttInstance.setZoomLevel(zoomLevel);
        });

        document.getElementById('testPerformanceBtn').addEventListener('click', () => {
            log('开始性能测试...', 'info');
            
            const startTime = performance.now();
            const testData = generateTestData(1000); // 大数据集
            ganttInstance.setData(testData);
            
            setTimeout(() => {
                const endTime = performance.now();
                const renderTime = Math.round(endTime - startTime);
                document.getElementById('renderTime').textContent = `${renderTime}ms`;
                log(`性能测试完成: 1000个任务渲染耗时 ${renderTime}ms`, 'success');
            }, 100);
        });

        document.getElementById('testAccuracyBtn').addEventListener('click', () => {
            log('开始精度测试...', 'info');
            
            if (!ganttInstance.timeScale) {
                log('TimeScale未初始化', 'error');
                return;
            }

            // 测试坐标转换精度
            const testDate = new Date('2024-06-15T12:30:00');
            const x = ganttInstance.timeScale.dateToX(testDate, { subPixel: true });
            const convertedDate = ganttInstance.timeScale.xToDate(x, { precise: true });
            
            const timeDiff = Math.abs(testDate.getTime() - convertedDate.getTime());
            const isAccurate = timeDiff < 1000; // 1秒误差内
            
            log(`精度测试: 时间差 ${timeDiff}ms ${isAccurate ? '✓' : '✗'}`, isAccurate ? 'success' : 'error');
        });

        document.getElementById('resetBtn').addEventListener('click', () => {
            log('重置甘特图...', 'info');
            initializeGantt();
        });

        // 初始化
        document.addEventListener('DOMContentLoaded', () => {
            log('页面加载完成，初始化甘特图...', 'info');
            initializeGantt();
        });
    </script>
</body>
</html>

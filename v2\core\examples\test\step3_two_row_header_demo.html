<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Step3: 两行时间轴头部演示</title>
    <link rel="stylesheet" href="../../src/styles/gantt.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }

        .header h1 {
            margin: 0;
            font-size: 24px;
        }

        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
        }

        .demo-controls {
            padding: 20px;
            border-bottom: 1px solid #eee;
            background: #f9f9f9;
            display: flex;
            gap: 20px;
            align-items: center;
            flex-wrap: wrap;
        }

        .control-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .control-group label {
            font-weight: 500;
            color: #333;
            font-size: 14px;
        }

        .control-group select,
        .control-group button {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            background: white;
        }

        .control-group button {
            background: #6366f1;
            color: white;
            border: none;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .control-group button:hover {
            background: #5b21b6;
        }

        .gantt-container {
            height: 500px;
            position: relative;
        }

        .explanation {
            padding: 20px;
            background: #f8f9fa;
            border-top: 1px solid #eee;
        }

        .explanation h3 {
            margin: 0 0 15px 0;
            color: #333;
        }

        .explanation-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 15px;
        }

        .explanation-item {
            background: white;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid #6366f1;
        }

        .explanation-item h4 {
            margin: 0 0 10px 0;
            color: #6366f1;
            font-size: 16px;
        }

        .explanation-item p {
            margin: 0;
            color: #666;
            line-height: 1.5;
        }

        .view-mode-demo {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .view-mode-card {
            background: white;
            padding: 15px;
            border-radius: 6px;
            border: 2px solid #e5e7eb;
            text-align: center;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .view-mode-card:hover {
            border-color: #6366f1;
            transform: translateY(-2px);
        }

        .view-mode-card.active {
            border-color: #6366f1;
            background: #f0f9ff;
        }

        .view-mode-card h4 {
            margin: 0 0 10px 0;
            color: #333;
        }

        .view-mode-card p {
            margin: 0;
            color: #666;
            font-size: 12px;
        }

        /* 增强头部显示 */
        .gantt-table-header {
            border: 3px solid #6366f1 !important;
            background: rgba(99, 102, 241, 0.05) !important;
        }

        .gantt-timeline-header {
            border: 3px solid #6366f1 !important;
        }

        .timeline-scales-top-row {
            background: linear-gradient(135deg, rgba(99, 102, 241, 0.1) 0%, rgba(139, 92, 246, 0.1) 100%) !important;
        }

        .timeline-scale-group.top-row {
            border-right: 2px solid #6366f1 !important;
        }

        .scale-group-label {
            color: #6366f1 !important;
            font-weight: 700 !important;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
        }

        .timeline-scales-bottom-row {
            background: rgba(99, 102, 241, 0.05) !important;
        }

        .timeline-scale.bottom-row.major {
            background: rgba(99, 102, 241, 0.1) !important;
            border-right: 2px solid #6366f1 !important;
        }

        .timeline-scale.bottom-row .scale-label {
            color: #6366f1 !important;
            font-weight: 600 !important;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 两行时间轴头部演示</h1>
            <p>展示不同视图模式下的两行时间轴头部布局效果</p>
        </div>

        <div class="demo-controls">
            <div class="control-group">
                <label>当前视图模式</label>
                <select id="viewModeSelect">
                    <option value="day">日视图</option>
                    <option value="week">周视图</option>
                    <option value="month">月视图</option>
                    <option value="quarter">季度视图</option>
                </select>
            </div>

            <div class="control-group">
                <button id="autoSwitchBtn">自动切换演示</button>
                <button id="resetBtn">重置视图</button>
            </div>
        </div>

        <div class="gantt-container" id="ganttContainer"></div>

        <div class="explanation">
            <h3>两行时间轴头部说明</h3>
            
            <div class="view-mode-demo">
                <div class="view-mode-card" data-mode="day">
                    <h4>日视图</h4>
                    <p>第一行：月份和年份<br>第二行：具体日期</p>
                </div>
                <div class="view-mode-card" data-mode="week">
                    <h4>周视图</h4>
                    <p>第一行：月份和年份<br>第二行：周开始日期</p>
                </div>
                <div class="view-mode-card" data-mode="month">
                    <h4>月视图</h4>
                    <p>第一行：年份<br>第二行：月份</p>
                </div>
                <div class="view-mode-card" data-mode="quarter">
                    <h4>季度视图</h4>
                    <p>第一行：年份<br>第二行：季度</p>
                </div>
            </div>

            <div class="explanation-grid">
                <div class="explanation-item">
                    <h4>设计理念</h4>
                    <p>采用两行布局提供更清晰的时间层次结构，第一行显示较大的时间单位（年份、月份），第二行显示具体的时间刻度，便于用户快速定位和理解时间关系。</p>
                </div>
                <div class="explanation-item">
                    <h4>视觉层次</h4>
                    <p>顶部行使用更大的字体和更明显的分隔线，底部行使用较小的字体显示详细信息。主要刻度和次要刻度通过颜色和边框粗细进行区分。</p>
                </div>
                <div class="explanation-item">
                    <h4>交互体验</h4>
                    <p>支持悬停效果，滚动时动态更新可见区域的刻度，防抖机制确保流畅的性能表现，响应式设计适配不同屏幕尺寸。</p>
                </div>
            </div>
        </div>
    </div>

    <script type="module">
        import GanttChart from '../../src/GanttChart.js';
        import DateUtils from '../../src/utils/DateUtils.js';

        // 生成演示数据
        function generateDemoData() {
            const tasks = [];
            const startDate = new Date('2024-01-01');
            
            for (let i = 0; i < 20; i++) {
                const taskStart = DateUtils.addDays(startDate, i * 15);
                const duration = 10 + Math.random() * 20;
                const taskEnd = DateUtils.addDays(taskStart, duration);
                
                tasks.push({
                    id: `demo-task-${i}`,
                    name: `演示任务 ${i + 1}`,
                    startDate: taskStart,
                    endDate: taskEnd,
                    progress: Math.random(),
                    status: ['pending', 'in-progress', 'completed'][Math.floor(Math.random() * 3)]
                });
            }
            
            return tasks;
        }

        // 初始化甘特图
        let ganttInstance = null;

        function initializeGantt() {
            const container = document.getElementById('ganttContainer');
            container.innerHTML = '';
            
            const demoData = generateDemoData();
            
            ganttInstance = new GanttChart('ganttContainer', {
                data: demoData,
                viewMode: 'day',
                pixelsPerDay: 40,
                taskList: {
                    columns: [
                        { key: 'name', label: '任务名称', width: 200 },
                        { key: 'startDate', label: '开始', width: 80 },
                        { key: 'endDate', label: '结束', width: 80 }
                    ]
                }
            });

            ganttInstance.on('ready', () => {
                console.log('甘特图初始化完成');
                updateActiveCard('day');
            });

            ganttInstance.on('viewChange', (data) => {
                updateActiveCard(data.to);
            });
        }

        // 更新活动卡片
        function updateActiveCard(viewMode) {
            document.querySelectorAll('.view-mode-card').forEach(card => {
                card.classList.toggle('active', card.dataset.mode === viewMode);
            });
        }

        // 事件绑定
        document.getElementById('viewModeSelect').addEventListener('change', (e) => {
            ganttInstance.changeViewMode(e.target.value);
        });

        document.querySelectorAll('.view-mode-card').forEach(card => {
            card.addEventListener('click', () => {
                const mode = card.dataset.mode;
                document.getElementById('viewModeSelect').value = mode;
                ganttInstance.changeViewMode(mode);
            });
        });

        document.getElementById('autoSwitchBtn').addEventListener('click', () => {
            const modes = ['day', 'week', 'month', 'quarter'];
            let currentIndex = 0;
            
            const switchMode = () => {
                ganttInstance.changeViewMode(modes[currentIndex]);
                document.getElementById('viewModeSelect').value = modes[currentIndex];
                currentIndex = (currentIndex + 1) % modes.length;
                
                if (currentIndex !== 0) {
                    setTimeout(switchMode, 2000);
                }
            };
            
            switchMode();
        });

        document.getElementById('resetBtn').addEventListener('click', () => {
            ganttInstance.changeViewMode('day');
            document.getElementById('viewModeSelect').value = 'day';
        });

        // 初始化
        document.addEventListener('DOMContentLoaded', () => {
            console.log('页面加载完成，初始化甘特图...');
            initializeGantt();
        });
    </script>
</body>
</html>

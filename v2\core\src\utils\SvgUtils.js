/**
 * SVG 操作工具类
 * 提供 SVG 元素创建、操作和管理的工具函数
 */
class SvgUtils {
  /**
   * SVG 命名空间
   */
  static SVG_NS = "http://www.w3.org/2000/svg";

  /**
   * 预定义的 SVG 图标路径
   */
  static ICONS = {
    milestone: {
      diamond: "M 0,-8 L 8,0 L 0,8 L -8,0 Z",
      circle: "M 0,0 m -6,0 a 6,6 0 1,0 12,0 a 6,6 0 1,0 -12,0",
      triangle: "M 0,-8 L 8,8 L -8,8 Z",
      star: "M 0,-8 L 2,-2 L 8,-2 L 3,2 L 5,8 L 0,4 L -5,8 L -3,2 L -8,-2 L -2,-2 Z",
    },
    task: {
      arrow: "M 0,0 L 8,4 L 0,8 L 2,4 Z",
      flag: "M 0,0 L 12,0 L 10,4 L 12,8 L 0,8 Z",
    },
  };

  /**
   * 创建 SVG 元素
   * @param {string} tagName - 标签名
   * @param {object} attributes - 属性对象
   * @param {string} textContent - 文本内容
   * @returns {SVGElement} SVG 元素
   */
  static createElement(tagName, attributes = {}, textContent = "") {
    const element = document.createElementNS(SvgUtils.SVG_NS, tagName);

    // 设置属性
    Object.entries(attributes).forEach(([key, value]) => {
      if (value !== null && value !== undefined) {
        element.setAttribute(key, value);
      }
    });

    // 设置文本内容
    if (textContent) {
      element.textContent = textContent;
    }

    return element;
  }

  /**
   * 创建 SVG 容器
   * @param {number} width - 宽度
   * @param {number} height - 高度
   * @param {object} options - 选项
   * @returns {SVGElement} SVG 容器
   */
  static createSvg(width = "100%", height = "100%", options = {}) {
    const svg = SvgUtils.createElement("svg", {
      width,
      height,
      viewBox: options.viewBox || null,
      preserveAspectRatio: options.preserveAspectRatio || "xMidYMid meet",
      ...options.attributes,
    });

    // 添加默认定义
    if (options.includeDefs !== false) {
      const defs = SvgUtils.createDefs();
      svg.appendChild(defs);
    }

    return svg;
  }

  /**
   * 创建定义区域
   * @returns {SVGElement} defs 元素
   */
  static createDefs() {
    const defs = SvgUtils.createElement("defs");

    // 添加默认渐变
    const gradients = [
      {
        id: "taskGradient",
        colors: [
          { offset: "0%", color: "#4A90E2", opacity: 1 },
          { offset: "100%", color: "#4A90E2", opacity: 0.8 },
        ],
      },
      {
        id: "progressGradient",
        colors: [
          { offset: "0%", color: "#7ED321", opacity: 1 },
          { offset: "100%", color: "#7ED321", opacity: 0.9 },
        ],
      },
      {
        id: "milestoneGradient",
        colors: [
          { offset: "0%", color: "#FF9500", opacity: 1 },
          { offset: "100%", color: "#FF6B00", opacity: 1 },
        ],
      },
    ];

    gradients.forEach((grad) => {
      const gradient = SvgUtils.createLinearGradient(grad.id, grad.colors);
      defs.appendChild(gradient);
    });

    // 添加默认图案
    const gridPattern = SvgUtils.createPattern("gridPattern", 30, 40, [
      {
        type: "rect",
        attrs: {
          width: 30,
          height: 40,
          fill: "none",
          stroke: "#f0f0f0",
          "stroke-width": 1,
        },
      },
    ]);
    defs.appendChild(gridPattern);

    return defs;
  }

  /**
   * 创建线性渐变
   * @param {string} id - 渐变ID
   * @param {Array} colors - 颜色数组
   * @param {object} direction - 渐变方向
   * @returns {SVGElement} linearGradient 元素
   */
  static createLinearGradient(
    id,
    colors,
    direction = { x1: "0%", y1: "0%", x2: "0%", y2: "100%" }
  ) {
    const gradient = SvgUtils.createElement("linearGradient", {
      id,
      ...direction,
    });

    colors.forEach((color) => {
      const stop = SvgUtils.createElement("stop", {
        offset: color.offset,
        "stop-color": color.color,
        "stop-opacity": color.opacity || 1,
      });
      gradient.appendChild(stop);
    });

    return gradient;
  }

  /**
   * 创建图案
   * @param {string} id - 图案ID
   * @param {number} width - 宽度
   * @param {number} height - 高度
   * @param {Array} elements - 元素数组
   * @returns {SVGElement} pattern 元素
   */
  static createPattern(id, width, height, elements) {
    const pattern = SvgUtils.createElement("pattern", {
      id,
      width,
      height,
      patternUnits: "userSpaceOnUse",
    });

    elements.forEach((elem) => {
      const element = SvgUtils.createElement(elem.type, elem.attrs);
      pattern.appendChild(element);
    });

    return pattern;
  }

  /**
   * 创建分组元素
   * @param {object} attributes - 属性
   * @param {string} className - CSS 类名
   * @returns {SVGElement} g 元素
   */
  static createGroup(attributes = {}, className = "") {
    if (className) {
      attributes.class = className;
    }
    return SvgUtils.createElement("g", attributes);
  }

  /**
   * 创建矩形
   * @param {number} x - X 坐标
   * @param {number} y - Y 坐标
   * @param {number} width - 宽度
   * @param {number} height - 高度
   * @param {object} options - 选项
   * @returns {SVGElement} rect 元素
   */
  static createRect(x, y, width, height, options = {}) {
    return SvgUtils.createElement("rect", {
      x,
      y,
      width,
      height,
      fill: options.fill || "#4A90E2",
      stroke: options.stroke || "none",
      "stroke-width": options.strokeWidth || 0,
      rx: options.borderRadius || 0,
      ry: options.borderRadius || 0,
      ...options.attributes,
    });
  }

  /**
   * 创建圆形
   * @param {number} cx - 圆心 X 坐标
   * @param {number} cy - 圆心 Y 坐标
   * @param {number} r - 半径
   * @param {object} options - 选项
   * @returns {SVGElement} circle 元素
   */
  static createCircle(cx, cy, r, options = {}) {
    return SvgUtils.createElement("circle", {
      cx,
      cy,
      r,
      fill: options.fill || "#FF9500",
      stroke: options.stroke || "none",
      "stroke-width": options.strokeWidth || 0,
      ...options.attributes,
    });
  }

  /**
   * 创建多边形
   * @param {Array|string} points - 点数组或点字符串
   * @param {object} options - 选项
   * @returns {SVGElement} polygon 元素
   */
  static createPolygon(points, options = {}) {
    const pointsStr = Array.isArray(points)
      ? points.map((p) => `${p.x},${p.y}`).join(" ")
      : points;

    return SvgUtils.createElement("polygon", {
      points: pointsStr,
      fill: options.fill || "#FF9500",
      stroke: options.stroke || "#333",
      "stroke-width": options.strokeWidth || 1,
      ...options.attributes,
    });
  }

  /**
   * 创建线段
   * @param {number} x1 - 起点 X 坐标
   * @param {number} y1 - 起点 Y 坐标
   * @param {number} x2 - 终点 X 坐标
   * @param {number} y2 - 终点 Y 坐标
   * @param {object} options - 选项
   * @returns {SVGElement} line 元素
   */
  static createLine(x1, y1, x2, y2, options = {}) {
    return SvgUtils.createElement("line", {
      x1,
      y1,
      x2,
      y2,
      stroke: options.stroke || "#666",
      "stroke-width": options.strokeWidth || 1,
      "stroke-dasharray": options.dashArray || "none",
      ...options.attributes,
    });
  }

  /**
   * 创建路径
   * @param {string} d - 路径字符串
   * @param {object} options - 选项
   * @returns {SVGElement} path 元素
   */
  static createPath(d, options = {}) {
    return SvgUtils.createElement("path", {
      d,
      fill: options.fill || "none",
      stroke: options.stroke || "#333",
      "stroke-width": options.strokeWidth || 2,
      "stroke-linecap": options.linecap || "round",
      "stroke-linejoin": options.linejoin || "round",
      ...options.attributes,
    });
  }

  /**
   * 创建文本
   * @param {string} text - 文本内容
   * @param {number} x - X 坐标
   * @param {number} y - Y 坐标
   * @param {object} options - 选项
   * @returns {SVGElement} text 元素
   */
  static createText(text, x, y, options = {}) {
    return SvgUtils.createElement(
      "text",
      {
        x,
        y,
        fill: options.fill || "#333",
        "font-size": options.fontSize || 12,
        "font-family": options.fontFamily || "Arial, sans-serif",
        "font-weight": options.fontWeight || "normal",
        "text-anchor": options.textAnchor || "start",
        "dominant-baseline": options.baseline || "auto",
        ...options.attributes,
      },
      text
    );
  }

  /**
   * 创建里程碑标记
   * @param {number} x - X 坐标
   * @param {number} y - Y 坐标
   * @param {string} type - 里程碑类型
   * @param {object} options - 选项
   * @returns {SVGElement} 里程碑元素
   */
  static createMilestone(x, y, type = "diamond", options = {}) {
    const group = SvgUtils.createGroup({
      transform: `translate(${x}, ${y})`,
      class: "milestone-marker",
      "data-milestone-id": options.id || "",
      "data-milestone-type": type,
    });

    let marker;
    const colors = {
      review: "#FF9500",
      delivery: "#007AFF",
      approval: "#34C759",
      warning: "#FF3B30",
      info: "#5AC8FA",
    };

    const fill = colors[options.status] || colors.info;

    switch (type) {
      case "diamond":
        marker = SvgUtils.createPath(SvgUtils.ICONS.milestone.diamond, {
          fill,
          stroke: "#333",
          strokeWidth: 1,
          ...options.attributes,
        });
        break;
      case "circle":
        marker = SvgUtils.createCircle(0, 0, 6, {
          fill,
          stroke: "#333",
          strokeWidth: 1,
          ...options.attributes,
        });
        break;
      case "triangle":
        marker = SvgUtils.createPath(SvgUtils.ICONS.milestone.triangle, {
          fill,
          stroke: "#333",
          strokeWidth: 1,
          ...options.attributes,
        });
        break;
      case "star":
        marker = SvgUtils.createPath(SvgUtils.ICONS.milestone.star, {
          fill,
          stroke: "#333",
          strokeWidth: 1,
          ...options.attributes,
        });
        break;
      default:
        marker = SvgUtils.createPath(SvgUtils.ICONS.milestone.diamond, {
          fill,
          stroke: "#333",
          strokeWidth: 1,
          ...options.attributes,
        });
    }

    group.appendChild(marker);

    // 添加连接线
    if (options.showLine !== false) {
      const line = SvgUtils.createLine(0, 8, 0, options.lineHeight || 24, {
        stroke: "#666",
        strokeWidth: 1,
        dashArray: "2,2",
      });
      group.appendChild(line);
    }

    return group;
  }

  /**
   * 创建任务条
   * @param {number} x - X 坐标
   * @param {number} y - Y 坐标
   * @param {number} width - 宽度
   * @param {number} height - 高度
   * @param {object} options - 选项
   * @returns {SVGElement} 任务条元素
   */
  static createTaskBar(x, y, width, height, options = {}) {
    const group = SvgUtils.createGroup({
      transform: `translate(${x}, ${y})`,
      class: "task-bar",
      "data-task-id": options.id || "",
      "data-task-status": options.status || "normal",
    });

    // 主任务条
    const rect = SvgUtils.createRect(0, 0, width, height, {
      fill: options.fill || "url(#taskGradient)",
      stroke: options.stroke || "none",
      borderRadius: options.borderRadius || 4,
      attributes: {
        class: "task-main-bar",
      },
    });
    group.appendChild(rect);

    // 进度条
    if (options.progress > 0) {
      const progressWidth = width * options.progress;
      const progressRect = SvgUtils.createRect(0, 0, progressWidth, height, {
        fill: options.progressFill || "url(#progressGradient)",
        borderRadius: options.borderRadius || 4,
        attributes: {
          class: "task-progress-bar",
          opacity: 0.8,
        },
      });
      group.appendChild(progressRect);
    }

    // 任务文本
    if (options.showText !== false && options.text) {
      const textY = height / 2 + 4; // 垂直居中
      const text = SvgUtils.createText(options.text, 8, textY, {
        fill: options.textColor || "#fff",
        fontSize: options.fontSize || 12,
        fontWeight: "500",
        attributes: {
          class: "task-text",
        },
      });
      group.appendChild(text);
    }

    return group;
  }

  /**
   * 创建依赖关系连线
   * @param {object} fromPoint - 起点 {x, y}
   * @param {object} toPoint - 终点 {x, y}
   * @param {object} options - 选项
   * @returns {SVGElement} 连线元素
   */
  static createDependencyLine(fromPoint, toPoint, options = {}) {
    const group = SvgUtils.createGroup({
      class: "dependency-line",
      "data-from": options.fromId || "",
      "data-to": options.toId || "",
    });

    // 计算控制点（贝塞尔曲线）
    const midX = (fromPoint.x + toPoint.x) / 2;
    const controlPoint1 = { x: midX, y: fromPoint.y };
    const controlPoint2 = { x: midX, y: toPoint.y };

    // 创建曲线路径
    const pathData = `M ${fromPoint.x},${fromPoint.y} 
                     C ${controlPoint1.x},${controlPoint1.y} 
                       ${controlPoint2.x},${controlPoint2.y} 
                       ${toPoint.x},${toPoint.y}`;

    const path = SvgUtils.createPath(pathData, {
      fill: "none",
      stroke: options.stroke || "#666",
      strokeWidth: options.strokeWidth || 2,
      attributes: {
        "marker-end": "url(#arrowhead)",
      },
    });
    group.appendChild(path);

    // 创建箭头标记
    if (options.showArrow !== false) {
      const arrowSize = 6;
      const angle = Math.atan2(
        toPoint.y - fromPoint.y,
        toPoint.x - fromPoint.x
      );

      const arrowPoints = [
        { x: toPoint.x, y: toPoint.y },
        {
          x: toPoint.x - arrowSize * Math.cos(angle - Math.PI / 6),
          y: toPoint.y - arrowSize * Math.sin(angle - Math.PI / 6),
        },
        {
          x: toPoint.x - arrowSize * Math.cos(angle + Math.PI / 6),
          y: toPoint.y - arrowSize * Math.sin(angle + Math.PI / 6),
        },
      ];

      const arrow = SvgUtils.createPolygon(arrowPoints, {
        fill: options.stroke || "#666",
        stroke: "none",
      });
      group.appendChild(arrow);
    }

    return group;
  }

  /**
   * 创建时间轴刻度
   * @param {Array} scales - 刻度数组
   * @param {number} height - 高度
   * @param {object} options - 选项
   * @returns {SVGElement} 刻度组
   */
  static createTimeScales(scales, height, options = {}) {
    const group = SvgUtils.createGroup({ class: "time-scales" });

    scales.forEach((scale) => {
      const scaleGroup = SvgUtils.createGroup({
        transform: `translate(${scale.x}, 0)`,
        class: "time-scale",
      });

      // 刻度线
      const line = SvgUtils.createLine(0, 0, 0, height, {
        stroke: options.lineColor || "#e0e0e0",
        strokeWidth: options.lineWidth || 1,
      });
      scaleGroup.appendChild(line);

      // 刻度标签
      if (scale.label) {
        const text = SvgUtils.createText(scale.label, 4, 16, {
          fill: options.textColor || "#666",
          fontSize: options.fontSize || 11,
          attributes: {
            class: "scale-label",
          },
        });
        scaleGroup.appendChild(text);
      }

      group.appendChild(scaleGroup);
    });

    return group;
  }

  /**
   * 批量设置元素属性
   * @param {SVGElement} element - SVG 元素
   * @param {object} attributes - 属性对象
   */
  static setAttributes(element, attributes) {
    Object.entries(attributes).forEach(([key, value]) => {
      if (value !== null && value !== undefined) {
        element.setAttribute(key, value);
      }
    });
  }

  /**
   * 获取元素的边界框
   * @param {SVGElement} element - SVG 元素
   * @returns {object} 边界框信息
   */
  static getBBox(element) {
    try {
      return element.getBBox();
    } catch (e) {
      // 对于未渲染的元素，返回默认值
      return { x: 0, y: 0, width: 0, height: 0 };
    }
  }

  /**
   * 清空 SVG 容器
   * @param {SVGElement} container - 容器元素
   */
  static clearContainer(container) {
    while (container.firstChild) {
      container.removeChild(container.firstChild);
    }
  }

  /**
   * 计算两点之间的距离
   * @param {object} point1 - 点1 {x, y}
   * @param {object} point2 - 点2 {x, y}
   * @returns {number} 距离
   */
  static getDistance(point1, point2) {
    const dx = point2.x - point1.x;
    const dy = point2.y - point1.y;
    return Math.sqrt(dx * dx + dy * dy);
  }

  /**
   * 将屏幕坐标转换为 SVG 坐标
   * @param {SVGElement} svg - SVG 元素
   * @param {number} clientX - 屏幕 X 坐标
   * @param {number} clientY - 屏幕 Y 坐标
   * @returns {object} SVG 坐标 {x, y}
   */
  static screenToSVG(svg, clientX, clientY) {
    const pt = svg.createSVGPoint();
    pt.x = clientX;
    pt.y = clientY;

    try {
      const screenCTM = svg.getScreenCTM();
      const svgPoint = pt.matrixTransform(screenCTM.inverse());
      return { x: svgPoint.x, y: svgPoint.y };
    } catch (e) {
      // 降级处理
      const rect = svg.getBoundingClientRect();
      return {
        x: clientX - rect.left,
        y: clientY - rect.top,
      };
    }
  }

  /**
   * 创建动画元素
   * @param {string} attributeName - 动画属性名
   * @param {string} from - 起始值
   * @param {string} to - 结束值
   * @param {number} duration - 持续时间（秒）
   * @param {object} options - 选项
   * @returns {SVGElement} animate 元素
   */
  static createAnimation(attributeName, from, to, duration, options = {}) {
    return SvgUtils.createElement("animate", {
      attributeName,
      from,
      to,
      dur: `${duration}s`,
      repeatCount: options.repeat || "1",
      fill: options.fill || "freeze",
      begin: options.begin || "0s",
      ...options.attributes,
    });
  }

  /**
   * 添加 CSS 样式到 SVG
   * @param {SVGElement} svg - SVG 元素
   * @param {string} css - CSS 样式字符串
   */
  static addStyles(svg, css) {
    const style = SvgUtils.createElement("style");
    style.textContent = css;

    let defs = svg.querySelector("defs");
    if (!defs) {
      defs = SvgUtils.createDefs();
      svg.insertBefore(defs, svg.firstChild);
    }

    defs.appendChild(style);
  }

  /**
   * 创建滤镜效果
   * @param {string} id - 滤镜ID
   * @param {Array} effects - 效果数组
   * @returns {SVGElement} filter 元素
   */
  static createFilter(id, effects) {
    const filter = SvgUtils.createElement("filter", { id });

    effects.forEach((effect) => {
      const filterElement = SvgUtils.createElement(
        effect.type,
        effect.attributes
      );
      filter.appendChild(filterElement);
    });

    return filter;
  }

  /**
   * 工具函数：生成唯一ID
   * @param {string} prefix - 前缀
   * @returns {string} 唯一ID
   */
  static generateId(prefix = "svg") {
    return `${prefix}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 检查浏览器对 SVG 的支持
   * @returns {boolean} 是否支持 SVG
   */
  static isSvgSupported() {
    return (
      typeof SVGElement !== "undefined" &&
      document.createElementNS &&
      document.createElementNS(SvgUtils.SVG_NS, "svg").createSVGRect
    );
  }
}

export default SvgUtils;
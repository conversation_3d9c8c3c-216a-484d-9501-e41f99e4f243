import DateUtils from "../utils/DateUtils.js";

/**
 * 数据验证器 - 增强版本
 * 增加多时间线布局验证
 */
class DataValidator {
  /**
   * 验证时间线数据
   * @param {object} timelineData - 时间线数据
   * @returns {object} 验证结果
   */
  static validateTimeline(timelineData) {
    const errors = [];
    const warnings = [];

    if (!timelineData.name || timelineData.name.trim() === "") {
      errors.push("时间线名称不能为空");
    }

    // 验证Y轴布局属性
    if (timelineData.yOffset < 0) {
      warnings.push("Y轴偏移量为负数，可能导致显示异常");
    }

    if (timelineData.height && timelineData.height < 10) {
      warnings.push("时间线高度过小，可能影响显示效果");
    }

    if (timelineData.milestones) {
      timelineData.milestones.forEach((milestone, index) => {
        const milestoneResult = DataValidator.validateMilestone(milestone);
        if (milestoneResult.errors.length > 0) {
          errors.push(
            `里程碑 ${index + 1}: ${milestoneResult.errors.join(", ")}`
          );
        }
        warnings.push(
          ...milestoneResult.warnings.map((w) => `里程碑 ${index + 1}: ${w}`)
        );
      });
    }

    return { errors, warnings, valid: errors.length === 0 };
  }

  /**
   * 验证里程碑数据
   * @param {object} milestoneData - 里程碑数据
   * @returns {object} 验证结果
   */
  static validateMilestone(milestoneData) {
    const errors = [];
    const warnings = [];

    if (!milestoneData.name || milestoneData.name.trim() === "") {
      errors.push("里程碑名称不能为空");
    }

    if (!milestoneData.date) {
      errors.push("里程碑日期不能为空");
    } else {
      const date = DateUtils.parseDate(milestoneData.date);
      if (!date) {
        errors.push("里程碑日期格式无效");
      } else if (
        date < DateUtils.today() &&
        milestoneData.status === "pending"
      ) {
        warnings.push("里程碑日期已过期，建议更新状态");
      }
    }

    return { errors, warnings, valid: errors.length === 0 };
  }

  /**
   * 验证整个甘特图数据
   * @param {Array} data - 甘特图数据数组
   * @returns {object} 验证结果
   */
  static validateGanttData(data) {
    const errors = [];
    const warnings = [];
    const taskIds = new Set();

    if (!Array.isArray(data)) {
      errors.push("数据必须是数组格式");
      return { errors, warnings, valid: false };
    }

    data.forEach((task, index) => {
      // 检查ID唯一性
      if (taskIds.has(task.id)) {
        errors.push(`任务 ${index + 1}: ID "${task.id}" 重复`);
      } else {
        taskIds.add(task.id);
      }

      // 验证任务数据
      const taskResult = DataValidator.validateTask(task);
      if (taskResult.errors.length > 0) {
        errors.push(
          `任务 ${index + 1} (${task.name}): ${taskResult.errors.join(", ")}`
        );
      }
      warnings.push(
        ...taskResult.warnings.map(
          (w) => `任务 ${index + 1} (${task.name}): ${w}`
        )
      );
    });

    // 全局布局检查
    const layoutWarnings = DataValidator.validateGlobalLayout(data);
    warnings.push(...layoutWarnings);

    return { errors, warnings, valid: errors.length === 0 };
  }

  /**
   * 验证全局布局
   * @param {Array} data - 甘特图数据数组
   * @returns {string[]} 警告信息
   */
  static validateGlobalLayout(data) {
    const warnings = [];

    // 检查任务高度分布
    const taskHeights = data.map((task) => {
      if (task.timelines && task.timelines.length > 0) {
        return task.expandedHeight || 40;
      }
      return task.rowHeight || 40;
    });

    const maxHeight = Math.max(...taskHeights);
    const minHeight = Math.min(...taskHeights);

    if (maxHeight / minHeight > 5) {
      warnings.push("任务高度差异过大，可能影响整体显示效果");
    }

    // 检查时间线数量分布
    const timelineCounts = data.map((task) =>
      task.timelines ? task.timelines.length : 0
    );
    const maxTimelines = Math.max(...timelineCounts);

    if (maxTimelines > 10) {
      warnings.push("某些任务包含过多时间线，可能影响性能和可读性");
    }

    return warnings;
  }

  /**
   * 验证任务数据
   * @param {object} taskData - 任务数据
   * @returns {object} 验证结果
   */
  static validateTask(taskData) {
    const errors = [];
    const warnings = [];

    if (!taskData.name || taskData.name.trim() === "") {
      errors.push("任务名称不能为空");
    }

    if (taskData.startDate && taskData.endDate) {
      const start = DateUtils.parseDate(taskData.startDate);
      const end = DateUtils.parseDate(taskData.endDate);

      if (start && end && start > end) {
        errors.push("开始日期不能晚于结束日期");
      }
    }

    if (taskData.progress < 0 || taskData.progress > 1) {
      errors.push("进度值必须在 0-1 之间");
    }

    // 验证时间线布局
    if (taskData.timelines && taskData.timelines.length > 0) {
      const layoutWarnings = DataValidator.validateTimelineLayout(
        taskData.timelines
      );
      warnings.push(...layoutWarnings);

      taskData.timelines.forEach((timeline, index) => {
        const timelineResult = DataValidator.validateTimeline(timeline);
        if (timelineResult.errors.length > 0) {
          errors.push(
            `时间线 ${index + 1} (${
              timeline.name
            }): ${timelineResult.errors.join(", ")}`
          );
        }
        warnings.push(
          ...timelineResult.warnings.map(
            (w) => `时间线 ${index + 1} (${timeline.name}): ${w}`
          )
        );
      });
    }

    return { errors, warnings, valid: errors.length === 0 };
  }

  /**
   * 验证时间线布局
   * @param {object[]} timelines - 时间线数组
   * @returns {string[]} 警告信息
   */
  static validateTimelineLayout(timelines) {
    const warnings = [];

    // 检查时间线重叠
    const sortedTimelines = timelines
      .filter((t) => t.visible)
      .sort((a, b) => (a.yOffset || 0) - (b.yOffset || 0));

    for (let i = 0; i < sortedTimelines.length - 1; i++) {
      const current = sortedTimelines[i];
      const next = sortedTimelines[i + 1];

      const currentEnd = (current.yOffset || 0) + (current.height || 20);
      const nextStart = next.yOffset || 0;

      if (currentEnd > nextStart) {
        warnings.push(
          `时间线 "${current.name}" 与 "${next.name}" 在Y轴上可能重叠`
        );
      }
    }

    // 检查时间线顺序
    const orderedTimelines = [...timelines].sort(
      (a, b) => (a.order || 0) - (b.order || 0)
    );
    let hasOrderConflict = false;

    for (let i = 0; i < orderedTimelines.length - 1; i++) {
      if (orderedTimelines[i].order === orderedTimelines[i + 1].order) {
        hasOrderConflict = true;
        break;
      }
    }

    if (hasOrderConflict) {
      warnings.push("存在相同order值的时间线，可能影响显示顺序");
    }

    return warnings;
  }
}

export default DataValidator;

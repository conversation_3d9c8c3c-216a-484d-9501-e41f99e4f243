<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Step3: 优化表格头部演示</title>
    <link rel="stylesheet" href="../../src/styles/gantt.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }

        .header h1 {
            margin: 0;
            font-size: 24px;
        }

        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
        }

        .demo-info {
            padding: 20px;
            background: #f3f4f6;
            border-bottom: 1px solid #e5e7eb;
        }

        .demo-info h3 {
            margin: 0 0 10px 0;
            color: #374151;
        }

        .feature-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }

        .feature-item {
            background: white;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid #8b5cf6;
        }

        .feature-item h4 {
            margin: 0 0 8px 0;
            color: #8b5cf6;
            font-size: 14px;
        }

        .feature-item p {
            margin: 0;
            color: #6b7280;
            font-size: 13px;
            line-height: 1.4;
        }

        .gantt-container {
            height: 600px;
            position: relative;
        }

        .controls {
            padding: 20px;
            background: #f9fafb;
            border-top: 1px solid #e5e7eb;
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }

        .control-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .control-group label {
            font-size: 12px;
            font-weight: 600;
            color: #374151;
            text-transform: uppercase;
        }

        .control-group button {
            padding: 8px 16px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            background: white;
            color: #374151;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .control-group button:hover {
            background: #8b5cf6;
            color: white;
            border-color: #8b5cf6;
        }

        .control-group button.active {
            background: #8b5cf6;
            color: white;
            border-color: #8b5cf6;
        }

        .event-log {
            padding: 20px;
            background: #1f2937;
            color: #f9fafb;
            max-height: 200px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }

        .event-log h3 {
            margin: 0 0 10px 0;
            color: #8b5cf6;
        }

        .log-entry {
            margin: 5px 0;
            padding: 5px;
            border-left: 3px solid #8b5cf6;
            background: rgba(139, 92, 246, 0.1);
        }

        .log-entry.sort {
            border-left-color: #10b981;
            background: rgba(16, 185, 129, 0.1);
        }

        .log-entry.resize {
            border-left-color: #f59e0b;
            background: rgba(245, 158, 11, 0.1);
        }

        /* 增强表格头部显示 */
        .gantt-table-header {
            border: 2px solid #8b5cf6 !important;
        }

        .gantt-timeline-header {
            border: 2px solid #8b5cf6 !important;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>✨ 优化表格头部演示</h1>
            <p>展示排序、列宽调整、悬停效果等高级表格头部功能</p>
        </div>

        <div class="demo-info">
            <h3>功能特性</h3>
            <div class="feature-list">
                <div class="feature-item">
                    <h4>🔄 列排序</h4>
                    <p>点击列头进行升序/降序排序，支持排序状态指示器</p>
                </div>
                <div class="feature-item">
                    <h4>📏 列宽调整</h4>
                    <p>拖拽列边界调整列宽，支持最小/最大宽度限制</p>
                </div>
                <div class="feature-item">
                    <h4>🎨 视觉效果</h4>
                    <p>渐变背景、悬停动画、状态指示等现代化视觉效果</p>
                </div>
                <div class="feature-item">
                    <h4>📱 响应式设计</h4>
                    <p>移动端优化，自适应不同屏幕尺寸</p>
                </div>
                <div class="feature-item">
                    <h4>⚡ 性能优化</h4>
                    <p>事件防抖、智能重渲染、内存管理优化</p>
                </div>
                <div class="feature-item">
                    <h4>🔧 可配置</h4>
                    <p>支持图标、描述、排序/调整开关等丰富配置</p>
                </div>
            </div>
        </div>

        <div class="gantt-container" id="ganttContainer"></div>

        <div class="controls">
            <div class="control-group">
                <label>测试功能</label>
                <button id="testSortBtn">测试排序</button>
            </div>
            <div class="control-group">
                <label>列宽调整</label>
                <button id="resetColumnsBtn">重置列宽</button>
            </div>
            <div class="control-group">
                <label>数据操作</label>
                <button id="addDataBtn">添加数据</button>
            </div>
            <div class="control-group">
                <label>视图切换</label>
                <button id="switchViewBtn">切换视图</button>
            </div>
        </div>

        <div class="event-log">
            <h3>事件日志</h3>
            <div id="logContainer"></div>
        </div>
    </div>

    <script type="module">
        import GanttChart from '../../src/GanttChart.js';
        import DateUtils from '../../src/utils/DateUtils.js';

        // 生成演示数据
        function generateDemoData(count = 25) {
            const tasks = [];
            const startDate = new Date('2024-01-01');
            const priorities = ['低', '中', '高'];
            const statuses = ['待开始', '进行中', '已完成', '已暂停'];
            const departments = ['开发部', '设计部', '测试部', '产品部', '运营部'];
            
            for (let i = 0; i < count; i++) {
                const taskStart = DateUtils.addDays(startDate, i * 8);
                const duration = 5 + Math.random() * 15;
                const taskEnd = DateUtils.addDays(taskStart, duration);
                
                tasks.push({
                    id: `opt-task-${i}`,
                    name: `优化演示任务 ${i + 1}`,
                    startDate: taskStart,
                    endDate: taskEnd,
                    progress: Math.random(),
                    priority: priorities[Math.floor(Math.random() * priorities.length)],
                    status: statuses[Math.floor(Math.random() * statuses.length)],
                    department: departments[Math.floor(Math.random() * departments.length)],
                    assignee: `用户${i + 1}`,
                    budget: Math.floor(Math.random() * 100000) + 10000
                });
            }
            
            return tasks;
        }

        // 初始化甘特图
        let ganttInstance = null;

        function initializeGantt() {
            const container = document.getElementById('ganttContainer');
            container.innerHTML = '';
            
            const demoData = generateDemoData();
            
            ganttInstance = new GanttChart('ganttContainer', {
                data: demoData,
                viewMode: 'day',
                pixelsPerDay: 35,
                taskList: {
                    columns: [
                        { 
                            key: 'name', 
                            title: '任务名称', 
                            width: 200,
                            minWidth: 150,
                            maxWidth: 300,
                            icon: '📋',
                            description: '任务的名称和描述'
                        },
                        { 
                            key: 'priority', 
                            title: '优先级', 
                            width: 80,
                            minWidth: 60,
                            maxWidth: 120,
                            icon: '⚡',
                            description: '任务优先级'
                        },
                        { 
                            key: 'status', 
                            title: '状态', 
                            width: 90,
                            minWidth: 70,
                            maxWidth: 130,
                            icon: '📊',
                            description: '任务当前状态'
                        },
                        { 
                            key: 'department', 
                            title: '部门', 
                            width: 100,
                            minWidth: 80,
                            maxWidth: 150,
                            icon: '🏢',
                            description: '负责部门'
                        },
                        { 
                            key: 'assignee', 
                            title: '负责人', 
                            width: 90,
                            minWidth: 70,
                            maxWidth: 130,
                            icon: '👤',
                            description: '任务负责人'
                        },
                        { 
                            key: 'progress', 
                            title: '进度', 
                            width: 80,
                            minWidth: 60,
                            maxWidth: 100,
                            icon: '📈',
                            description: '任务完成进度'
                        }
                    ]
                }
            });

            // 监听事件
            ganttInstance.on('ready', () => {
                logEvent('甘特图初始化完成', 'info');
            });

            ganttInstance.on('columnSort', (data) => {
                logEvent(`列排序: ${data.column} ${data.direction}`, 'sort');
            });

            ganttInstance.on('columnResize', (data) => {
                logEvent(`列宽调整: ${data.column} -> ${data.width}px`, 'resize');
            });
        }

        // 日志记录
        function logEvent(message, type = 'info') {
            const logContainer = document.getElementById('logContainer');
            const entry = document.createElement('div');
            entry.className = `log-entry ${type}`;
            entry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            logContainer.appendChild(entry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        // 事件绑定
        document.getElementById('testSortBtn').addEventListener('click', () => {
            const columns = ['name', 'priority', 'status', 'department'];
            const column = columns[Math.floor(Math.random() * columns.length)];
            const direction = Math.random() > 0.5 ? 'asc' : 'desc';
            
            // 模拟点击排序
            ganttInstance._handleColumnSort(column);
            logEvent(`自动测试排序: ${column}`, 'sort');
        });

        document.getElementById('resetColumnsBtn').addEventListener('click', () => {
            // 重置所有列宽
            ganttInstance.options.taskList.columns.forEach(col => {
                col.width = col.minWidth + (col.maxWidth - col.minWidth) / 2;
            });
            ganttInstance.renderTaskListHeader();
            logEvent('列宽已重置', 'resize');
        });

        document.getElementById('addDataBtn').addEventListener('click', () => {
            const newData = generateDemoData(5);
            ganttInstance.setData([...ganttInstance.getData(), ...newData]);
            logEvent(`添加了 ${newData.length} 条新数据`, 'info');
        });

        document.getElementById('switchViewBtn').addEventListener('click', () => {
            const modes = ['day', 'week', 'month'];
            const currentMode = ganttInstance.state.viewMode;
            const currentIndex = modes.indexOf(currentMode);
            const nextMode = modes[(currentIndex + 1) % modes.length];
            
            ganttInstance.changeViewMode(nextMode);
            logEvent(`视图切换: ${currentMode} -> ${nextMode}`, 'info');
        });

        // 初始化
        document.addEventListener('DOMContentLoaded', () => {
            logEvent('页面加载完成，初始化甘特图...', 'info');
            initializeGantt();
        });
    </script>
</body>
</html>

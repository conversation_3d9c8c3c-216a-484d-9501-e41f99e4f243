<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>网格全渲染测试</title>
    <link rel="stylesheet" href="../../src/styles/gantt.css">
    <style>
        body {
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
        }

        .test-container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .test-header {
            background: linear-gradient(135deg, #28a745 0%, #17a2b8 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }

        .test-header h1 {
            margin: 0 0 10px 0;
            font-size: 24px;
        }

        .test-header p {
            margin: 0;
            opacity: 0.9;
            font-size: 14px;
        }

        .test-instructions {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 6px;
            padding: 15px;
            margin: 20px;
            color: #155724;
        }

        .test-instructions h4 {
            margin: 0 0 10px 0;
            color: #155724;
        }

        .test-instructions ol {
            margin: 10px 0 0 20px;
            padding: 0;
        }

        .test-instructions li {
            margin-bottom: 5px;
        }

        .gantt-container {
            height: 600px;
            margin: 0;
            border-radius: 0;
            border: none;
            box-shadow: none;
        }

        .grid-info {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(40, 167, 69, 0.9);
            color: white;
            padding: 15px;
            border-radius: 8px;
            font-family: monospace;
            font-size: 12px;
            z-index: 1000;
            min-width: 250px;
        }

        .grid-info h4 {
            margin: 0 0 10px 0;
            font-size: 14px;
            color: #fff;
        }

        .grid-info div {
            margin-bottom: 5px;
            display: flex;
            justify-content: space-between;
        }

        .grid-info div:last-child {
            margin-bottom: 0;
        }

        .status-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 5px;
        }

        .status-success {
            background-color: #28a745;
        }

        .status-warning {
            background-color: #ffc107;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>🔧 网格全渲染测试</h1>
            <p>测试网格渲染虚拟化移除后的效果和性能</p>
        </div>

        <div class="test-instructions">
            <h4>🧪 测试内容：</h4>
            <ol>
                <li>检查网格线是否完整渲染（不再使用虚拟化）</li>
                <li>横向滚动时观察网格线是否保持完整</li>
                <li>检查控制台日志中的网格渲染信息</li>
                <li>验证滚动性能是否流畅</li>
                <li>观察右上角的网格统计信息</li>
            </ol>
            <p><strong>预期结果</strong>：网格线应该一次性全部渲染，滚动时无需重新计算，提供流畅的滚动体验。</p>
        </div>

        <div id="gantt-container" class="gantt-container"></div>
    </div>

    <div class="grid-info" id="gridInfo">
        <h4>📊 网格渲染状态</h4>
        <div>渲染模式: <span><span class="status-indicator status-success"></span>全渲染</span></div>
        <div>主要网格线: <span id="majorLines">-</span></div>
        <div>次要网格线: <span id="minorLines">-</span></div>
        <div>子网格线: <span id="subLines">-</span></div>
        <div>总宽度: <span id="totalWidth">-</span></div>
        <div>渲染时间: <span id="renderTime">-</span></div>
        <div>虚拟化状态: <span style="color: #dc3545;">❌ 已禁用</span></div>
    </div>

    <script type="module">
        import GanttChart from '../../src/GanttChart.js';

        // 生成测试数据 - 较大的时间范围以测试网格性能
        function generateTestData() {
            const data = [];
            const startDate = new Date('2024-01-01');
            
            for (let i = 0; i < 25; i++) {
                const taskStartDate = new Date(startDate);
                taskStartDate.setDate(startDate.getDate() + i * 12);
                
                const taskEndDate = new Date(taskStartDate);
                taskEndDate.setDate(taskStartDate.getDate() + 10);
                
                data.push({
                    id: `task-${i + 1}`,
                    name: `任务 ${i + 1}`,
                    startDate: taskStartDate.toISOString().split('T')[0],
                    endDate: taskEndDate.toISOString().split('T')[0],
                    progress: Math.random(),
                    assignee: `用户${i + 1}`,
                    status: ['pending', 'in-progress', 'completed'][Math.floor(Math.random() * 3)],
                    level: 0
                });
            }
            
            return data;
        }

        // 初始化甘特图
        let ganttInstance;
        
        try {
            const startTime = performance.now();
            
            ganttInstance = new GanttChart('gantt-container', {
                data: generateTestData(),
                viewMode: 'day',
                pixelsPerDay: 40,
                taskList: {
                    width: 250,
                    columns: [
                        { key: 'name', title: '任务名称', width: 150 },
                        { key: 'assignee', title: '负责人', width: 100 }
                    ]
                },
                timeline: {
                    showWeekday: true,
                    adaptiveWidth: true,
                    weekdayFormat: 'short'
                }
            });

            const endTime = performance.now();
            const initTime = Math.round(endTime - startTime);

            console.log('甘特图初始化成功，耗时:', initTime, 'ms');

            // 更新网格统计信息
            setTimeout(() => {
                updateGridInfo(initTime);
            }, 100);

            // 监听滚动事件
            ganttInstance.on('scroll', (data) => {
                // 在全渲染模式下，滚动时不应该有网格重新计算
                console.log('滚动位置:', Math.round(data.x), Math.round(data.y));
            });

        } catch (error) {
            console.error('甘特图初始化失败:', error);
            document.getElementById('gridInfo').innerHTML = `
                <h4>❌ 初始化失败</h4>
                <div style="color: #dc3545;">${error.message}</div>
            `;
        }

        function updateGridInfo(renderTime) {
            if (!ganttInstance || !ganttInstance.timeScale) {
                return;
            }

            try {
                // 获取网格统计信息
                const allScales = ganttInstance.timeScale.getAllScales();
                const totalWidth = ganttInstance.timeScale.getTotalWidth();
                
                // 模拟获取网格线数量（实际应该从渲染结果中获取）
                const gridLines = ganttInstance.timeScale.getAllGridLines({
                    includeSubGrid: true,
                    alignToPixel: true,
                    optimizePerformance: false
                });

                document.getElementById('majorLines').textContent = gridLines.major.length;
                document.getElementById('minorLines').textContent = gridLines.minor.length;
                document.getElementById('subLines').textContent = gridLines.sub?.length || 0;
                document.getElementById('totalWidth').textContent = `${totalWidth}px`;
                document.getElementById('renderTime').textContent = `${renderTime}ms`;

                console.log('网格统计:', {
                    totalScales: allScales.length,
                    majorLines: gridLines.major.length,
                    minorLines: gridLines.minor.length,
                    subLines: gridLines.sub?.length || 0,
                    totalWidth: totalWidth,
                    renderTime: renderTime
                });

            } catch (error) {
                console.error('获取网格信息失败:', error);
            }
        }

        // 添加测试按钮
        const testButtons = document.createElement('div');
        testButtons.style.cssText = `
            position: fixed;
            bottom: 20px;
            right: 20px;
            display: flex;
            flex-direction: column;
            gap: 10px;
            z-index: 1000;
        `;

        const scrollTestBtn = document.createElement('button');
        scrollTestBtn.textContent = '滚动测试';
        scrollTestBtn.style.cssText = `
            padding: 10px 15px;
            background: #28a745;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        `;
        scrollTestBtn.onclick = () => {
            const chartBody = ganttInstance.elements.chartBody;
            const totalWidth = ganttInstance.timeScale.getTotalWidth();
            
            // 平滑滚动到中间位置
            const targetX = (totalWidth - chartBody.offsetWidth) / 2;
            chartBody.scrollTo({
                left: targetX,
                behavior: 'smooth'
            });
        };

        const refreshBtn = document.createElement('button');
        refreshBtn.textContent = '刷新网格';
        refreshBtn.style.cssText = scrollTestBtn.style.cssText;
        refreshBtn.onclick = () => {
            const startTime = performance.now();
            ganttInstance.renderGrid();
            const endTime = performance.now();
            console.log('网格重新渲染耗时:', Math.round(endTime - startTime), 'ms');
        };

        testButtons.appendChild(scrollTestBtn);
        testButtons.appendChild(refreshBtn);
        document.body.appendChild(testButtons);
    </script>
</body>
</html>

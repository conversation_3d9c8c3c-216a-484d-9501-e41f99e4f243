/**
 * 甘特图数据结构定义 - 多时间线增强版本
 * 支持多时间线里程碑和完整的任务管理，优化Y轴布局
 */

/**
 * 里程碑数据结构
 */
class Milestone {
  constructor(data = {}) {
    this.id = data.id || this.generateId();
    this.name = data.name || '';
    this.date = DateUtils.parseDate(data.date);
    this.type = data.type || 'review'; // review, delivery, approval, warning, info
    this.status = data.status || 'pending'; // pending, completed, overdue, cancelled
    this.description = data.description || '';
    this.icon = data.icon || 'diamond'; // diamond, circle, triangle, star
    this.color = data.color || null; // 自定义颜色，null 时使用类型默认颜色
    this.priority = data.priority || 'normal'; // low, normal, high, critical
    
    // 扩展属性
    this.customFields = data.customFields || {};
    this.metadata = data.metadata || {};
    
    // 时间线关联
    this.timelineId = data.timelineId || null;
    
    this.validate();
  }

  validate() {
    if (!this.name) {
      throw new Error('Milestone name is required');
    }
    if (!this.date) {
      throw new Error('Milestone date is required');
    }
  }

  generateId() {
    return `milestone-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  // 获取里程碑颜色
  getColor() {
    if (this.color) return this.color;
    
    const colors = {
      review: '#FF9500',      // 橙色
      delivery: '#007AFF',    // 蓝色
      approval: '#34C759',    // 绿色
      warning: '#FF3B30',     // 红色
      info: '#5AC8FA'         // 浅蓝色
    };
    
    return colors[this.type] || colors.info;
  }

  // 检查是否逾期
  isOverdue() {
    if (this.status === 'completed') return false;
    return this.date < DateUtils.today();
  }

  // 克隆里程碑
  clone() {
    return new Milestone({
      ...this,
      id: this.generateId(),
      customFields: { ...this.customFields },
      metadata: { ...this.metadata }
    });
  }

  // 转换为 JSON
  toJSON() {
    return {
      id: this.id,
      name: this.name,
      date: DateUtils.formatDate(this.date),
      type: this.type,
      status: this.status,
      description: this.description,
      icon: this.icon,
      color: this.color,
      priority: this.priority,
      timelineId: this.timelineId,
      customFields: this.customFields,
      metadata: this.metadata
    };
  }
}

/**
 * 时间线数据结构 - 增强版本
 * 一个任务可以包含多条时间线，每条时间线包含多个里程碑
 * 增加Y轴布局相关属性
 */
class Timeline {
  constructor(data = {}) {
    this.id = data.id || this.generateId();
    this.name = data.name || '';
    this.description = data.description || '';
    this.color = data.color || this.getDefaultColor();
    this.type = data.type || 'default'; // default, critical, secondary, technical, business
    this.visible = data.visible !== false; // 默认可见
    this.order = data.order || 0; // 显示顺序
    
    // Y轴布局属性 - 新增
    this.yOffset = data.yOffset || 0; // Y轴偏移量（像素）
    this.height = data.height || 20; // 时间线高度（像素）
    this.layer = data.layer || 0; // 图层层级，决定渲染顺序
    this.collapsed = data.collapsed || false; // 是否折叠
    
    // 样式属性 - 新增
    this.lineStyle = data.lineStyle || {
      strokeWidth: 2,
      strokeDashArray: null, // 实线为null，虚线如 "5,5"
      opacity: 1.0
    };
    
    // 里程碑列表
    this.milestones = [];
    if (data.milestones && Array.isArray(data.milestones)) {
      this.milestones = data.milestones.map(m => 
        m instanceof Milestone ? m : new Milestone({ ...m, timelineId: this.id })
      );
    }
    
    // 扩展属性
    this.customFields = data.customFields || {};
    this.metadata = data.metadata || {};
    
    this.validate();
  }

  validate() {
    if (!this.name) {
      throw new Error('Timeline name is required');
    }
  }

  generateId() {
    return `timeline-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  // 根据类型获取默认颜色
  getDefaultColor() {
    const colors = {
      default: '#4A90E2',
      critical: '#FF3B30',
      secondary: '#8E8E93',
      technical: '#007AFF',
      business: '#34C759'
    };
    return colors[this.type] || colors.default;
  }

  // 添加里程碑
  addMilestone(milestoneData) {
    const milestone = milestoneData instanceof Milestone 
      ? milestoneData 
      : new Milestone({ ...milestoneData, timelineId: this.id });
    
    this.milestones.push(milestone);
    this.sortMilestones();
    return milestone;
  }

  // 删除里程碑
  removeMilestone(milestoneId) {
    const index = this.milestones.findIndex(m => m.id === milestoneId);
    if (index !== -1) {
      return this.milestones.splice(index, 1)[0];
    }
    return null;
  }

  // 获取里程碑
  getMilestone(milestoneId) {
    return this.milestones.find(m => m.id === milestoneId);
  }

  // 按日期排序里程碑
  sortMilestones() {
    this.milestones.sort((a, b) => a.date - b.date);
  }

  // 获取时间线的日期范围
  getDateRange() {
    if (this.milestones.length === 0) {
      return { startDate: null, endDate: null };
    }

    const dates = this.milestones.map(m => m.date).filter(Boolean);
    return {
      startDate: new Date(Math.min(...dates)),
      endDate: new Date(Math.max(...dates))
    };
  }

  // 获取已完成的里程碑数量
  getCompletedCount() {
    return this.milestones.filter(m => m.status === 'completed').length;
  }

  // 获取进度百分比
  getProgress() {
    if (this.milestones.length === 0) return 0;
    return this.getCompletedCount() / this.milestones.length;
  }

  // 检查是否有逾期里程碑
  hasOverdueMilestones() {
    return this.milestones.some(m => m.isOverdue());
  }

  // 计算时间线在任务中的Y坐标位置
  calculateYPosition(baseY, timelineIndex, rowHeight = 40) {
    if (this.collapsed) {
      return { y: baseY, visible: false };
    }
    
    // 基础Y坐标 + 时间线偏移 + 索引偏移
    const y = baseY + this.yOffset + (timelineIndex * this.height);
    return { y, visible: this.visible };
  }

  // 获取时间线的边界框
  getBounds(baseY, timelineIndex, rowHeight = 40) {
    const position = this.calculateYPosition(baseY, timelineIndex, rowHeight);
    if (!position.visible) {
      return null;
    }
    
    const dateRange = this.getDateRange();
    return {
      y: position.y,
      height: this.height,
      startDate: dateRange.startDate,
      endDate: dateRange.endDate
    };
  }

  // 切换折叠状态
  toggleCollapsed() {
    this.collapsed = !this.collapsed;
    return this.collapsed;
  }

  // 克隆时间线
  clone() {
    return new Timeline({
      ...this,
      id: this.generateId(),
      milestones: this.milestones.map(m => m.clone()),
      customFields: { ...this.customFields },
      metadata: { ...this.metadata },
      lineStyle: { ...this.lineStyle }
    });
  }

  // 转换为 JSON
  toJSON() {
    return {
      id: this.id,
      name: this.name,
      description: this.description,
      color: this.color,
      type: this.type,
      visible: this.visible,
      order: this.order,
      yOffset: this.yOffset,
      height: this.height,
      layer: this.layer,
      collapsed: this.collapsed,
      lineStyle: this.lineStyle,
      milestones: this.milestones.map(m => m.toJSON()),
      customFields: this.customFields,
      metadata: this.metadata
    };
  }
}

/**
 * 任务数据结构 - 增强版本
 * 增加多时间线Y轴布局管理功能
 */
class TaskItem {
  constructor(data = {}) {
    this.id = data.id || this.generateId();
    this.name = data.name || '';
    this.description = data.description || '';
    
    // 时间相关
    this.startDate = DateUtils.parseDate(data.startDate);
    this.endDate = DateUtils.parseDate(data.endDate);
    this.duration = data.duration || 0; // 天数
    this.progress = Math.max(0, Math.min(1, data.progress || 0)); // 0-1
    
    // 层级结构
    this.level = data.level || 0;
    this.parentId = data.parentId || null;
    this.children = data.children || [];
    this.collapsed = data.collapsed || false;
    
    // 状态和优先级
    this.status = data.status || 'pending'; // pending, in-progress, completed, cancelled, overdue
    this.priority = data.priority || 'normal'; // low, normal, high, critical
    this.type = data.type || 'task'; // task, milestone, summary, project
    
    // 资源分配
    this.assignee = data.assignee || '';
    this.team = data.team || [];
    this.resources = data.resources || [];
    
    // 多时间线支持 - 增强
    this.timelines = [];
    if (data.timelines && Array.isArray(data.timelines)) {
      this.timelines = data.timelines.map(t => 
        t instanceof Timeline ? t : new Timeline(t)
      );
    }
    
    // 兼容旧版本的里程碑数据
    if (data.milestones && Array.isArray(data.milestones) && this.timelines.length === 0) {
      const defaultTimeline = new Timeline({
        name: '主要里程碑',
        type: 'default',
        milestones: data.milestones
      });
      this.timelines.push(defaultTimeline);
    }
    
    // Y轴布局属性 - 新增
    this.rowHeight = data.rowHeight || 40; // 基础行高
    this.expandedHeight = data.expandedHeight || null; // 展开后的总高度，null表示自动计算
    this.timelineSpacing = data.timelineSpacing || 8; // 时间线之间的间距
    this.showTimelineLabels = data.showTimelineLabels !== false; // 是否显示时间线标签
    
    // 依赖关系
    this.dependencies = data.dependencies || []; // { id, type: 'FS'|'SS'|'FF'|'SF', lag: 0 }
    
    // 约束
    this.constraints = data.constraints || {}; // { type: 'ASAP'|'ALAP'|'MSO'|'MFO', date: null }
    
    // 扩展字段
    this.customFields = data.customFields || {};
    this.metadata = data.metadata || {};
    
    // 样式
    this.style = data.style || {};
    
    this.validate();
    this.calculateDuration();
    this.updateTimelineLayout();
  }

  validate() {
    if (!this.name) {
      throw new Error('Task name is required');
    }
    
    if (this.startDate && this.endDate && this.startDate > this.endDate) {
      throw new Error('Start date cannot be later than end date');
    }
  }

  generateId() {
    return `task-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  // 计算工期
  calculateDuration() {
    if (this.startDate && this.endDate) {
      this.duration = DateUtils.getDaysDiff(this.startDate, this.endDate) + 1;
    }
  }

  // 更新时间线布局
  updateTimelineLayout() {
    if (this.timelines.length === 0) return;

    // 按order排序时间线
    this.sortTimelines();

    let currentY = 0;
    this.timelines.forEach((timeline, index) => {
      if (timeline.visible && !timeline.collapsed) {
        timeline.yOffset = currentY;
        currentY += timeline.height + this.timelineSpacing;
      } else {
        timeline.yOffset = 0; // 隐藏或折叠的时间线
      }
    });

    // 计算展开后的总高度
    this.expandedHeight = Math.max(
      this.rowHeight,
      currentY - this.timelineSpacing + (this.showTimelineLabels ? 20 : 0)
    );
  }

  // 添加时间线
  addTimeline(timelineData) {
    const timeline = timelineData instanceof Timeline 
      ? timelineData 
      : new Timeline(timelineData);
    
    this.timelines.push(timeline);
    this.updateTimelineLayout();
    return timeline;
  }

  // 删除时间线
  removeTimeline(timelineId) {
    const index = this.timelines.findIndex(t => t.id === timelineId);
    if (index !== -1) {
      const removed = this.timelines.splice(index, 1)[0];
      this.updateTimelineLayout();
      return removed;
    }
    return null;
  }

  // 获取时间线
  getTimeline(timelineId) {
    return this.timelines.find(t => t.id === timelineId);
  }

  // 按顺序排序时间线
  sortTimelines() {
    this.timelines.sort((a, b) => {
      // 先按order排序，再按创建时间
      if (a.order !== b.order) {
        return a.order - b.order;
      }
      return a.id.localeCompare(b.id);
    });
  }

  // 获取可见时间线
  getVisibleTimelines() {
    return this.timelines.filter(t => t.visible && !t.collapsed);
  }

  // 获取时间线的渲染信息
  getTimelineRenderInfo(baseY = 0) {
    const renderInfo = [];
    
    this.timelines.forEach((timeline, index) => {
      if (!timeline.visible) return;
      
      const position = timeline.calculateYPosition(baseY, index, this.rowHeight);
      if (position.visible) {
        renderInfo.push({
          timeline,
          y: position.y,
          height: timeline.height,
          bounds: timeline.getBounds(baseY, index, this.rowHeight)
        });
      }
    });

    return renderInfo;
  }

  // 获取任务的实际渲染高度
  getRenderHeight() {
    if (this.collapsed || this.timelines.length === 0) {
      return this.rowHeight;
    }
    return this.expandedHeight || this.rowHeight;
  }

  // 获取所有里程碑（扁平化）
  getAllMilestones() {
    return this.timelines.reduce((all, timeline) => {
      if (timeline.visible && !timeline.collapsed) {
        return all.concat(timeline.milestones.map(m => ({
          ...m,
          timelineName: timeline.name,
          timelineColor: timeline.color,
          timelineY: timeline.yOffset
        })));
      }
      return all;
    }, []);
  }

  // 获取特定状态的里程碑
  getMilestonesByStatus(status) {
    return this.getAllMilestones().filter(m => m.status === status);
  }

  // 添加里程碑到指定时间线
  addMilestone(timelineId, milestoneData) {
    const timeline = this.getTimeline(timelineId);
    if (timeline) {
      return timeline.addMilestone(milestoneData);
    }
    throw new Error(`Timeline ${timelineId} not found`);
  }

  // 获取任务的整体进度（包含里程碑）
  getOverallProgress() {
    if (this.timelines.length === 0) {
      return this.progress;
    }
    
    // 考虑时间线进度
    const visibleTimelines = this.getVisibleTimelines();
    if (visibleTimelines.length === 0) {
      return this.progress;
    }
    
    const timelineProgress = visibleTimelines.reduce((sum, timeline) => {
      return sum + timeline.getProgress();
    }, 0) / visibleTimelines.length;
    
    // 任务进度和里程碑进度的加权平均
    return (this.progress * 0.7) + (timelineProgress * 0.3);
  }

  // 检查是否逾期
  isOverdue() {
    if (this.status === 'completed') return false;
    if (!this.endDate) return false;
    return this.endDate < DateUtils.today();
  }

  // 检查是否在关键路径上
  isCritical() {
    return this.priority === 'critical' || 
           this.metadata.critical === true ||
           this.timelines.some(t => t.type === 'critical');
  }

  // 添加依赖关系
  addDependency(taskId, type = 'FS', lag = 0) {
    const existing = this.dependencies.find(d => d.id === taskId);
    if (existing) {
      existing.type = type;
      existing.lag = lag;
    } else {
      this.dependencies.push({ id: taskId, type, lag });
    }
  }

  // 移除依赖关系
  removeDependency(taskId) {
    const index = this.dependencies.findIndex(d => d.id === taskId);
    if (index !== -1) {
      return this.dependencies.splice(index, 1)[0];
    }
    return null;
  }

  // 获取子任务数量
  getChildrenCount() {
    return this.children.length;
  }

  // 检查是否为父任务
  isParent() {
    return this.children.length > 0;
  }

  // 检查是否为叶子任务
  isLeaf() {
    return this.children.length === 0;
  }

  // 切换折叠状态
  toggleCollapsed() {
    this.collapsed = !this.collapsed;
    if (this.collapsed) {
      // 折叠时隐藏所有时间线
      this.timelines.forEach(t => t.collapsed = true);
    } else {
      // 展开时恢复时间线的原始状态
      this.timelines.forEach(t => t.collapsed = false);
    }
    this.updateTimelineLayout();
    return this.collapsed;
  }

  // 切换时间线显示
  toggleTimeline(timelineId) {
    const timeline = this.getTimeline(timelineId);
    if (timeline) {
      timeline.collapsed = !timeline.collapsed;
      this.updateTimelineLayout();
      return timeline.collapsed;
    }
    return false;
  }

  // 克隆任务
  clone() {
    const cloned = new TaskItem({
      ...this,
      id: this.generateId(),
      children: [...this.children],
      dependencies: this.dependencies.map(d => ({ ...d })),
      timelines: this.timelines.map(t => t.clone()),
      customFields: { ...this.customFields },
      metadata: { ...this.metadata },
      style: { ...this.style }
    });
    
    cloned.updateTimelineLayout();
    return cloned;
  }

  // 转换为 JSON
  toJSON() {
    return {
      id: this.id,
      name: this.name,
      description: this.description,
      startDate: DateUtils.formatDate(this.startDate),
      endDate: DateUtils.formatDate(this.endDate),
      duration: this.duration,
      progress: this.progress,
      level: this.level,
      parentId: this.parentId,
      children: this.children,
      collapsed: this.collapsed,
      status: this.status,
      priority: this.priority,
      type: this.type,
      assignee: this.assignee,
      team: this.team,
      resources: this.resources,
      timelines: this.timelines.map(t => t.toJSON()),
      rowHeight: this.rowHeight,
      expandedHeight: this.expandedHeight,
      timelineSpacing: this.timelineSpacing,
      showTimelineLabels: this.showTimelineLabels,
      dependencies: this.dependencies,
      constraints: this.constraints,
      customFields: this.customFields,
      metadata: this.metadata,
      style: this.style
    };
  }
}

/**
 * 多时间线布局管理器
 * 专门处理复杂的Y轴布局计算
 */
class TimelineLayoutManager {
  constructor(options = {}) {
    this.defaultRowHeight = options.defaultRowHeight || 40;
    this.timelineHeight = options.timelineHeight || 20;
    this.timelineSpacing = options.timelineSpacing || 8;
    this.labelHeight = options.labelHeight || 20;
  }

  /**
   * 计算任务列表的完整布局信息
   * @param {TaskItem[]} tasks - 任务列表
   * @returns {Object} 布局信息
   */
  calculateLayout(tasks) {
    const layoutInfo = {
      tasks: [],
      totalHeight: 0,
      maxRowHeight: this.defaultRowHeight
    };

    let currentY = 0;

    tasks.forEach(task => {
      const taskLayout = this.calculateTaskLayout(task, currentY);
      layoutInfo.tasks.push(taskLayout);
      
      currentY += taskLayout.height;
      layoutInfo.maxRowHeight = Math.max(layoutInfo.maxRowHeight, taskLayout.height);
    });

    layoutInfo.totalHeight = currentY;
    return layoutInfo;
  }

  /**
   * 计算单个任务的布局
   * @param {TaskItem} task - 任务对象
   * @param {number} baseY - 基础Y坐标
   * @returns {Object} 任务布局信息
   */
  calculateTaskLayout(task, baseY = 0) {
    const layout = {
      task,
      baseY,
      height: task.getRenderHeight(),
      timelines: []
    };

    if (task.collapsed || task.timelines.length === 0) {
      return layout;
    }

    // 计算每条时间线的布局
    let timelineY = baseY;
    
    task.getVisibleTimelines().forEach((timeline, index) => {
      const timelineLayout = {
        timeline,
        y: timelineY + timeline.yOffset,
        height: timeline.height,
        milestones: []
      };

      // 计算里程碑位置
      timeline.milestones.forEach(milestone => {
        timelineLayout.milestones.push({
          milestone,
          y: timelineLayout.y + (timeline.height / 2), // 里程碑位于时间线中心
          x: null // X坐标由时间轴计算器确定
        });
      });

      layout.timelines.push(timelineLayout);
    });

    return layout;
  }

  /**
   * 检查Y轴位置是否有重叠
   * @param {Object[]} layouts - 布局数组
   * @returns {boolean} 是否有重叠
   */
  checkOverlap(layouts) {
    for (let i = 0; i < layouts.length - 1; i++) {
      const current = layouts[i];
      const next = layouts[i + 1];
      
      if (current.baseY + current.height > next.baseY) {
        return true;
      }
    }
    return false;
  }

  /**
   * 优化时间线间距以避免重叠
   * @param {TaskItem} task - 任务对象
   */
  optimizeSpacing(task) {
    const visibleTimelines = task.getVisibleTimelines();
    if (visibleTimelines.length <= 1) return;

    // 计算最小间距
    const minSpacing = 5;
    let currentY = 0;

    visibleTimelines.forEach(timeline => {
      timeline.yOffset = currentY;
      currentY += timeline.height + Math.max(minSpacing, task.timelineSpacing);
    });

    task.updateTimelineLayout();
  }
}

/**
 * 数据验证器 - 增强版本
 * 增加多时间线布局验证
 */
class DataValidator {
  /**
   * 验证时间线数据
   * @param {object} timelineData - 时间线数据
   * @returns {object} 验证结果
   */
  static validateTimeline(timelineData) {
    const errors = [];
    const warnings = [];

    if (!timelineData.name || timelineData.name.trim() === '') {
      errors.push('时间线名称不能为空');
    }

    // 验证Y轴布局属性
    if (timelineData.yOffset < 0) {
      warnings.push('Y轴偏移量为负数，可能导致显示异常');
    }

    if (timelineData.height && timelineData.height < 10) {
      warnings.push('时间线高度过小，可能影响显示效果');
    }

    if (timelineData.milestones) {
      timelineData.milestones.forEach((milestone, index) => {
        const milestoneResult = DataValidator.validateMilestone(milestone);
        if (milestoneResult.errors.length > 0) {
          errors.push(`里程碑 ${index + 1}: ${milestoneResult.errors.join(', ')}`);
        }
        warnings.push(...milestoneResult.warnings.map(w => `里程碑 ${index + 1}: ${w}`));
      });
    }

    return { errors, warnings, valid: errors.length === 0 };
  }

  /**
   * 验证里程碑数据
   * @param {object} milestoneData - 里程碑数据
   * @returns {object} 验证结果
   */
  static validateMilestone(milestoneData) {
    const errors = [];
    const warnings = [];

    if (!milestoneData.name || milestoneData.name.trim() === '') {
      errors.push('里程碑名称不能为空');
    }

    if (!milestoneData.date) {
      errors.push('里程碑日期不能为空');
    } else {
      const date = DateUtils.parseDate(milestoneData.date);
      if (!date) {
        errors.push('里程碑日期格式无效');
      } else if (date < DateUtils.today() && milestoneData.status === 'pending') {
        warnings.push('里程碑日期已过期，建议更新状态');
      }
    }

    return { errors, warnings, valid: errors.length === 0 };
  }

  /**
   * 验证整个甘特图数据
   * @param {Array} data - 甘特图数据数组
   * @returns {object} 验证结果
   */
  static validateGanttData(data) {
    const errors = [];
    const warnings = [];
    const taskIds = new Set();

    if (!Array.isArray(data)) {
      errors.push('数据必须是数组格式');
      return { errors, warnings, valid: false };
    }

    data.forEach((task, index) => {
      // 检查ID唯一性
      if (taskIds.has(task.id)) {
        errors.push(`任务 ${index + 1}: ID "${task.id}" 重复`);
      } else {
        taskIds.add(task.id);
      }

      // 验证任务数据
      const taskResult = DataValidator.validateTask(task);
      if (taskResult.errors.length > 0) {
        errors.push(`任务 ${index + 1} (${task.name}): ${taskResult.errors.join(', ')}`);
      }
      warnings.push(...taskResult.warnings.map(w => `任务 ${index + 1} (${task.name}): ${w}`));
    });

    // 全局布局检查
    const layoutWarnings = DataValidator.validateGlobalLayout(data);
    warnings.push(...layoutWarnings);

    return { errors, warnings, valid: errors.length === 0 };
  }

  /**
   * 验证全局布局
   * @param {Array} data - 甘特图数据数组
   * @returns {string[]} 警告信息
   */
  static validateGlobalLayout(data) {
    const warnings = [];
    
    // 检查任务高度分布
    const taskHeights = data.map(task => {
      if (task.timelines && task.timelines.length > 0) {
        return task.expandedHeight || 40;
      }
      return task.rowHeight || 40;
    });

    const maxHeight = Math.max(...taskHeights);
    const minHeight = Math.min(...taskHeights);
    
    if (maxHeight / minHeight > 5) {
      warnings.push('任务高度差异过大，可能影响整体显示效果');
    }

    // 检查时间线数量分布
    const timelineCounts = data.map(task => task.timelines ? task.timelines.length : 0);
    const maxTimelines = Math.max(...timelineCounts);
    
    if (maxTimelines > 10) {
      warnings.push('某些任务包含过多时间线，可能影响性能和可读性');
    }

    return warnings;
  }
}

/**
 * 数据转换器 - 增强版本
 * 支持多时间线布局的数据转换
 */
class DataTransformer {
  /**
   * 将旧版本数据转换为新版本格式
   * @param {Array} oldData - 旧版本数据
   * @returns {Array} 新版本数据
   */
  static migrateFromV1(oldData) {
    if (!Array.isArray(oldData)) return [];

    return oldData.map(task => {
      const newTask = { ...task };
      
      // 转换里程碑为时间线格式
      if (task.milestones && Array.isArray(task.milestones)) {
        newTask.timelines = [{
          name: '主要里程碑',
          type: 'default',
          visible: true,
          order: 0,
          yOffset: 0,
          height: 20,
          milestones: task.milestones.map(m => ({
            ...m,
            id: m.id || `milestone-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
          }))
        }];
        delete newTask.milestones;
      }

      // 设置默认的布局属性
      newTask.rowHeight = newTask.rowHeight || 40;
      newTask.timelineSpacing = newTask.timelineSpacing || 8;
      newTask.showTimelineLabels = newTask.showTimelineLabels !== false;

      return newTask;
    });
  }

  /**
   * 优化多时间线布局
   * @param {Array} data - 甘特图数据
   * @returns {Array} 优化后的数据
   */
  static optimizeTimelineLayout(data) {
    const layoutManager = new TimelineLayoutManager();
    
    return data.map(taskData => {
      const task = new TaskItem(taskData);
      
      // 自动优化时间线间距
      if (task.timelines.length > 1) {
        layoutManager.optimizeSpacing(task);
      }
      
      return task.toJSON();
    });
  }

  /**
   * 导出为标准 JSON 格式
   * @param {Array} data - 甘特图数据
   * @returns {object} 标准格式数据
   */
  static exportToStandard(data) {
    return {
      version: '2.1',
      exportTime: new Date().toISOString(),
      features: ['multiTimeline', 'yAxisLayout', 'milestoneChains'],
      tasks: data.map(task => {
        if (task instanceof TaskItem) {
          return task.toJSON();
        }
        return task;
      })
    };
  }

  /**
   * 从标准格式导入
   * @param {object} standardData - 标准格式数据
   * @returns {Array} 任务数组
   */
  static importFromStandard(standardData) {
    if (!standardData.tasks) {
      throw new Error('Invalid standard format: missing tasks');
    }

    // 检查版本兼容性
    if (standardData.version && parseFloat(standardData.version) < 2.0) {
      // 需要版本升级
      return DataTransformer.migrateFromV1(standardData.tasks).map(taskData => new TaskItem(taskData));
    }

    return standardData.tasks.map(taskData => new TaskItem(taskData));
  }

  /**
   * 导出时间线统计信息
   * @param {Array} data - 甘特图数据
   * @returns {object} 统计信息
   */
  static exportTimelineStatistics(data) {
    const stats = {
      totalTasks: data.length,
      totalTimelines: 0,
      totalMilestones: 0,
      timelineTypes: {},
      milestoneTypes: {},
      averageTimelinesPerTask: 0,
      averageMilestonesPerTimeline: 0,
      maxTaskHeight: 0,
      layoutComplexity: 'simple'
    };

    data.forEach(task => {
      if (task.timelines && task.timelines.length > 0) {
        stats.totalTimelines += task.timelines.length;
        
        task.timelines.forEach(timeline => {
          // 统计时间线类型
          stats.timelineTypes[timeline.type] = (stats.timelineTypes[timeline.type] || 0) + 1;
          
          if (timeline.milestones && timeline.milestones.length > 0) {
            stats.totalMilestones += timeline.milestones.length;
            
            timeline.milestones.forEach(milestone => {
              // 统计里程碑类型
              stats.milestoneTypes[milestone.type] = (stats.milestoneTypes[milestone.type] || 0) + 1;
            });
          }
        });
        
        // 计算任务高度
        const taskHeight = task.expandedHeight || task.rowHeight || 40;
        stats.maxTaskHeight = Math.max(stats.maxTaskHeight, taskHeight);
      }
    });

    // 计算平均值
    if (stats.totalTasks > 0) {
      stats.averageTimelinesPerTask = stats.totalTimelines / stats.totalTasks;
    }
    
    if (stats.totalTimelines > 0) {
      stats.averageMilestonesPerTimeline = stats.totalMilestones / stats.totalTimelines;
    }

    // 评估布局复杂度
    if (stats.averageTimelinesPerTask > 5 || stats.maxTaskHeight > 200) {
      stats.layoutComplexity = 'complex';
    } else if (stats.averageTimelinesPerTask > 2 || stats.maxTaskHeight > 80) {
      stats.layoutComplexity = 'medium';
    }

    return stats;
  }

  /**
   * 生成示例数据
   * @param {number} taskCount - 任务数量
   * @param {number} maxTimelines - 每个任务最大时间线数
   * @returns {Array} 示例数据
   */
  static generateSampleData(taskCount = 10, maxTimelines = 3) {
    const tasks = [];
    const today = new Date();
    
    for (let i = 0; i < taskCount; i++) {
      const startDate = new Date(today.getTime() + i * 7 * 24 * 60 * 60 * 1000);
      const endDate = new Date(startDate.getTime() + (10 + i * 3) * 24 * 60 * 60 * 1000);
      
      const timelineCount = Math.floor(Math.random() * maxTimelines) + 1;
      const timelines = [];
      
      for (let j = 0; j < timelineCount; j++) {
        const timelineTypes = ['technical', 'business', 'delivery'];
        const milestoneTypes = ['review', 'delivery', 'approval'];
        
        const milestones = [];
        const milestoneCount = Math.floor(Math.random() * 4) + 1;
        
        for (let k = 0; k < milestoneCount; k++) {
          const milestoneDate = new Date(
            startDate.getTime() + 
            (k + 1) * (endDate - startDate) / (milestoneCount + 1)
          );
          
          milestones.push({
            name: `里程碑 ${j + 1}-${k + 1}`,
            date: milestoneDate,
            type: milestoneTypes[Math.floor(Math.random() * milestoneTypes.length)],
            status: Math.random() > 0.3 ? 'completed' : 'pending'
          });
        }
        
        timelines.push({
          name: `时间线 ${j + 1}`,
          type: timelineTypes[j % timelineTypes.length],
          order: j,
          milestones: milestones
        });
      }
      
      tasks.push({
        name: `任务 ${i + 1}`,
        startDate: startDate,
        endDate: endDate,
        progress: Math.random(),
        status: Math.random() > 0.2 ? 'in-progress' : 'completed',
        priority: ['normal', 'high', 'critical'][Math.floor(Math.random() * 3)],
        assignee: `用户${i + 1}`,
        timelines: timelines
      });
    }
    
    return tasks;
  }
}
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>甘特图测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            padding: 20px;
        }
        
        h1 {
            color: #333;
            margin-bottom: 20px;
            text-align: center;
        }
        
        #ganttChart {
            width: 100%;
            height: 600px;
            border: 1px solid #ddd;
            border-radius: 4px;
            overflow: auto;
            position: relative;
            background-color: #fafafa;
        }
        
        #ganttSvg {
            display: block;
            width: 100%;
            height: 100%;
        }
        
        .grid-line {
            stroke: #e0e0e0;
            stroke-width: 1;
        }
        
        .time-tick {
            stroke: #ccc;
            stroke-width: 1;
        }
        
        .time-label {
            font-size: 12px;
            fill: #666;
            text-anchor: middle;
        }
        
        .time-axis-line {
            stroke: #333;
            stroke-width: 2;
        }
        
        .task-label {
            font-size: 12px;
            fill: #333;
            text-anchor: start;
        }
        
        .task-bar {
            cursor: pointer;
        }
        
        .time-line {
            stroke: #666;
            stroke-width: 2;
            fill: none;
        }
        
        .time-point {
            cursor: pointer;
        }
        
        .time-point.start {
            fill: #28a745;
        }
        
        .time-point.checkpoint {
            fill: #0366d6;
        }
        
        .time-point.end {
            fill: #dc3545;
        }
        
        .time-point.milestone {
            fill: #f66a0a;
        }
        
        .dependency-line {
            stroke: #586069;
            stroke-width: 2;
            fill: none;
            marker-end: url(#arrowhead);
        }
        
        .tooltip {
            position: absolute;
            background-color: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 12px;
            pointer-events: none;
            z-index: 1000;
            display: none;
        }
        
        .controls {
            margin-bottom: 20px;
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            align-items: center;
        }
        
        .controls button {
            padding: 8px 16px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: #fff;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        
        .controls button:hover {
            background-color: #f0f0f0;
        }
        
        .controls button.active {
            background-color: #4CAF50;
            color: white;
            border-color: #4CAF50;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>甘特图测试</h1>
        
        <div class="controls">
            <button id="loadBuiltinBtn">加载内置数据</button>
            <button id="loadSampleBtn">加载示例项目</button>
            <button id="loadSimpleBtn">加载简单项目</button>
            <button id="toggleMilestonesBtn">显示里程碑</button>
            <button id="viewMonthBtn" class="active">月视图</button>
            <button id="viewWeekBtn">周视图</button>
            <button id="viewDayBtn">日视图</button>
        </div>
        
        <div id="ganttChart">
            <svg id="ganttSvg" width="1200" height="600"></svg>
        </div>
        
        <div class="tooltip" id="tooltip"></div>
    </div>
    
    <script src="GanttChart.js"></script>
    
    <script>
        // 初始化甘特图
        const gantt = new GanttChart('ganttChart');
        
        // 加载内置数据
        document.getElementById('loadBuiltinBtn').addEventListener('click', () => {
            gantt.loadBuiltInSampleData();
        });
        
        // 加载示例项目
        document.getElementById('loadSampleBtn').addEventListener('click', async () => {
            try {
                const result = await gantt.loadFromJSONFile('data/sample-project.json');
                if (result.success) {
                    console.log('加载项目:', result.metadata.name);
                } else {
                    console.error('加载失败:', result.error);
                }
            } catch (error) {
                console.error('加载失败:', error);
            }
        });
        
        // 加载简单项目
        document.getElementById('loadSimpleBtn').addEventListener('click', async () => {
            try {
                const result = await gantt.loadFromJSONFile('data/simple-project.json');
                if (result.success) {
                    console.log('加载项目:', result.metadata.name);
                } else {
                    console.error('加载失败:', result.error);
                }
            } catch (error) {
                console.error('加载失败:', error);
            }
        });
        
        // 里程碑切换
        document.getElementById('toggleMilestonesBtn').addEventListener('click', () => {
            gantt.config.showMilestones = !gantt.config.showMilestones;
            gantt.render();
        });
        
        // 视图切换
        const viewButtons = {
            month: document.getElementById('viewMonthBtn'),
            week: document.getElementById('viewWeekBtn'),
            day: document.getElementById('viewDayBtn')
        };
        
        Object.entries(viewButtons).forEach(([view, button]) => {
            button.addEventListener('click', () => {
                Object.values(viewButtons).forEach(btn => btn.classList.remove('active'));
                button.classList.add('active');
                gantt.config.viewMode = view;
                gantt.updatePixelPerDay();
                gantt.render();
            });
        });
    </script>
</body>
</html> 
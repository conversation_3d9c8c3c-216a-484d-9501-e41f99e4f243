# 甘特图组件设计方案

## 1. 整体架构设计

### 1.1 核心模块划分
```
GanttChart
├── DataManager (数据管理层)
├── TimelineRenderer (时间轴渲染)
├── TaskRenderer (任务渲染)
├── MilestoneRenderer (里程碑渲染)
├── TableRenderer (表格渲染)
├── VirtualScroller (虚拟滚动)
├── InteractionManager (交互管理)
└── ConfigManager (配置管理)
```

### 1.2 数据结构设计

```javascript
// 任务数据结构
const TaskItem = {
  id: 'task-1',
  name: '任务名称',
  startDate: '2024-01-01',
  endDate: '2024-01-15',
  duration: 15, // 天数
  progress: 0.6, // 进度 0-1
  level: 0, // 层级，用于缩进
  parent: null, // 父任务ID
  children: [], // 子任务ID数组
  
  // 扩展属性
  customFields: {
    assignee: '张三',
    priority: 'high',
    department: '研发部',
    budget: 50000
  },
  
  // 里程碑数据
  milestones: [
    {
      id: 'milestone-1',
      name: '需求评审',
      date: '2024-01-03',
      type: 'review', // review, delivery, approval
      status: 'completed', // pending, completed, overdue
      description: '完成需求文档评审'
    },
    {
      id: 'milestone-2', 
      name: '原型交付',
      date: '2024-01-10',
      type: 'delivery',
      status: 'pending'
    }
  ]
};
```

## 2. 核心功能实现

### 2.1 时间轴系统
```javascript
class TimeScale {
  constructor(startDate, endDate, viewMode = 'day') {
    this.startDate = new Date(startDate);
    this.endDate = new Date(endDate);
    this.viewMode = viewMode; // day, week, month, quarter
    this.pixelsPerUnit = this.calculatePixelsPerUnit();
  }
  
  // 计算时间单位对应的像素宽度
  calculatePixelsPerUnit() {
    const modes = {
      day: 30,
      week: 100, 
      month: 120,
      quarter: 300
    };
    return modes[this.viewMode];
  }
  
  // 日期转换为X坐标
  dateToX(date) {
    const diffTime = new Date(date) - this.startDate;
    const diffDays = diffTime / (1000 * 60 * 60 * 24);
    return diffDays * this.pixelsPerUnit;
  }
  
  // X坐标转换为日期
  xToDate(x) {
    const days = x / this.pixelsPerUnit;
    return new Date(this.startDate.getTime() + days * 24 * 60 * 60 * 1000);
  }
}
```

### 2.2 虚拟化滚动实现
```javascript
class VirtualScroller {
  constructor(container, itemHeight = 40) {
    this.container = container;
    this.itemHeight = itemHeight;
    this.viewportHeight = container.clientHeight;
    this.visibleCount = Math.ceil(this.viewportHeight / itemHeight) + 2; // 缓冲区
    this.scrollTop = 0;
    this.totalItems = 0;
  }
  
  // 计算可见区域的项目范围
  getVisibleRange() {
    const startIndex = Math.floor(this.scrollTop / this.itemHeight);
    const endIndex = Math.min(startIndex + this.visibleCount, this.totalItems);
    return { startIndex, endIndex };
  }
  
  // 更新滚动位置
  updateScroll(scrollTop) {
    this.scrollTop = scrollTop;
    this.render();
  }
  
  // 计算项目的Y坐标
  getItemY(index) {
    return index * this.itemHeight;
  }
}
```

### 2.3 任务渲染器
```javascript
class TaskRenderer {
  constructor(svg, timeScale, virtualScroller) {
    this.svg = svg;
    this.timeScale = timeScale;
    this.virtualScroller = virtualScroller;
    this.taskGroup = this.createGroup('tasks');
  }
  
  // 渲染任务条
  renderTask(task, yPosition) {
    const x = this.timeScale.dateToX(task.startDate);
    const width = this.timeScale.dateToX(task.endDate) - x;
    
    // 创建任务组
    const taskGroup = this.createSVGElement('g', {
      'data-task-id': task.id,
      transform: `translate(${x}, ${yPosition})`
    });
    
    // 主任务条
    const rect = this.createSVGElement('rect', {
      width: width,
      height: 24,
      fill: this.getTaskColor(task),
      rx: 4,
      class: 'task-bar'
    });
    
    // 进度条
    const progressRect = this.createSVGElement('rect', {
      width: width * task.progress,
      height: 24,
      fill: this.getProgressColor(task),
      rx: 4,
      class: 'task-progress'
    });
    
    taskGroup.appendChild(rect);
    taskGroup.appendChild(progressRect);
    
    return taskGroup;
  }
  
  getTaskColor(task) {
    const colors = {
      normal: '#4A90E2',
      critical: '#E24A4A',
      completed: '#7ED321'
    };
    return colors[task.status] || colors.normal;
  }
}
```

### 2.4 里程碑渲染器
```javascript
class MilestoneRenderer {
  constructor(svg, timeScale) {
    this.svg = svg;
    this.timeScale = timeScale;
    this.milestoneGroup = this.createGroup('milestones');
  }
  
  // 渲染里程碑
  renderMilestone(milestone, yPosition, taskId) {
    const x = this.timeScale.dateToX(milestone.date);
    
    const milestoneGroup = this.createSVGElement('g', {
      'data-milestone-id': milestone.id,
      'data-task-id': taskId,
      transform: `translate(${x}, ${yPosition})`
    });
    
    // 里程碑菱形标记
    const diamond = this.createSVGElement('polygon', {
      points: '0,-8 8,0 0,8 -8,0',
      fill: this.getMilestoneColor(milestone),
      stroke: '#333',
      'stroke-width': 1,
      class: 'milestone-marker'
    });
    
    // 里程碑连接线
    const line = this.createSVGElement('line', {
      x1: 0,
      y1: 8,
      x2: 0,
      y2: 24,
      stroke: '#666',
      'stroke-width': 1,
      'stroke-dasharray': '2,2',
      class: 'milestone-line'
    });
    
    milestoneGroup.appendChild(diamond);
    milestoneGroup.appendChild(line);
    
    return milestoneGroup;
  }
  
  getMilestoneColor(milestone) {
    const colors = {
      review: '#FF9500',
      delivery: '#007AFF',
      approval: '#34C759'
    };
    return colors[milestone.type] || colors.review;
  }
}
```

### 2.5 表格渲染器
```javascript
class TableRenderer {
  constructor(container, columns, virtualScroller) {
    this.container = container;
    this.columns = columns;
    this.virtualScroller = virtualScroller;
    this.table = this.createTable();
  }
  
  // 创建表格结构
  createTable() {
    const table = document.createElement('div');
    table.className = 'gantt-table';
    
    // 表头
    const header = this.createHeader();
    table.appendChild(header);
    
    // 表体容器
    const body = document.createElement('div');
    body.className = 'gantt-table-body';
    table.appendChild(body);
    
    this.container.appendChild(table);
    return table;
  }
  
  // 渲染表格行
  renderRow(task, index) {
    const row = document.createElement('div');
    row.className = 'gantt-table-row';
    row.style.top = `${this.virtualScroller.getItemY(index)}px`;
    row.dataset.taskId = task.id;
    
    // 渲染各列
    this.columns.forEach(column => {
      const cell = document.createElement('div');
      cell.className = 'gantt-table-cell';
      cell.style.width = `${column.width}px`;
      
      if (column.key === 'name') {
        // 任务名称列，支持层级缩进
        const indent = task.level * 20;
        cell.style.paddingLeft = `${indent + 8}px`;
        cell.textContent = task.name;
      } else if (column.key.startsWith('custom.')) {
        // 自定义字段
        const fieldKey = column.key.replace('custom.', '');
        cell.textContent = task.customFields[fieldKey] || '';
      } else {
        // 标准字段
        cell.textContent = task[column.key] || '';
      }
      
      row.appendChild(cell);
    });
    
    return row;
  }
}
```

## 3. 主组件集成

### 3.1 甘特图主类
```javascript
class GanttChart {
  constructor(container, options = {}) {
    this.container = container;
    this.options = this.mergeOptions(options);
    this.data = [];
    this.filteredData = [];
    
    this.init();
  }
  
  init() {
    this.createLayout();
    this.initializeComponents();
    this.bindEvents();
  }
  
  // 创建布局
  createLayout() {
    this.container.innerHTML = `
      <div class="gantt-container">
        <div class="gantt-left-panel">
          <div class="gantt-table-container"></div>
        </div>
        <div class="gantt-right-panel">
          <div class="gantt-timeline-header"></div>
          <div class="gantt-chart-container">
            <svg class="gantt-svg"></svg>
          </div>
        </div>
      </div>
    `;
  }
  
  // 初始化各组件
  initializeComponents() {
    this.timeScale = new TimeScale(
      this.options.startDate,
      this.options.endDate,
      this.options.viewMode
    );
    
    this.virtualScroller = new VirtualScroller(
      this.container.querySelector('.gantt-chart-container'),
      this.options.rowHeight
    );
    
    this.tableRenderer = new TableRenderer(
      this.container.querySelector('.gantt-table-container'),
      this.options.columns,
      this.virtualScroller
    );
    
    const svg = this.container.querySelector('.gantt-svg');
    this.taskRenderer = new TaskRenderer(svg, this.timeScale, this.virtualScroller);
    this.milestoneRenderer = new MilestoneRenderer(svg, this.timeScale);
  }
  
  // 设置数据
  setData(data) {
    this.data = data;
    this.filteredData = this.processData(data);
    this.virtualScroller.totalItems = this.filteredData.length;
    this.render();
  }
  
  // 主渲染方法
  render() {
    const { startIndex, endIndex } = this.virtualScroller.getVisibleRange();
    const visibleTasks = this.filteredData.slice(startIndex, endIndex);
    
    // 清除之前的渲染
    this.clearRendered();
    
    // 渲染可见任务
    visibleTasks.forEach((task, i) => {
      const actualIndex = startIndex + i;
      const yPosition = this.virtualScroller.getItemY(actualIndex);
      
      // 渲染表格行
      const tableRow = this.tableRenderer.renderRow(task, actualIndex);
      
      // 渲染任务条
      const taskElement = this.taskRenderer.renderTask(task, yPosition);
      
      // 渲染里程碑
      task.milestones?.forEach(milestone => {
        const milestoneElement = this.milestoneRenderer.renderMilestone(
          milestone, 
          yPosition, 
          task.id
        );
      });
    });
  }
}
```

## 4. 性能优化策略

### 4.1 虚拟化优化
- **行虚拟化**: 只渲染可见区域的任务行
- **列虚拟化**: 对于大量自定义字段，实现列的虚拟滚动
- **时间轴虚拟化**: 超长时间跨度时，只渲染可见时间范围

### 4.2 渲染优化
- **批量更新**: 使用 `requestAnimationFrame` 批量处理 DOM 更新
- **对象池**: 复用 SVG 元素，减少创建/销毁开销
- **增量渲染**: 只更新变化的部分

### 4.3 数据优化
- **数据扁平化**: 预处理层级数据为扁平结构
- **索引构建**: 为快速查找构建 ID 映射表
- **懒加载**: 大数据集分批加载

## 5. 交互功能设计

### 5.1 基础交互
- 任务拖拽调整时间
- 进度条拖拽调整进度
- 里程碑拖拽调整日期
- 双击编辑任务信息

### 5.2 高级交互
- 任务依赖关系连线
- 批量操作选择
- 右键菜单
- 快捷键支持

## 6. 扩展性考虑

### 6.1 插件系统
```javascript
class PluginManager {
  constructor(gantt) {
    this.gantt = gantt;
    this.plugins = new Map();
  }
  
  register(name, plugin) {
    this.plugins.set(name, plugin);
    plugin.install(this.gantt);
  }
  
  unregister(name) {
    const plugin = this.plugins.get(name);
    if (plugin) {
      plugin.uninstall(this.gantt);
      this.plugins.delete(name);
    }
  }
}
```

### 6.2 主题系统
- CSS 变量支持
- 动态主题切换
- 自定义颜色方案

### 6.3 国际化支持
- 多语言文本
- 日期格式本地化
- RTL 布局支持

这个设计方案提供了完整的甘特图功能，特别强化了里程碑展示和虚拟化性能，可以作为实现的基础架构。
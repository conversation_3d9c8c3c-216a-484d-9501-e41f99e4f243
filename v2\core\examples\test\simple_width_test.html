<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单宽度测试</title>
    <link rel="stylesheet" href="../../src/styles/gantt.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .gantt-container {
            height: 400px;
            border: 2px solid #007bff;
            margin: 20px 0;
        }
        .info {
            background: #e7f3ff;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .controls {
            margin: 15px 0;
        }
        .btn {
            padding: 8px 16px;
            margin: 5px;
            border: 1px solid #007bff;
            background: white;
            color: #007bff;
            border-radius: 4px;
            cursor: pointer;
        }
        .btn:hover {
            background: #007bff;
            color: white;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>甘特图宽度修复测试</h1>
        
        <div class="info">
            <strong>测试目标：</strong>验证甘特图横向渲染是否完整，所有任务数据是否都能正确显示
        </div>

        <div class="controls">
            <button class="btn" onclick="checkWidths()">检查宽度</button>
            <button class="btn" onclick="scrollToEnd()">滚动到末尾</button>
            <button class="btn" onclick="zoomTest()">缩放测试</button>
        </div>

        <div class="gantt-container" id="ganttContainer"></div>

        <div class="info" id="results">
            等待初始化...
        </div>
    </div>

    <script type="module">
        import GanttChart from '../../src/GanttChart.js';
        import DateUtils from '../../src/utils/DateUtils.js';

        let ganttInstance = null;

        // 生成测试数据
        function generateTestData() {
            const tasks = [];
            const startDate = new Date('2024-01-01');
            
            // 生成20个任务，跨度较大
            for (let i = 0; i < 20; i++) {
                const taskStart = DateUtils.addDays(startDate, i * 10);
                const duration = 15 + Math.random() * 20;
                const taskEnd = DateUtils.addDays(taskStart, duration);
                
                tasks.push({
                    id: `test-task-${i}`,
                    name: `测试任务 ${i + 1}`,
                    startDate: taskStart,
                    endDate: taskEnd,
                    progress: Math.random()
                });
            }
            
            return tasks;
        }

        function initGantt() {
            const testData = generateTestData();
            
            ganttInstance = new GanttChart('ganttContainer', {
                data: testData,
                viewMode: 'day',
                pixelsPerDay: 30,
                taskList: {
                    columns: [
                        { key: 'name', title: '任务名称', width: 150 }
                    ]
                }
            });

            ganttInstance.on('ready', () => {
                updateResults('甘特图初始化完成');
                checkWidths();
            });
        }

        function updateResults(message) {
            document.getElementById('results').innerHTML = message;
        }

        window.checkWidths = function() {
            if (!ganttInstance) return;
            
            const timeScale = ganttInstance.timeScale;
            const svg = ganttInstance.elements.svg;
            const chartBody = ganttInstance.elements.chartBody;
            
            const totalWidth = timeScale ? timeScale.getTotalWidth() : 0;
            const svgWidth = svg ? svg.style.width : 'N/A';
            const scrollWidth = chartBody ? chartBody.scrollWidth : 0;
            const clientWidth = chartBody ? chartBody.clientWidth : 0;
            
            const results = `
                <strong>宽度检查结果：</strong><br>
                时间轴总宽度: ${totalWidth}px<br>
                SVG宽度: ${svgWidth}<br>
                滚动容器宽度: ${scrollWidth}px<br>
                可视区域宽度: ${clientWidth}px<br>
                <strong>状态:</strong> ${totalWidth > 0 && scrollWidth >= totalWidth ? '✅ 正常' : '❌ 异常'}
            `;
            
            updateResults(results);
            console.log('宽度检查:', { totalWidth, svgWidth, scrollWidth, clientWidth });
        };

        window.scrollToEnd = function() {
            if (!ganttInstance) return;
            
            const chartBody = ganttInstance.elements.chartBody;
            if (chartBody) {
                chartBody.scrollLeft = chartBody.scrollWidth - chartBody.clientWidth;
                updateResults(`滚动到末尾: ${chartBody.scrollLeft}px`);
            }
        };

        window.zoomTest = function() {
            if (!ganttInstance) return;
            
            ganttInstance.zoomIn();
            setTimeout(() => {
                checkWidths();
            }, 300);
        };

        // 初始化
        document.addEventListener('DOMContentLoaded', () => {
            initGantt();
        });
    </script>
</body>
</html>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Step3: 头部高度对齐测试</title>
    <link rel="stylesheet" href="../../src/styles/gantt.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }

        .header h1 {
            margin: 0;
            font-size: 24px;
        }

        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
        }

        .test-info {
            padding: 20px;
            background: #fef3c7;
            border-bottom: 1px solid #f59e0b;
        }

        .test-info h3 {
            margin: 0 0 10px 0;
            color: #92400e;
        }

        .test-info p {
            margin: 0;
            color: #92400e;
            line-height: 1.5;
        }

        .gantt-container {
            height: 600px;
            position: relative;
        }

        .alignment-indicators {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            pointer-events: none;
            z-index: 1000;
        }

        .alignment-line {
            position: absolute;
            left: 0;
            right: 0;
            height: 2px;
            background: #f59e0b;
            opacity: 0.8;
        }

        .alignment-line.top {
            top: 0;
        }

        .alignment-line.bottom {
            top: calc(var(--gantt-row-height) * 1.5 - 2px);
        }

        .alignment-label {
            position: absolute;
            right: 10px;
            background: #f59e0b;
            color: white;
            padding: 2px 8px;
            border-radius: 3px;
            font-size: 12px;
            font-weight: 600;
        }

        /* 增强头部边框以便观察对齐 */
        .gantt-table-header {
            border: 3px solid #f59e0b !important;
            box-shadow: inset 0 0 0 1px rgba(245, 158, 11, 0.3) !important;
        }

        .gantt-timeline-header {
            border: 3px solid #f59e0b !important;
            box-shadow: inset 0 0 0 1px rgba(245, 158, 11, 0.3) !important;
        }

        .gantt-table-header-cell {
            background: rgba(245, 158, 11, 0.1) !important;
            border-right: 2px solid #f59e0b !important;
        }

        .timeline-scales-top-row {
            background: rgba(245, 158, 11, 0.1) !important;
        }

        .timeline-scales-bottom-row {
            background: rgba(245, 158, 11, 0.05) !important;
        }

        .measurement-panel {
            padding: 20px;
            background: #f8f9fa;
            border-top: 1px solid #eee;
        }

        .measurement-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .measurement-item {
            background: white;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid #f59e0b;
        }

        .measurement-label {
            font-size: 12px;
            color: #666;
            text-transform: uppercase;
            margin-bottom: 5px;
        }

        .measurement-value {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            font-family: monospace;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 10px;
        }

        .status-indicator.aligned {
            background: #10b981;
        }

        .status-indicator.misaligned {
            background: #ef4444;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📏 头部高度对齐测试</h1>
            <p>验证任务列表头部与时间轴头部的高度是否完美对齐</p>
        </div>

        <div class="test-info">
            <h3>测试说明</h3>
            <p>
                此页面用于验证任务列表头部（左侧）与时间轴头部（右侧）的高度对齐。
                两个头部都应该具有相同的高度（1.5倍行高），并且在视觉上完美对齐。
                橙色边框和对齐线帮助您观察对齐效果。
            </p>
        </div>

        <div class="gantt-container" id="ganttContainer">
            <div class="alignment-indicators">
                <div class="alignment-line top">
                    <div class="alignment-label">头部顶部</div>
                </div>
                <div class="alignment-line bottom">
                    <div class="alignment-label">头部底部</div>
                </div>
            </div>
        </div>

        <div class="measurement-panel">
            <h3>高度测量结果</h3>
            <div class="measurement-grid">
                <div class="measurement-item">
                    <div class="measurement-label">任务列表头部高度</div>
                    <div class="measurement-value" id="tableHeaderHeight">-</div>
                </div>
                <div class="measurement-item">
                    <div class="measurement-label">时间轴头部高度</div>
                    <div class="measurement-value" id="timelineHeaderHeight">-</div>
                </div>
                <div class="measurement-item">
                    <div class="measurement-label">高度差异</div>
                    <div class="measurement-value" id="heightDifference">-</div>
                </div>
                <div class="measurement-item">
                    <div class="measurement-label">对齐状态</div>
                    <div class="measurement-value" id="alignmentStatus">
                        检测中... <span class="status-indicator" id="statusIndicator"></span>
                    </div>
                </div>
                <div class="measurement-item">
                    <div class="measurement-label">顶部位置差异</div>
                    <div class="measurement-value" id="topPositionDiff">-</div>
                </div>
                <div class="measurement-item">
                    <div class="measurement-label">底部位置差异</div>
                    <div class="measurement-value" id="bottomPositionDiff">-</div>
                </div>
            </div>
        </div>
    </div>

    <script type="module">
        import GanttChart from '../../src/GanttChart.js';
        import DateUtils from '../../src/utils/DateUtils.js';

        // 生成测试数据
        function generateTestData() {
            const tasks = [];
            const startDate = new Date('2024-01-01');
            
            for (let i = 0; i < 15; i++) {
                const taskStart = DateUtils.addDays(startDate, i * 10);
                const duration = 5 + Math.random() * 10;
                const taskEnd = DateUtils.addDays(taskStart, duration);
                
                tasks.push({
                    id: `alignment-test-${i}`,
                    name: `对齐测试任务 ${i + 1}`,
                    startDate: taskStart,
                    endDate: taskEnd,
                    progress: Math.random(),
                    status: ['pending', 'in-progress', 'completed'][Math.floor(Math.random() * 3)]
                });
            }
            
            return tasks;
        }

        // 初始化甘特图
        let ganttInstance = null;

        function initializeGantt() {
            const container = document.getElementById('ganttContainer');
            
            const testData = generateTestData();
            
            ganttInstance = new GanttChart('ganttContainer', {
                data: testData,
                viewMode: 'day',
                pixelsPerDay: 35,
                taskList: {
                    columns: [
                        { key: 'name', label: '任务名称', width: 220 },
                        { key: 'startDate', label: '开始日期', width: 100 },
                        { key: 'endDate', label: '结束日期', width: 100 },
                        { key: 'progress', label: '进度', width: 80 }
                    ]
                }
            });

            ganttInstance.on('ready', () => {
                console.log('甘特图初始化完成');
                setTimeout(measureAlignment, 500); // 延迟测量以确保渲染完成
            });
        }

        // 测量头部对齐
        function measureAlignment() {
            const tableHeader = document.querySelector('.gantt-table-header');
            const timelineHeader = document.querySelector('.gantt-timeline-header');

            if (!tableHeader || !timelineHeader) {
                console.error('找不到头部元素');
                return;
            }

            const tableRect = tableHeader.getBoundingClientRect();
            const timelineRect = timelineHeader.getBoundingClientRect();

            const tableHeight = Math.round(tableRect.height);
            const timelineHeight = Math.round(timelineRect.height);
            const heightDiff = Math.abs(tableHeight - timelineHeight);
            const topDiff = Math.abs(tableRect.top - timelineRect.top);
            const bottomDiff = Math.abs(tableRect.bottom - timelineRect.bottom);

            // 更新显示
            document.getElementById('tableHeaderHeight').textContent = `${tableHeight}px`;
            document.getElementById('timelineHeaderHeight').textContent = `${timelineHeight}px`;
            document.getElementById('heightDifference').textContent = `${heightDiff}px`;
            document.getElementById('topPositionDiff').textContent = `${topDiff.toFixed(1)}px`;
            document.getElementById('bottomPositionDiff').textContent = `${bottomDiff.toFixed(1)}px`;

            // 判断对齐状态
            const isAligned = heightDiff <= 1 && topDiff <= 1 && bottomDiff <= 1;
            const statusElement = document.getElementById('alignmentStatus');
            const indicatorElement = document.getElementById('statusIndicator');

            if (isAligned) {
                statusElement.textContent = '完美对齐 ✓';
                indicatorElement.className = 'status-indicator aligned';
            } else {
                statusElement.textContent = '未对齐 ✗';
                indicatorElement.className = 'status-indicator misaligned';
            }

            console.log('头部对齐测量结果:', {
                tableHeight,
                timelineHeight,
                heightDiff,
                topDiff,
                bottomDiff,
                isAligned
            });
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', () => {
            console.log('页面加载完成，初始化甘特图...');
            initializeGantt();
        });

        // 窗口大小改变时重新测量
        window.addEventListener('resize', () => {
            setTimeout(measureAlignment, 100);
        });
    </script>
</body>
</html>

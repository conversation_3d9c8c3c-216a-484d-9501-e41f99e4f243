import EventEmitter from "./utils/EventEmitter";

class GanttChart extends EventEmitter {
  constructor(containerId, options = {}) {
    if (!containerId) {
      throw new Error("Container element is required");
    }

    this.container = document.getElementById(containerId);
    this.options = this.mergeDefaultOptions(options);

    this.data = options.data || this.generateSampleData();

    // DOM 元素引用
    this.elements = {};

    // 状态管理
    this.state = {
      viewMode: this.options.viewMode,
      selectedTasks: new Set(),
      scrollPosition: { x: 0, y: 0 },
    };

    this.init();
  }

  // 合并默认配置
  mergeDefaultOptions(options) {
    const defaults = {
      taskMapping: {
        // 任务映射
        id: "id",
        startDate: "start",
        endDate: "end",
        label: "label",
        duration: "duration",
        progress: "progress",
        type: "type",
        style: "style",
        collapsed: "collapsed",
        parentId: "parentId",
      },

      // 基础配置
      startDate: new Date("2024-01-01"),
      endDate: new Date("2024-03-31"),
      viewMode: "day", // day, week, month, quarter
      rowHeight: 40,

      // 表格配置
      taskList: {
        display: true,
        columns: [{ key: "id", title: "ID", width: 40 }],
      },

      // 样式配置
      theme: "default",
      colors: {
        primary: "#4A90E2",
        success: "#7ED321",
        warning: "#F5A623",
        danger: "#D0021B",
      },

      // 功能开关
      enableVirtualScroll: true,
      enableMilestones: true,
      enableDragDrop: false,

      // 事件回调
      onTaskClick: null,
      onMilestoneClick: null,
      onDataChange: null,
    };

    return { ...defaults, ...options };
  }

  // 生成示例数据
  generateSampleData() {
    return [
      {
        id: "task-1",
        name: "项目启动",
        startDate: "2024-01-01",
        endDate: "2024-01-05",
        duration: 5,
        progress: 1.0,
        assignee: "张三",
        level: 0,
        status: "completed",
        customFields: {
          priority: "high",
          department: "项目组",
        },
        milestones: [
          {
            id: "milestone-1",
            name: "项目启动会",
            date: "2024-01-02",
            type: "review",
          },
        ],
      },
      {
        id: "task-2",
        name: "需求分析",
        startDate: "2024-01-06",
        endDate: "2024-01-20",
        duration: 15,
        progress: 0.8,
        assignee: "李四",
        level: 1,
        status: "in-progress",
        customFields: {
          priority: "high",
          department: "产品组",
        },
        milestones: [
          {
            id: "milestone-2",
            name: "需求评审",
            date: "2024-01-15",
            type: "review",
          },
        ],
      },
      {
        id: "task-3",
        name: "系统设计",
        startDate: "2024-01-21",
        endDate: "2024-02-10",
        duration: 21,
        progress: 0.6,
        assignee: "王五",
        level: 1,
        status: "in-progress",
        customFields: {
          priority: "medium",
          department: "架构组",
        },
        milestones: [
          {
            id: "milestone-3",
            name: "架构评审",
            date: "2024-02-05",
            type: "review",
          },
        ],
      },
      {
        id: "task-4",
        name: "开发实现",
        startDate: "2024-02-11",
        endDate: "2024-03-15",
        duration: 33,
        progress: 0.3,
        assignee: "赵六",
        level: 0,
        status: "in-progress",
        customFields: {
          priority: "high",
          department: "开发组",
        },
        milestones: [
          {
            id: "milestone-4",
            name: "代码评审",
            date: "2024-03-01",
            type: "review",
          },
          {
            id: "milestone-5",
            name: "功能交付",
            date: "2024-03-10",
            type: "delivery",
          },
        ],
      },
      {
        id: "task-5",
        name: "测试验收",
        startDate: "2024-03-16",
        endDate: "2024-03-30",
        duration: 15,
        progress: 0.0,
        assignee: "孙七",
        level: 0,
        status: "pending",
        customFields: {
          priority: "medium",
          department: "测试组",
        },
        milestones: [
          {
            id: "milestone-6",
            name: "测试完成",
            date: "2024-03-25",
            type: "approval",
          },
        ],
      },
    ];
  }

  // 初始化
  init() {
    console.log("Initializing GanttChart...");
    this.createLayout();
    this.renderTable();
    this.renderChart();
    this.bindEvents();
    this.updateStatus();
    console.log("GanttChart initialized successfully!");
  }

  // 创建基础布局
  createLayout() {
    this.container.innerHTML = "";
    this.container.className = "gantt-container";

    const layout = `
              <div class="gantt-header">
                  <div class="gantt-toolbar">
                      <div class="gantt-view-controls">
                          <button class="gantt-btn ${
                            this.state.viewMode === "day" ? "active" : ""
                          }" data-view="day">日</button>
                          <button class="gantt-btn ${
                            this.state.viewMode === "week" ? "active" : ""
                          }" data-view="week">周</button>
                          <button class="gantt-btn ${
                            this.state.viewMode === "month" ? "active" : ""
                          }" data-view="month">月</button>
                          <button class="gantt-btn ${
                            this.state.viewMode === "quarter" ? "active" : ""
                          }" data-view="quarter">季度</button>
                      </div>
                      <div class="gantt-actions">
                          <button class="gantt-btn" id="gantt-zoom-in">放大</button>
                          <button class="gantt-btn" id="gantt-zoom-out">缩小</button>
                          <button class="gantt-btn" id="gantt-fit-view">适应窗口</button>
                          <button class="gantt-btn" id="gantt-refresh">刷新</button>
                      </div>
                  </div>
              </div>
              
              <div class="gantt-main">
                <div class="gantt-left-panel">
                  <div class="gantt-table-header"></div>
                  <div class="gantt-table-body"></div>
                </div>
                
                <div class="gantt-splitter"></div>
                  
                <div class="gantt-right-panel">
                  <div class="gantt-timeline-header">时间轴 (${
                    this.state.viewMode
                  }视图)</div>
                  <div class="gantt-chart-body">
                    <svg class="gantt-svg">
                      <defs>
                        <!-- 渐变定义 -->
                        <linearGradient id="taskGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                          <stop offset="0%" style="stop-color:${
                            this.options.colors.primary
                          };stop-opacity:1" />
                          <stop offset="100%" style="stop-color:${
                            this.options.colors.primary
                          };stop-opacity:0.8" />
                        </linearGradient>
                        
                        <!-- 网格图案 -->
                        <pattern id="gridPattern" width="30" height="40" patternUnits="userSpaceOnUse">
                          <rect width="30" height="40" fill="none" stroke="#f0f0f0" stroke-width="1"/>
                        </pattern>
                      </defs>
                      
                      <!-- 渲染层 -->
                      <g class="gantt-grid-layer"></g>
                      <g class="gantt-tasks-layer"></g>
                      <g class="gantt-milestones-layer"></g>
                      <g class="gantt-dependencies-layer"></g>
                    </svg>
                  </div>
                </div>
              </div>
              
              <div class="gantt-footer">
                  <div class="gantt-status">
                      <span>任务数: ${this.data.length}</span>
                      <span>视图: ${this.state.viewMode}</span>
                      <span>状态: 就绪</span>
                  </div>
              </div>
          `;

    this.container.innerHTML = layout;

    // 保存关键DOM引用
    this.elements = {
      header: this.container.querySelector(".gantt-header"),
      toolbar: this.container.querySelector(".gantt-toolbar"),
      leftPanel: this.container.querySelector(".gantt-left-panel"),
      rightPanel: this.container.querySelector(".gantt-right-panel"),
      tableHeader: this.container.querySelector(".gantt-table-header"),
      tableBody: this.container.querySelector(".gantt-table-body"),
      timelineHeader: this.container.querySelector(".gantt-timeline-header"),
      chartBody: this.container.querySelector(".gantt-chart-body"),
      svg: this.container.querySelector(".gantt-svg"),
      footer: this.container.querySelector(".gantt-footer"),
      splitter: this.container.querySelector(".gantt-splitter"),
      status: this.container.querySelector(".gantt-status"),
    };

    console.log("Layout created successfully");
  }

  // 渲染表格
  renderTable() {
    // 渲染表头
    const headerHtml = this.options.taskList.columns
      .map(
        (col) =>
          `<div class="gantt-table-header-cell" style="width: ${col.width}px">${col.title}</div>`
      )
      .join("");
    this.elements.tableHeader.innerHTML = headerHtml;

    // 渲染表格行
    const rowsHtml = this.data
      .map((task, index) => {
        const cellsHtml = this.options.taskList.columns
          .map((col) => {
            let value = "";
            let style = `width: ${col.width}px`;

            if (col.key === "name") {
              const indent = (task.level || 0) * 20;
              style += `; padding-left: ${indent + 12}px`;
              value = task.name;
            } else if (col.key === "assignee") {
              value = task.assignee || "";
            } else if (col.key === "startDate") {
              value = new Date(task.startDate).toLocaleDateString("zh-CN");
            } else if (col.key === "duration") {
              value = `${task.duration}天`;
            } else {
              value = task[col.key] || "";
            }

            return `<div class="gantt-table-cell" style="${style}">${value}</div>`;
          })
          .join("");

        return `<div class="gantt-table-row" data-task-id="${task.id}">${cellsHtml}</div>`;
      })
      .join("");

    this.elements.tableBody.innerHTML = rowsHtml;
    console.log("Table rendered successfully");
  }

  // 渲染图表区域
  renderChart() {
    const svg = this.elements.svg;

    // 清除现有内容
    const layers = [
      "gantt-grid-layer",
      "gantt-tasks-layer",
      "gantt-milestones-layer",
    ];
    layers.forEach((layerClass) => {
      const layer = svg.querySelector(`.${layerClass}`);
      if (layer) layer.innerHTML = "";
    });

    // 计算 SVG 尺寸
    const totalHeight = this.data.length * this.options.rowHeight;
    svg.style.height = `${Math.max(totalHeight, 400)}px`;

    // 渲染网格背景
    this.renderGrid();

    // 渲染任务条（占位）
    this.renderTaskBars();

    // 渲染里程碑（占位）
    this.renderMilestones();

    console.log("Chart rendered successfully");
  }

  // 渲染网格
  renderGrid() {
    const gridLayer = this.elements.svg.querySelector(".gantt-grid-layer");
    const totalHeight = this.data.length * this.options.rowHeight;

    // 背景网格
    const background = document.createElementNS(
      "http://www.w3.org/2000/svg",
      "rect"
    );
    background.setAttribute("width", "100%");
    background.setAttribute("height", totalHeight);
    background.setAttribute("fill", "url(#gridPattern)"); // 铺满背景
    gridLayer.appendChild(background);
  }

  // 渲染任务条（示例）
  renderTaskBars() {
    const tasksLayer = this.elements.svg.querySelector(".gantt-tasks-layer");

    this.data.forEach((task, index) => {
      const y = index * this.options.rowHeight;
      const taskGroup = document.createElementNS(
        "http://www.w3.org/2000/svg",
        "g"
      );
      taskGroup.setAttribute("data-task-id", task.id);

      // 示例任务条
      const rect = document.createElementNS(
        "http://www.w3.org/2000/svg",
        "rect"
      );
      rect.setAttribute("x", 50 + index * 100);
      rect.setAttribute("y", y + 8);
      rect.setAttribute("width", task.duration * 8);
      rect.setAttribute("height", 24);
      rect.setAttribute("fill", this.getTaskColor(task));
      rect.setAttribute("rx", 4);
      rect.setAttribute("class", "task-bar");

      // 进度条
      const progressRect = document.createElementNS(
        "http://www.w3.org/2000/svg",
        "rect"
      );
      progressRect.setAttribute("x", 50 + index * 100);
      progressRect.setAttribute("y", y + 8);
      progressRect.setAttribute("width", task.duration * 8 * task.progress);
      progressRect.setAttribute("height", 24);
      progressRect.setAttribute("fill", this.getProgressColor(task));
      progressRect.setAttribute("rx", 4);
      progressRect.setAttribute("opacity", "0.8");

      // 任务文本
      const text = document.createElementNS(
        "http://www.w3.org/2000/svg",
        "text"
      );
      text.setAttribute("x", 50 + index * 100 + 8);
      text.setAttribute("y", y + 24);
      text.setAttribute("font-size", "12px");
      text.setAttribute("fill", "white");
      text.setAttribute("font-weight", "500");
      text.textContent = task.name;

      taskGroup.appendChild(rect);
      taskGroup.appendChild(progressRect);
      taskGroup.appendChild(text);
      tasksLayer.appendChild(taskGroup);
    });
  }

  // 渲染里程碑（示例）
  renderMilestones() {}

  // 获取任务颜色
  getTaskColor(task) {
    const colors = {
      completed: "#7ED321",
      "in-progress": "#4A90E2",
      pending: "#9CA3AF",
      overdue: "#D0021B",
    };
    return colors[task.status] || colors["pending"];
  }

  // 获取进度颜色
  getProgressColor(task) {
    if (task.progress >= 1.0) return "#7ED321";
    if (task.progress >= 0.5) return "#4A90E2";
    return "#F5A623";
  }

  // 获取里程碑颜色
  getMilestoneColor(milestone) {
    const colors = {
      review: "#FF9500",
      delivery: "#007AFF",
      approval: "#34C759",
    };
    return colors[milestone.type] || colors["review"];
  }

  // 绑定事件
  bindEvents() {
    // 滚动同步
    this.elements.tableBody.addEventListener("scroll", (e) => {
      this.elements.chartBody.scrollTop = e.target.scrollTop;
      this.state.scrollPosition.y = e.target.scrollTop;
    });

    this.elements.chartBody.addEventListener("scroll", (e) => {
      this.elements.tableBody.scrollTop = e.target.scrollTop;
      this.state.scrollPosition.x = e.target.scrollLeft;
      this.state.scrollPosition.y = e.target.scrollTop;
    });

    // SVG 点击事件
    this.elements.svg.addEventListener("click", (e) => {
      const taskBar = e.target.closest("[data-task-id]");
      if (taskBar) {
        this.selectTask(taskBar.dataset.taskId);
      }

      // const milestone = e.target.closest("[data-milestone-id]");
      // if (milestone) {
      //   this.selectMilestone(milestone.dataset.milestoneId);
      // }
    });

    // 窗口大小变化
    window.addEventListener("resize", () => {
      this.handleResize();
    });

    // 分割器拖拽（基础实现）
    let isDragging = false;
    this.elements.splitter.addEventListener("mousedown", (e) => {
      isDragging = true;
      document.body.style.cursor = "col-resize";
      e.preventDefault();
    });

    document.addEventListener("mousemove", (e) => {
      if (isDragging) {
        const containerRect = this.container.getBoundingClientRect();
        const newWidth = e.clientX - containerRect.left - 20;
        if (newWidth >= 200 && newWidth <= 600) {
          this.elements.leftPanel.style.width = `${newWidth}px`;
        }
      }
    });

    document.addEventListener("mouseup", () => {
      if (isDragging) {
        isDragging = false;
        document.body.style.cursor = "";
      }
    });

    console.log("Events bound successfully");
  }

  // 刷新
  refresh() {
    console.log("Refreshing gantt chart...");
    this.renderTable();
    this.renderChart();
    this.updateStatus();
    this.showMessage("甘特图已刷新");
  }

  // 公共 API
  setData(data) {
    this.data = data;
    this.validateData();
    this.renderTable();
    this.renderChart();
    this.updateStatus();
    console.log("Data updated");
  }

  getData() {
    return this.data;
  }

  // 窗口大小调整
  handleResize() {
    console.log("Window resized - adjusting layout");
    // 重新计算布局
    setTimeout(() => {
      this.renderChart();
    }, 100);
  }

  // 更新状态
  updateStatus() {
    const completedTasks = this.data.filter(
      (task) => task.status === "completed"
    ).length;
    const inProgressTasks = this.data.filter(
      (task) => task.status === "in-progress"
    ).length;
    const pendingTasks = this.data.filter(
      (task) => task.status === "pending"
    ).length;

    this.elements.status.innerHTML = `
              <span>总任务: ${this.data.length}</span>
              <span>已完成: ${completedTasks}</span>
              <span>进行中: ${inProgressTasks}</span>
              <span>待开始: ${pendingTasks}</span>
              <span>视图: ${this.state.viewMode}</span>
          `;
  }

  // 销毁
  destroy() {
    // 清理事件监听器
    window.removeEventListener("resize", this.handleResize);
    this.container.innerHTML = "";
    console.log("GanttChart destroyed");
  }
}

export default GanttChart;
<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Step 1: 甘特图基础框架</title>
    <style>
      /* =============================================
           Step 1: 甘特图基础样式系统
           ============================================= */

      /* CSS 变量定义 - 支持主题切换 */
      :root {
        /* 颜色变量 */
        --gantt-primary: #4a90e2;
        --gantt-success: #7ed321;
        --gantt-warning: #f5a623;
        --gantt-danger: #d0021b;
        --gantt-gray-50: #f9fafb;
        --gantt-gray-100: #f3f4f6;
        --gantt-gray-200: #e5e7eb;
        --gantt-gray-300: #d1d5db;
        --gantt-gray-400: #9ca3af;
        --gantt-gray-500: #6b7280;
        --gantt-gray-600: #4b5563;
        --gantt-gray-700: #374151;
        --gantt-gray-800: #1f2937;
        --gantt-gray-900: #111827;

        /* 尺寸变量 */
        --gantt-header-height: 60px;
        --gantt-footer-height: 30px;
        --gantt-row-height: 40px;
        --gantt-splitter-width: 4px;
        --gantt-left-panel-width: 400px;

        /* 字体变量 */
        --gantt-font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
        --gantt-font-size-sm: 12px;
        --gantt-font-size-base: 14px;
        --gantt-font-size-lg: 16px;

        /* 阴影和圆角 */
        --gantt-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        --gantt-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
        --gantt-radius-sm: 4px;
        --gantt-radius: 6px;
        --gantt-radius-lg: 8px;
      }

      /* 基础重置 */
      * {
        box-sizing: border-box;
      }

      body {
        margin: 0;
        padding: 20px;
        background-color: #f5f5f5;
        font-family: var(--gantt-font-family);
      }

      /* =============================================
           甘特图容器
           ============================================= */
      .gantt-container {
        width: 100%;
        height: 80vh;
        display: flex;
        flex-direction: column;
        font-family: var(--gantt-font-family);
        font-size: var(--gantt-font-size-base);
        color: var(--gantt-gray-700);
        background-color: white;
        border: 1px solid var(--gantt-gray-200);
        border-radius: var(--gantt-radius);
        overflow: hidden;
        box-shadow: var(--gantt-shadow);
      }

      /* =============================================
           头部工具栏
           ============================================= */
      .gantt-header {
        height: var(--gantt-header-height);
        background-color: var(--gantt-gray-50);
        border-bottom: 1px solid var(--gantt-gray-200);
        flex-shrink: 0;
      }

      .gantt-toolbar {
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 16px;
      }

      .gantt-view-controls {
        display: flex;
        gap: 2px;
        background-color: var(--gantt-gray-100);
        border-radius: var(--gantt-radius-sm);
        padding: 2px;
      }

      .gantt-actions {
        display: flex;
        gap: 8px;
      }

      .gantt-btn {
        padding: 6px 12px;
        border: 1px solid var(--gantt-gray-300);
        background-color: white;
        color: var(--gantt-gray-700);
        border-radius: var(--gantt-radius-sm);
        cursor: pointer;
        font-size: var(--gantt-font-size-sm);
        font-weight: 500;
        transition: all 0.2s ease;
        user-select: none;
        -webkit-user-select: none;
      }

      .gantt-btn:hover {
        background-color: var(--gantt-gray-50);
        border-color: var(--gantt-gray-400);
        transform: translateY(-1px);
      }

      .gantt-btn:active {
        transform: translateY(0);
      }

      .gantt-btn.active {
        background-color: var(--gantt-primary);
        color: white;
        border-color: var(--gantt-primary);
      }

      .gantt-view-controls .gantt-btn {
        border: none;
        background-color: transparent;
        border-radius: var(--gantt-radius-sm);
      }

      .gantt-view-controls .gantt-btn:hover {
        background-color: var(--gantt-gray-200);
      }

      .gantt-view-controls .gantt-btn.active {
        background-color: white;
        box-shadow: var(--gantt-shadow-sm);
      }

      /* =============================================
           主体区域
           ============================================= */
      .gantt-main {
        flex: 1;
        display: flex;
        overflow: hidden;
      }

      /* 左侧面板 */
      .gantt-left-panel {
        width: var(--gantt-left-panel-width);
        background-color: white;
        border-right: 1px solid var(--gantt-gray-200);
        display: flex;
        flex-direction: column;
        flex-shrink: 0;
      }

      /* 分割器 */
      .gantt-splitter {
        width: var(--gantt-splitter-width);
        background-color: var(--gantt-gray-200);
        cursor: col-resize;
        flex-shrink: 0;
        position: relative;
        transition: background-color 0.2s ease;
      }

      .gantt-splitter:hover {
        background-color: var(--gantt-primary);
      }

      .gantt-splitter::after {
        content: "";
        position: absolute;
        left: 50%;
        top: 50%;
        width: 2px;
        height: 20px;
        background-color: var(--gantt-gray-400);
        transform: translate(-50%, -50%);
        transition: background-color 0.2s ease;
      }

      .gantt-splitter:hover::after {
        background-color: white;
      }

      /* 右侧面板 */
      .gantt-right-panel {
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: hidden;
      }

      /* =============================================
           表格区域
           ============================================= */
      .gantt-table-header {
        height: var(--gantt-row-height);
        background-color: var(--gantt-gray-50);
        border-bottom: 1px solid var(--gantt-gray-200);
        display: flex;
        align-items: center;
        font-weight: 600;
        font-size: var(--gantt-font-size-sm);
        color: var(--gantt-gray-600);
      }

      .gantt-table-header-cell {
        padding: 8px 12px;
        border-right: 1px solid var(--gantt-gray-200);
        flex-shrink: 0;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }

      .gantt-table-body {
        flex: 1;
        overflow-y: auto;
        overflow-x: hidden;
      }

      .gantt-table-row {
        height: var(--gantt-row-height);
        display: flex;
        align-items: center;
        border-bottom: 1px solid var(--gantt-gray-100);
        transition: background-color 0.15s ease;
      }

      .gantt-table-row:hover {
        background-color: var(--gantt-gray-50);
      }

      .gantt-table-cell {
        padding: 8px 12px;
        border-right: 1px solid var(--gantt-gray-100);
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        font-size: var(--gantt-font-size-sm);
        flex-shrink: 0;
      }

      /* =============================================
           时间轴区域
           ============================================= */
      .gantt-timeline-header {
        height: var(--gantt-row-height);
        background-color: var(--gantt-gray-50);
        border-bottom: 1px solid var(--gantt-gray-200);
        overflow: hidden;
        position: relative;
        display: flex;
        align-items: center;
        padding: 0 16px;
        font-weight: 600;
        font-size: var(--gantt-font-size-sm);
        color: var(--gantt-gray-600);
      }

      .gantt-chart-body {
        flex: 1;
        overflow: auto;
        position: relative;
        background-color: white;
      }

      /* SVG 样式 */
      .gantt-svg {
        width: 100%;
        height: 100%;
        display: block;
        min-width: 1000px;
        min-height: 400px;
      }

      /* =============================================
           底部状态栏
           ============================================= */
      .gantt-footer {
        height: var(--gantt-footer-height);
        background-color: var(--gantt-gray-50);
        border-top: 1px solid var(--gantt-gray-200);
        display: flex;
        align-items: center;
        padding: 0 16px;
        flex-shrink: 0;
      }

      .gantt-status {
        display: flex;
        gap: 20px;
        font-size: var(--gantt-font-size-sm);
        color: var(--gantt-gray-500);
      }

      /* =============================================
           演示和占位内容
           ============================================= */
      .demo-content {
        padding: 20px;
        text-align: center;
        color: var(--gantt-gray-500);
      }

      .demo-content h3 {
        margin: 0 0 16px 0;
        color: var(--gantt-primary);
        font-size: var(--gantt-font-size-lg);
      }

      .demo-content p {
        margin: 8px 0;
        font-size: var(--gantt-font-size-sm);
      }

      .feature-list {
        list-style: none;
        padding: 0;
        margin: 16px 0;
      }

      .feature-list li {
        margin: 4px 0;
        padding: 4px 8px;
        background-color: var(--gantt-gray-50);
        border-radius: var(--gantt-radius-sm);
        font-size: var(--gantt-font-size-sm);
      }

      .feature-list li.completed {
        background-color: #e6f7e6;
        color: var(--gantt-success);
      }

      .feature-list li.completed::before {
        content: "✓ ";
        font-weight: bold;
      }

      /* =============================================
           滚动条样式
           ============================================= */
      .gantt-table-body::-webkit-scrollbar,
      .gantt-chart-body::-webkit-scrollbar {
        width: 8px;
        height: 8px;
      }

      .gantt-table-body::-webkit-scrollbar-track,
      .gantt-chart-body::-webkit-scrollbar-track {
        background: var(--gantt-gray-100);
      }

      .gantt-table-body::-webkit-scrollbar-thumb,
      .gantt-chart-body::-webkit-scrollbar-thumb {
        background: var(--gantt-gray-300);
        border-radius: 4px;
      }

      .gantt-table-body::-webkit-scrollbar-thumb:hover,
      .gantt-chart-body::-webkit-scrollbar-thumb:hover {
        background: var(--gantt-gray-400);
      }

      /* =============================================
           响应式设计
           ============================================= */
      @media (max-width: 768px) {
        :root {
          --gantt-left-panel-width: 300px;
          --gantt-font-size-base: 13px;
          --gantt-font-size-sm: 11px;
        }

        .gantt-toolbar {
          flex-direction: column;
          gap: 8px;
          padding: 8px 16px;
        }

        .gantt-header {
          height: auto;
          min-height: var(--gantt-header-height);
        }
      }
    </style>
  </head>
  <body>
    <h1>Step 1: 甘特图基础框架演示</h1>

    <div id="gantt-container" class="gantt-container">
      <!-- 甘特图将通过 JavaScript 初始化 -->
    </div>

    <script type="module">
      // =============================================
      // Step 1: 甘特图核心类实现
      // =============================================

      class GanttChart {
        constructor(container, options = {}) {
          if (!container) {
            throw new Error("Container element is required");
          }

          this.container = container;
          this.options = this.mergeDefaultOptions(options);
          this.data = options.data || this.generateSampleData();

          // DOM 元素引用
          this.elements = {};

          // 状态管理
          this.state = {
            viewMode: this.options.viewMode,
            selectedTasks: new Set(),
            scrollPosition: { x: 0, y: 0 },
          };

          this.init();
        }

        // 合并默认配置
        mergeDefaultOptions(options) {
          const defaults = {
            // 基础配置
            startDate: new Date("2024-01-01"),
            endDate: new Date("2024-03-31"),
            viewMode: "day", // day, week, month, quarter
            rowHeight: 40,

            // 表格配置
            columns: [
              { key: "name", title: "任务名称", width: 180 },
              { key: "assignee", title: "负责人", width: 80 },
              { key: "startDate", title: "开始", width: 80 },
              { key: "duration", title: "工期", width: 60 },
            ],

            // 样式配置
            theme: "default",
            colors: {
              primary: "#4A90E2",
              success: "#7ED321",
              warning: "#F5A623",
              danger: "#D0021B",
            },

            // 功能开关
            enableVirtualScroll: true,
            enableMilestones: true,
            enableDragDrop: false,

            // 事件回调
            onTaskClick: null,
            onMilestoneClick: null,
            onDataChange: null,
          };

          return { ...defaults, ...options };
        }

        // 生成示例数据
        generateSampleData() {
          return [
            {
              id: "task-1",
              name: "项目启动",
              startDate: "2024-01-01",
              endDate: "2024-01-05",
              duration: 5,
              progress: 1.0,
              assignee: "张三",
              level: 0,
              status: "completed",
              customFields: {
                priority: "high",
                department: "项目组",
              },
              milestones: [
                {
                  id: "milestone-1",
                  name: "项目启动会",
                  date: "2024-01-02",
                  type: "review",
                },
              ],
            },
            {
              id: "task-2",
              name: "需求分析",
              startDate: "2024-01-06",
              endDate: "2024-01-20",
              duration: 15,
              progress: 0.8,
              assignee: "李四",
              level: 1,
              status: "in-progress",
              customFields: {
                priority: "high",
                department: "产品组",
              },
              milestones: [
                {
                  id: "milestone-2",
                  name: "需求评审",
                  date: "2024-01-15",
                  type: "review",
                },
              ],
            },
            {
              id: "task-3",
              name: "系统设计",
              startDate: "2024-01-21",
              endDate: "2024-02-10",
              duration: 21,
              progress: 0.6,
              assignee: "王五",
              level: 1,
              status: "in-progress",
              customFields: {
                priority: "medium",
                department: "架构组",
              },
              milestones: [
                {
                  id: "milestone-3",
                  name: "架构评审",
                  date: "2024-02-05",
                  type: "review",
                },
              ],
            },
            {
              id: "task-4",
              name: "开发实现",
              startDate: "2024-02-11",
              endDate: "2024-03-15",
              duration: 33,
              progress: 0.3,
              assignee: "赵六",
              level: 0,
              status: "in-progress",
              customFields: {
                priority: "high",
                department: "开发组",
              },
              milestones: [
                {
                  id: "milestone-4",
                  name: "代码评审",
                  date: "2024-03-01",
                  type: "review",
                },
                {
                  id: "milestone-5",
                  name: "功能交付",
                  date: "2024-03-10",
                  type: "delivery",
                },
              ],
            },
            {
              id: "task-5",
              name: "测试验收",
              startDate: "2024-03-16",
              endDate: "2024-03-30",
              duration: 15,
              progress: 0.0,
              assignee: "孙七",
              level: 0,
              status: "pending",
              customFields: {
                priority: "medium",
                department: "测试组",
              },
              milestones: [
                {
                  id: "milestone-6",
                  name: "测试完成",
                  date: "2024-03-25",
                  type: "approval",
                },
              ],
            },
          ];
        }

        // 初始化
        init() {
          console.log("Initializing GanttChart...");
          this.validateData();
          this.createLayout();
          this.renderTable();
          this.renderChart();
          this.bindEvents();
          this.updateStatus();
          console.log("GanttChart initialized successfully!");
        }

        // 数据验证
        validateData() {
          if (!Array.isArray(this.data)) {
            throw new Error("Data must be an array");
          }

          this.data.forEach((task, index) => {
            if (!task.id) {
              throw new Error(`Task at index ${index} missing required field: id`);
            }
            if (!task.name) {
              throw new Error(`Task ${task.id} missing required field: name`);
            }
            if (!task.startDate || !task.endDate) {
              throw new Error(`Task ${task.id} missing required fields: startDate or endDate`);
            }
          });

          console.log(`Data validation passed: ${this.data.length} tasks`);
        }

        // 创建基础布局
        createLayout() {
          this.container.innerHTML = "";
          this.container.className = "gantt-container";

          const layout = `
                    <div class="gantt-header">
                        <div class="gantt-toolbar">
                            <div class="gantt-view-controls">
                                <button class="gantt-btn ${
                                  this.state.viewMode === "day" ? "active" : ""
                                }" data-view="day">日</button>
                                <button class="gantt-btn ${
                                  this.state.viewMode === "week" ? "active" : ""
                                }" data-view="week">周</button>
                                <button class="gantt-btn ${
                                  this.state.viewMode === "month" ? "active" : ""
                                }" data-view="month">月</button>
                                <button class="gantt-btn ${
                                  this.state.viewMode === "quarter" ? "active" : ""
                                }" data-view="quarter">季度</button>
                            </div>
                            <div class="gantt-actions">
                                <button class="gantt-btn" id="gantt-zoom-in">放大</button>
                                <button class="gantt-btn" id="gantt-zoom-out">缩小</button>
                                <button class="gantt-btn" id="gantt-fit-view">适应窗口</button>
                                <button class="gantt-btn" id="gantt-refresh">刷新</button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="gantt-main">
                        <div class="gantt-left-panel">
                            <div class="gantt-table-header"></div>
                            <div class="gantt-table-body"></div>
                        </div>
                        
                        <div class="gantt-splitter"></div>
                        
                        <div class="gantt-right-panel">
                            <div class="gantt-timeline-header">时间轴 (${this.state.viewMode}视图)</div>
                            <div class="gantt-chart-body">
                                <svg class="gantt-svg">
                                    <defs>
                                        <!-- 渐变定义 -->
                                        <linearGradient id="taskGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                                            <stop offset="0%" style="stop-color:${
                                              this.options.colors.primary
                                            };stop-opacity:1" />
                                            <stop offset="100%" style="stop-color:${
                                              this.options.colors.primary
                                            };stop-opacity:0.8" />
                                        </linearGradient>
                                        
                                        <!-- 网格图案 -->
                                        <pattern id="gridPattern" width="30" height="40" patternUnits="userSpaceOnUse">
                                            <rect width="30" height="40" fill="none" stroke="#f0f0f0" stroke-width="1"/>
                                        </pattern>
                                    </defs>
                                    
                                    <!-- 渲染层 -->
                                    <g class="gantt-grid-layer"></g>
                                    <g class="gantt-tasks-layer"></g>
                                    <g class="gantt-milestones-layer"></g>
                                    <g class="gantt-dependencies-layer"></g>
                                </svg>
                            </div>
                        </div>
                    </div>
                    
                    <div class="gantt-footer">
                        <div class="gantt-status">
                            <span>任务数: ${this.data.length}</span>
                            <span>视图: ${this.state.viewMode}</span>
                            <span>状态: 就绪</span>
                        </div>
                    </div>
                `;

          this.container.innerHTML = layout;

          // 保存关键DOM引用
          this.elements = {
            header: this.container.querySelector(".gantt-header"),
            toolbar: this.container.querySelector(".gantt-toolbar"),
            leftPanel: this.container.querySelector(".gantt-left-panel"),
            rightPanel: this.container.querySelector(".gantt-right-panel"),
            tableHeader: this.container.querySelector(".gantt-table-header"),
            tableBody: this.container.querySelector(".gantt-table-body"),
            timelineHeader: this.container.querySelector(".gantt-timeline-header"),
            chartBody: this.container.querySelector(".gantt-chart-body"),
            svg: this.container.querySelector(".gantt-svg"),
            footer: this.container.querySelector(".gantt-footer"),
            splitter: this.container.querySelector(".gantt-splitter"),
            status: this.container.querySelector(".gantt-status"),
          };

          console.log("Layout created successfully");
        }

        // 渲染表格
        renderTable() {
          // 渲染表头
          const headerHtml = this.options.columns
            .map((col) => `<div class="gantt-table-header-cell" style="width: ${col.width}px">${col.title}</div>`)
            .join("");
          this.elements.tableHeader.innerHTML = headerHtml;

          // 渲染表格行
          const rowsHtml = this.data
            .map((task, index) => {
              const cellsHtml = this.options.columns
                .map((col) => {
                  let value = "";
                  let style = `width: ${col.width}px`;

                  if (col.key === "name") {
                    const indent = (task.level || 0) * 20;
                    style += `; padding-left: ${indent + 12}px`;
                    value = task.name;
                  } else if (col.key === "assignee") {
                    value = task.assignee || "";
                  } else if (col.key === "startDate") {
                    value = new Date(task.startDate).toLocaleDateString("zh-CN");
                  } else if (col.key === "duration") {
                    value = `${task.duration}天`;
                  } else {
                    value = task[col.key] || "";
                  }

                  return `<div class="gantt-table-cell" style="${style}">${value}</div>`;
                })
                .join("");

              return `<div class="gantt-table-row" data-task-id="${task.id}">${cellsHtml}</div>`;
            })
            .join("");

          this.elements.tableBody.innerHTML = rowsHtml;
          console.log("Table rendered successfully");
        }

        // 渲染图表区域
        renderChart() {
          const svg = this.elements.svg;

          // 清除现有内容
          const layers = ["gantt-grid-layer", "gantt-tasks-layer", "gantt-milestones-layer"];
          layers.forEach((layerClass) => {
            const layer = svg.querySelector(`.${layerClass}`);
            if (layer) layer.innerHTML = "";
          });

          // 计算 SVG 尺寸
          const totalHeight = this.data.length * this.options.rowHeight;
          svg.style.height = `${Math.max(totalHeight, 400)}px`;

          // 渲染网格背景
          this.renderGrid();

          // 渲染任务条（占位）
          this.renderTaskBars();

          // 渲染里程碑（占位）
          this.renderMilestones();

          console.log("Chart rendered successfully");
        }

        // 渲染网格
        renderGrid() {
          const gridLayer = this.elements.svg.querySelector(".gantt-grid-layer");
          const totalHeight = this.data.length * this.options.rowHeight;

          // 背景网格
          const background = document.createElementNS("http://www.w3.org/2000/svg", "rect");
          background.setAttribute("width", "100%");
          background.setAttribute("height", totalHeight);
          background.setAttribute("fill", "url(#gridPattern)");
          gridLayer.appendChild(background);
        }

        // 渲染任务条（示例）
        renderTaskBars() {
          const tasksLayer = this.elements.svg.querySelector(".gantt-tasks-layer");

          this.data.forEach((task, index) => {
            const y = index * this.options.rowHeight;
            const taskGroup = document.createElementNS("http://www.w3.org/2000/svg", "g");
            taskGroup.setAttribute("data-task-id", task.id);

            // 示例任务条
            const rect = document.createElementNS("http://www.w3.org/2000/svg", "rect");
            rect.setAttribute("x", 50 + index * 100);
            rect.setAttribute("y", y + 8);
            rect.setAttribute("width", task.duration * 8);
            rect.setAttribute("height", 24);
            rect.setAttribute("fill", this.getTaskColor(task));
            rect.setAttribute("rx", 4);
            rect.setAttribute("class", "task-bar");

            // 进度条
            const progressRect = document.createElementNS("http://www.w3.org/2000/svg", "rect");
            progressRect.setAttribute("x", 50 + index * 100);
            progressRect.setAttribute("y", y + 8);
            progressRect.setAttribute("width", task.duration * 8 * task.progress);
            progressRect.setAttribute("height", 24);
            progressRect.setAttribute("fill", this.getProgressColor(task));
            progressRect.setAttribute("rx", 4);
            progressRect.setAttribute("opacity", "0.8");

            // 任务文本
            const text = document.createElementNS("http://www.w3.org/2000/svg", "text");
            text.setAttribute("x", 50 + index * 100 + 8);
            text.setAttribute("y", y + 24);
            text.setAttribute("font-size", "12px");
            text.setAttribute("fill", "white");
            text.setAttribute("font-weight", "500");
            text.textContent = task.name;

            taskGroup.appendChild(rect);
            taskGroup.appendChild(progressRect);
            taskGroup.appendChild(text);
            tasksLayer.appendChild(taskGroup);
          });
        }

        // 渲染里程碑（示例）
        renderMilestones() {
          const milestonesLayer = this.elements.svg.querySelector(".gantt-milestones-layer");

          this.data.forEach((task, taskIndex) => {
            if (task.milestones && task.milestones.length > 0) {
              const y = taskIndex * this.options.rowHeight;

              task.milestones.forEach((milestone, milestoneIndex) => {
                const milestoneGroup = document.createElementNS("http://www.w3.org/2000/svg", "g");
                milestoneGroup.setAttribute("data-milestone-id", milestone.id);

                // 里程碑位置（示例计算）
                const x = 150 + taskIndex * 100 + milestoneIndex * 50;

                // 菱形标记
                const diamond = document.createElementNS("http://www.w3.org/2000/svg", "polygon");
                diamond.setAttribute("points", `${x},${y + 12} ${x + 8},${y + 20} ${x},${y + 28} ${x - 8},${y + 20}`);
                diamond.setAttribute("fill", this.getMilestoneColor(milestone));
                diamond.setAttribute("stroke", "#333");
                diamond.setAttribute("stroke-width", "1");

                // 连接线
                const line = document.createElementNS("http://www.w3.org/2000/svg", "line");
                line.setAttribute("x1", x);
                line.setAttribute("y1", y + 28);
                line.setAttribute("x2", x);
                line.setAttribute("y2", y + 35);
                line.setAttribute("stroke", "#666");
                line.setAttribute("stroke-width", "1");
                line.setAttribute("stroke-dasharray", "2,2");

                milestoneGroup.appendChild(diamond);
                milestoneGroup.appendChild(line);
                milestonesLayer.appendChild(milestoneGroup);
              });
            }
          });
        }

        // 获取任务颜色
        getTaskColor(task) {
          const colors = {
            completed: "#7ED321",
            "in-progress": "#4A90E2",
            pending: "#9CA3AF",
            overdue: "#D0021B",
          };
          return colors[task.status] || colors["pending"];
        }

        // 获取进度颜色
        getProgressColor(task) {
          if (task.progress >= 1.0) return "#7ED321";
          if (task.progress >= 0.5) return "#4A90E2";
          return "#F5A623";
        }

        // 获取里程碑颜色
        getMilestoneColor(milestone) {
          const colors = {
            review: "#FF9500",
            delivery: "#007AFF",
            approval: "#34C759",
          };
          return colors[milestone.type] || colors["review"];
        }

        // 绑定事件
        bindEvents() {
          // 工具栏事件
          this.elements.toolbar.addEventListener("click", (e) => {
            if (e.target.dataset.view) {
              this.switchView(e.target.dataset.view);
            }

            // 功能按钮
            switch (e.target.id) {
              case "gantt-zoom-in":
                this.zoomIn();
                break;
              case "gantt-zoom-out":
                this.zoomOut();
                break;
              case "gantt-fit-view":
                this.fitToView();
                break;
              case "gantt-refresh":
                this.refresh();
                break;
            }
          });

          // 表格行点击事件
          this.elements.tableBody.addEventListener("click", (e) => {
            const row = e.target.closest(".gantt-table-row");
            if (row) {
              this.selectTask(row.dataset.taskId);
            }
          });

          // SVG 点击事件
          this.elements.svg.addEventListener("click", (e) => {
            const taskBar = e.target.closest("[data-task-id]");
            if (taskBar) {
              this.selectTask(taskBar.dataset.taskId);
            }

            const milestone = e.target.closest("[data-milestone-id]");
            if (milestone) {
              this.selectMilestone(milestone.dataset.milestoneId);
            }
          });

          // 滚动同步
          this.elements.tableBody.addEventListener("scroll", (e) => {
            this.elements.chartBody.scrollTop = e.target.scrollTop;
            this.state.scrollPosition.y = e.target.scrollTop;
          });

          this.elements.chartBody.addEventListener("scroll", (e) => {
            this.elements.tableBody.scrollTop = e.target.scrollTop;
            this.state.scrollPosition.x = e.target.scrollLeft;
            this.state.scrollPosition.y = e.target.scrollTop;
          });

          // 窗口大小变化
          window.addEventListener("resize", () => {
            this.handleResize();
          });

          // 分割器拖拽（基础实现）
          let isDragging = false;
          this.elements.splitter.addEventListener("mousedown", (e) => {
            isDragging = true;
            document.body.style.cursor = "col-resize";
            e.preventDefault();
          });

          document.addEventListener("mousemove", (e) => {
            if (isDragging) {
              const containerRect = this.container.getBoundingClientRect();
              const newWidth = e.clientX - containerRect.left - 20;
              if (newWidth >= 200 && newWidth <= 600) {
                this.elements.leftPanel.style.width = `${newWidth}px`;
              }
            }
          });

          document.addEventListener("mouseup", () => {
            if (isDragging) {
              isDragging = false;
              document.body.style.cursor = "";
            }
          });

          console.log("Events bound successfully");
        }

        // 切换视图
        switchView(viewMode) {
          if (this.state.viewMode === viewMode) return;

          this.state.viewMode = viewMode;

          // 更新按钮状态
          this.elements.toolbar.querySelectorAll("[data-view]").forEach((btn) => {
            btn.classList.toggle("active", btn.dataset.view === viewMode);
          });

          // 更新时间轴头部
          this.elements.timelineHeader.textContent = `时间轴 (${viewMode}视图)`;

          this.updateStatus();
          console.log(`Switched to ${viewMode} view`);

          // 触发重新渲染
          this.renderChart();
        }

        // 选择任务
        selectTask(taskId) {
          // 清除之前的选择
          this.container.querySelectorAll(".selected").forEach((el) => {
            el.classList.remove("selected");
          });

          // 添加选择状态
          const tableRow = this.container.querySelector(`[data-task-id="${taskId}"]`);
          if (tableRow) {
            tableRow.classList.add("selected");
            tableRow.style.backgroundColor = "#e3f2fd";
          }

          const taskBar = this.elements.svg.querySelector(`[data-task-id="${taskId}"]`);
          if (taskBar) {
            taskBar.style.filter = "brightness(1.2)";
          }

          this.state.selectedTasks.clear();
          this.state.selectedTasks.add(taskId);

          console.log(`Task selected: ${taskId}`);

          // 触发回调
          if (this.options.onTaskClick) {
            const task = this.data.find((t) => t.id === taskId);
            this.options.onTaskClick(task);
          }
        }

        // 选择里程碑
        selectMilestone(milestoneId) {
          console.log(`Milestone selected: ${milestoneId}`);

          // 触发回调
          if (this.options.onMilestoneClick) {
            let milestone = null;
            for (const task of this.data) {
              if (task.milestones) {
                milestone = task.milestones.find((m) => m.id === milestoneId);
                if (milestone) break;
              }
            }
            if (milestone) {
              this.options.onMilestoneClick(milestone);
            }
          }
        }

        // 缩放功能（占位）
        zoomIn() {
          console.log("Zoom in - 功能将在后续步骤实现");
          this.showMessage("缩放功能将在时间轴系统完成后实现");
        }

        zoomOut() {
          console.log("Zoom out - 功能将在后续步骤实现");
          this.showMessage("缩放功能将在时间轴系统完成后实现");
        }

        fitToView() {
          console.log("Fit to view - 功能将在后续步骤实现");
          this.showMessage("适应窗口功能将在时间轴系统完成后实现");
        }

        // 刷新
        refresh() {
          console.log("Refreshing gantt chart...");
          this.renderTable();
          this.renderChart();
          this.updateStatus();
          this.showMessage("甘特图已刷新");
        }

        // 窗口大小调整
        handleResize() {
          console.log("Window resized - adjusting layout");
          // 重新计算布局
          setTimeout(() => {
            this.renderChart();
          }, 100);
        }

        // 更新状态
        updateStatus() {
          const completedTasks = this.data.filter((task) => task.status === "completed").length;
          const inProgressTasks = this.data.filter((task) => task.status === "in-progress").length;
          const pendingTasks = this.data.filter((task) => task.status === "pending").length;

          this.elements.status.innerHTML = `
                    <span>总任务: ${this.data.length}</span>
                    <span>已完成: ${completedTasks}</span>
                    <span>进行中: ${inProgressTasks}</span>
                    <span>待开始: ${pendingTasks}</span>
                    <span>视图: ${this.state.viewMode}</span>
                `;
        }

        // 显示消息
        showMessage(message) {
          // 创建消息提示
          const messageEl = document.createElement("div");
          messageEl.textContent = message;
          messageEl.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    background: #4A90E2;
                    color: white;
                    padding: 8px 16px;
                    border-radius: 4px;
                    font-size: 14px;
                    z-index: 1000;
                    transition: opacity 0.3s ease;
                `;

          document.body.appendChild(messageEl);

          setTimeout(() => {
            messageEl.style.opacity = "0";
            setTimeout(() => {
              document.body.removeChild(messageEl);
            }, 300);
          }, 2000);
        }

        // 公共 API
        setData(data) {
          this.data = data;
          this.validateData();
          this.renderTable();
          this.renderChart();
          this.updateStatus();
          console.log("Data updated");
        }

        getData() {
          return this.data;
        }

        updateOptions(newOptions) {
          this.options = { ...this.options, ...newOptions };
          this.renderTable();
          this.renderChart();
          console.log("Options updated");
        }

        getSelectedTasks() {
          return Array.from(this.state.selectedTasks);
        }

        // 添加任务
        addTask(task) {
          // 生成 ID
          if (!task.id) {
            task.id = "task-" + Date.now();
          }

          this.data.push(task);
          this.renderTable();
          this.renderChart();
          this.updateStatus();
          console.log(`Task added: ${task.id}`);

          if (this.options.onDataChange) {
            this.options.onDataChange(this.data);
          }
        }

        // 移除任务
        removeTask(taskId) {
          const index = this.data.findIndex((task) => task.id === taskId);
          if (index > -1) {
            this.data.splice(index, 1);
            this.renderTable();
            this.renderChart();
            this.updateStatus();
            console.log(`Task removed: ${taskId}`);

            if (this.options.onDataChange) {
              this.options.onDataChange(this.data);
            }
          }
        }

        // 销毁
        destroy() {
          // 清理事件监听器
          window.removeEventListener("resize", this.handleResize);
          this.container.innerHTML = "";
          console.log("GanttChart destroyed");
        }
      }

      // =============================================
      // 初始化演示
      // =============================================

      // 等待 DOM 加载完成
      document.addEventListener("DOMContentLoaded", () => {
        const container = document.getElementById("gantt-container");

        // 创建甘特图实例
        const gantt = new GanttChart(container, {
          viewMode: "day",
          onTaskClick: (task) => {
            console.log("任务被点击:", task);
            alert(`任务: ${task.name}\n负责人: ${task.assignee}\n进度: ${Math.round(task.progress * 100)}%`);
          },
          onMilestoneClick: (milestone) => {
            console.log("里程碑被点击:", milestone);
            alert(`里程碑: ${milestone.name}\n日期: ${milestone.date}\n类型: ${milestone.type}`);
          },
          onDataChange: (data) => {
            console.log("数据变更:", data);
          },
        });

        // 将实例暴露到全局，便于调试
        window.ganttInstance = gantt;

        // 演示 API 使用
        setTimeout(() => {
          console.log("=== API 演示 ===");
          console.log("当前数据:", gantt.getData());
          console.log("选中的任务:", gantt.getSelectedTasks());

          // 演示添加任务
          // gantt.addTask({
          //     name: '新增任务',
          //     startDate: '2024-04-01',
          //     endDate: '2024-04-05',
          //     duration: 5,
          //     progress: 0,
          //     assignee: '新员工',
          //     level: 0,
          //     status: 'pending'
          // });
        }, 1000);
      });

      // 添加一些交互样式
      const additionalStyles = document.createElement("style");
      additionalStyles.textContent = `
            .gantt-table-row.selected {
                background-color: #e3f2fd !important;
                border-left: 3px solid #4A90E2;
            }
            
            .task-bar {
                cursor: pointer;
                transition: all 0.2s ease;
            }
            
            .task-bar:hover {
                filter: brightness(1.1);
                stroke: #333;
                stroke-width: 1;
            }
            
            [data-milestone-id] {
                cursor: pointer;
                transition: all 0.2s ease;
            }
            
            [data-milestone-id]:hover {
                filter: brightness(1.2);
                transform: scale(1.1);
            }
            
            .gantt-table-row {
                cursor: pointer;
            }
            
            .demo-section {
                margin: 20px 0;
                padding: 15px;
                border: 1px solid #e0e0e0;
                border-radius: 8px;
                background-color: #f9f9f9;
            }
            
            .demo-section h4 {
                margin: 0 0 10px 0;
                color: #4A90E2;
            }
            
            .demo-section p {
                margin: 5px 0;
                font-size: 14px;
                color: #666;
            }
        `;
      document.head.appendChild(additionalStyles);
    </script>

    <!-- 演示说明 -->
    <div class="demo-section">
      <h4>🎉 Step 1 完成功能展示</h4>
      <p><strong>✅ 已实现的功能：</strong></p>
      <ul style="margin: 10px 0; padding-left: 20px">
        <li>完整的甘特图布局框架（头部工具栏、左侧表格、右侧图表、底部状态栏）</li>
        <li>响应式 CSS 样式系统，支持主题变量</li>
        <li>可配置的表格列显示系统</li>
        <li>基础的 SVG 渲染架构（分层管理：网格、任务、里程碑）</li>
        <li>示例任务条和里程碑的可视化显示</li>
        <li>交互功能：任务选择、视图切换、面板拖拽调整</li>
        <li>滚动同步机制（左右面板联动）</li>
        <li>完整的事件系统和 API 接口</li>
      </ul>

      <p><strong>🎮 交互操作：</strong></p>
      <ul style="margin: 10px 0; padding-left: 20px">
        <li>点击任务行或任务条查看任务信息</li>
        <li>点击里程碑菱形标记查看里程碑信息</li>
        <li>切换不同的时间视图（日/周/月/季度）</li>
        <li>拖拽中间分割器调整面板宽度</li>
        <li>使用工具栏按钮（刷新等功能）</li>
      </ul>

      <p><strong>🚀 下一步开发：</strong></p>
      <p>Step 2 将实现数据结构定义、工具类系统和事件管理机制，为后续的时间轴计算和精确渲染奠定基础。</p>
    </div>
  </body>
</html>

{"metadata": {"name": "简单项目示例", "description": "一个简单的项目甘特图示例，适合初学者理解", "version": "1.0.0", "created": "2024-01-01", "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "project": {"name": "网站建设", "startDate": "2024-01-01", "endDate": "2024-01-31", "description": "建设一个简单的企业网站"}, "tasks": [{"id": "task1", "name": "需求分析", "startDate": "2024-01-01", "endDate": "2024-01-05", "progress": 100, "status": "completed", "dependencies": [], "assignee": "产品经理", "priority": "high", "timePoints": [{"id": "p1", "date": "2024-01-01", "name": "需求收集", "type": "start"}, {"id": "p2", "date": "2024-01-03", "name": "需求确认", "type": "checkpoint"}, {"id": "p3", "date": "2024-01-05", "name": "需求文档", "type": "end"}]}, {"id": "task2", "name": "设计阶段", "startDate": "2024-01-06", "endDate": "2024-01-12", "progress": 100, "status": "completed", "dependencies": ["task1"], "assignee": "设计师", "priority": "medium", "timePoints": [{"id": "p4", "date": "2024-01-06", "name": "页面设计", "type": "start"}, {"id": "p5", "date": "2024-01-09", "name": "设计评审", "type": "checkpoint"}, {"id": "p6", "date": "2024-01-12", "name": "设计完成", "type": "end"}]}, {"id": "task3", "name": "前端开发", "startDate": "2024-01-13", "endDate": "2024-01-20", "progress": 80, "status": "inProgress", "dependencies": ["task2"], "assignee": "前端开发", "priority": "high", "timePoints": [{"id": "p7", "date": "2024-01-13", "name": "页面开发", "type": "start"}, {"id": "p8", "date": "2024-01-16", "name": "功能开发", "type": "checkpoint"}, {"id": "p9", "date": "2024-01-20", "name": "前端完成", "type": "end"}]}, {"id": "task4", "name": "后端开发", "startDate": "2024-01-15", "endDate": "2024-01-25", "progress": 60, "status": "inProgress", "dependencies": ["task2"], "assignee": "后端开发", "priority": "high", "timePoints": [{"id": "p10", "date": "2024-01-15", "name": "数据库设计", "type": "start"}, {"id": "p11", "date": "2024-01-18", "name": "API开发", "type": "checkpoint"}, {"id": "p12", "date": "2024-01-25", "name": "后端完成", "type": "end"}]}, {"id": "task5", "name": "测试部署", "startDate": "2024-01-21", "endDate": "2024-01-31", "progress": 0, "status": "notStarted", "dependencies": ["task3", "task4"], "assignee": "测试工程师", "priority": "medium", "timePoints": [{"id": "p13", "date": "2024-01-21", "name": "功能测试", "type": "start"}, {"id": "p14", "date": "2024-01-25", "name": "部署上线", "type": "checkpoint"}, {"id": "p15", "date": "2024-01-31", "name": "项目完成", "type": "end"}]}, {"id": "milestone1", "name": "需求确认", "startDate": "2024-01-05", "endDate": "2024-01-05", "progress": 100, "status": "completed", "dependencies": ["task1"], "assignee": "项目经理", "priority": "high", "isMilestone": true, "timePoints": [{"id": "m1", "date": "2024-01-05", "name": "需求确认", "type": "milestone"}]}, {"id": "milestone2", "name": "开发完成", "startDate": "2024-01-25", "endDate": "2024-01-25", "progress": 0, "status": "notStarted", "dependencies": ["task3", "task4"], "assignee": "项目经理", "priority": "high", "isMilestone": true, "timePoints": [{"id": "m2", "date": "2024-01-25", "name": "开发完成", "type": "milestone"}]}, {"id": "milestone3", "name": "项目上线", "startDate": "2024-01-31", "endDate": "2024-01-31", "progress": 0, "status": "notStarted", "dependencies": ["task5"], "assignee": "项目经理", "priority": "high", "isMilestone": true, "timePoints": [{"id": "m3", "date": "2024-01-31", "name": "项目上线", "type": "milestone"}]}]}
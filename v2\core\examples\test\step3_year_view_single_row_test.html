<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Step3: 年视图单行时间轴测试</title>
    <link rel="stylesheet" href="../../src/styles/gantt.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #7c3aed 0%, #5b21b6 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }

        .header h1 {
            margin: 0;
            font-size: 24px;
        }

        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
        }

        .view-controls {
            padding: 20px;
            background: #f3f4f6;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            justify-content: center;
            gap: 15px;
            flex-wrap: wrap;
        }

        .view-btn {
            padding: 10px 20px;
            border: 2px solid #7c3aed;
            border-radius: 8px;
            background: white;
            color: #7c3aed;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.2s ease;
            min-width: 100px;
        }

        .view-btn:hover {
            background: #7c3aed;
            color: white;
            transform: translateY(-1px);
        }

        .view-btn.active {
            background: #7c3aed;
            color: white;
            box-shadow: 0 4px 12px rgba(124, 58, 237, 0.3);
        }

        .info-panel {
            padding: 20px;
            background: #faf5ff;
            border-bottom: 1px solid #e9d5ff;
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .info-card {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #7c3aed;
        }

        .info-label {
            font-size: 12px;
            color: #6b7280;
            text-transform: uppercase;
            margin-bottom: 5px;
        }

        .info-value {
            font-size: 16px;
            font-weight: 600;
            color: #374151;
            font-family: monospace;
        }

        .gantt-container {
            height: 500px;
            position: relative;
            border: 2px solid #7c3aed;
        }

        .comparison-section {
            padding: 20px;
            background: #f9fafb;
            border-top: 1px solid #e5e7eb;
        }

        .comparison-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 15px;
        }

        .comparison-item {
            background: white;
            padding: 20px;
            border-radius: 8px;
            border: 2px solid #e5e7eb;
        }

        .comparison-item.year-view {
            border-color: #7c3aed;
        }

        .comparison-item h4 {
            margin: 0 0 15px 0;
            color: #374151;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .feature-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .feature-list li {
            padding: 8px 0;
            color: #6b7280;
            font-size: 14px;
            border-bottom: 1px solid #f3f4f6;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .feature-list li:last-child {
            border-bottom: none;
        }

        .feature-list li::before {
            content: "✓";
            color: #10b981;
            font-weight: bold;
            font-size: 16px;
        }

        .year-view .feature-list li::before {
            color: #7c3aed;
        }

        .theme-toggle {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 10px 15px;
            background: #7c3aed;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            z-index: 1000;
        }

        .theme-toggle:hover {
            background: #5b21b6;
        }

        /* 增强年视图时间轴的可见性 */
        .timeline-scales-single-row .timeline-scale.bottom-row {
            border-right: 2px solid #7c3aed !important;
        }

        .timeline-scales-single-row .timeline-scale.bottom-row:hover {
            background: rgba(124, 58, 237, 0.1) !important;
        }

        .timeline-scales-single-row .timeline-scale.bottom-row.major {
            background: rgba(124, 58, 237, 0.05) !important;
            border-right: 3px solid #7c3aed !important;
        }

        /* 深色主题切换 */
        .dark-theme {
            filter: invert(1) hue-rotate(180deg);
        }

        .dark-theme .gantt-container {
            filter: invert(1) hue-rotate(180deg);
        }
    </style>
</head>
<body>
    <button class="theme-toggle" onclick="toggleTheme()">🌙 切换深色主题</button>

    <div class="container">
        <div class="header">
            <h1>📅 年视图单行时间轴测试</h1>
            <p>展示年视图下简洁的单行时间轴设计</p>
        </div>

        <div class="view-controls">
            <button class="view-btn" data-mode="day">日视图</button>
            <button class="view-btn" data-mode="week">周视图</button>
            <button class="view-btn" data-mode="month">月视图</button>
            <button class="view-btn" data-mode="quarter">季度视图</button>
            <button class="view-btn active" data-mode="year">年视图</button>
        </div>

        <div class="info-panel">
            <h3>当前视图信息</h3>
            <div class="info-grid">
                <div class="info-card">
                    <div class="info-label">当前视图模式</div>
                    <div class="info-value" id="currentViewMode">年视图</div>
                </div>
                <div class="info-card">
                    <div class="info-label">时间轴行数</div>
                    <div class="info-value" id="timelineRows">1行</div>
                </div>
                <div class="info-card">
                    <div class="info-label">刻度宽度</div>
                    <div class="info-value" id="scaleWidth">-</div>
                </div>
                <div class="info-card">
                    <div class="info-label">可见刻度数</div>
                    <div class="info-value" id="visibleScales">-</div>
                </div>
            </div>
        </div>

        <div class="gantt-container" id="ganttContainer"></div>

        <div class="comparison-section">
            <h3>设计对比</h3>
            <div class="comparison-grid">
                <div class="comparison-item">
                    <h4>📊 其他视图模式 (双行)</h4>
                    <ul class="feature-list">
                        <li>顶部行显示时间分组</li>
                        <li>底部行显示具体时间</li>
                        <li>复杂的层次结构</li>
                        <li>适合详细时间信息</li>
                        <li>占用更多垂直空间</li>
                    </ul>
                </div>
                <div class="comparison-item year-view">
                    <h4>📅 年视图模式 (单行)</h4>
                    <ul class="feature-list">
                        <li>简洁的单行设计</li>
                        <li>直接显示年份信息</li>
                        <li>更大的字体和间距</li>
                        <li>节省垂直空间</li>
                        <li>适合长期项目规划</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script type="module">
        import GanttChart from '../../src/GanttChart.js';
        import DateUtils from '../../src/utils/DateUtils.js';

        // 生成长期项目数据
        function generateLongTermData() {
            const tasks = [];
            const startDate = new Date('2024-01-01');
            
            for (let i = 0; i < 8; i++) {
                const taskStart = new Date(startDate.getFullYear() + i, 0, 1);
                const duration = 365 + Math.random() * 730; // 1-3年的项目
                const taskEnd = DateUtils.addDays(taskStart, duration);
                
                tasks.push({
                    id: `long-term-${i}`,
                    name: `长期项目 ${i + 1}`,
                    startDate: taskStart,
                    endDate: taskEnd,
                    progress: Math.random()
                });
            }
            
            return tasks;
        }

        // 初始化甘特图
        let ganttInstance = null;

        function initializeGantt() {
            const container = document.getElementById('ganttContainer');
            container.innerHTML = '';
            
            const testData = generateLongTermData();
            
            ganttInstance = new GanttChart('ganttContainer', {
                data: testData,
                viewMode: 'year',
                pixelsPerDay: 2, // 年视图使用很小的像素密度
                timeline: {
                    showWeekday: false,
                    adaptiveWidth: true,
                    minScaleWidth: 80,
                    maxScaleWidth: 150,
                    weekdayFormat: 'short'
                },
                taskList: {
                    columns: [
                        { key: 'name', title: '项目名称', width: 200 },
                        { key: 'progress', title: '进度', width: 80 }
                    ]
                }
            });

            ganttInstance.on('ready', () => {
                updateInfo();
            });

            ganttInstance.on('viewChange', () => {
                updateInfo();
            });
        }

        // 更新信息显示
        function updateInfo() {
            if (!ganttInstance || !ganttInstance.timeScale) return;

            setTimeout(() => {
                const viewMode = ganttInstance.state.viewMode;
                const scales = ganttInstance.timeScale.getAllScales();
                const scaleWidth = scales.length > 1 ? (scales[1].x - scales[0].x) : 80;
                
                // 检测时间轴行数
                const singleRow = document.querySelector('.timeline-scales-single-row');
                const doubleRow = document.querySelector('.timeline-scales-top-row');
                const timelineRows = singleRow ? '1行 (单行模式)' : (doubleRow ? '2行 (双行模式)' : '未知');
                
                // 更新显示
                document.getElementById('currentViewMode').textContent = getViewModeName(viewMode);
                document.getElementById('timelineRows').textContent = timelineRows;
                document.getElementById('scaleWidth').textContent = `${scaleWidth.toFixed(1)}px`;
                document.getElementById('visibleScales').textContent = scales.length;
                
            }, 100);
        }

        function getViewModeName(mode) {
            const names = {
                'day': '日视图',
                'week': '周视图',
                'month': '月视图',
                'quarter': '季度视图',
                'year': '年视图'
            };
            return names[mode] || mode;
        }

        // 主题切换
        function toggleTheme() {
            document.body.classList.toggle('dark-theme');
            const button = document.querySelector('.theme-toggle');
            if (document.body.classList.contains('dark-theme')) {
                button.textContent = '☀️ 切换亮色主题';
            } else {
                button.textContent = '🌙 切换深色主题';
            }
        }

        // 视图切换
        document.querySelectorAll('.view-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                document.querySelectorAll('.view-btn').forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
                
                const mode = btn.dataset.mode;
                ganttInstance.changeViewMode(mode);
            });
        });

        // 全局函数
        window.toggleTheme = toggleTheme;

        // 初始化
        document.addEventListener('DOMContentLoaded', () => {
            console.log('页面加载完成，初始化年视图单行测试...');
            initializeGantt();
        });
    </script>
</body>
</html>

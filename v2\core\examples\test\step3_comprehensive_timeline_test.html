<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Step3: 时间轴综合适配测试</title>
    <link rel="stylesheet" href="../../src/styles/gantt.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .container {
            max-width: 1600px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }

        .header h1 {
            margin: 0;
            font-size: 24px;
        }

        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
        }

        .test-controls {
            padding: 20px;
            background: #f8fafc;
            border-bottom: 1px solid #e2e8f0;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .control-section {
            background: white;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #e2e8f0;
        }

        .control-section h3 {
            margin: 0 0 15px 0;
            color: #374151;
            font-size: 16px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .view-mode-buttons {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 10px;
            margin-bottom: 15px;
        }

        .view-mode-btn {
            padding: 10px;
            border: 2px solid #e2e8f0;
            border-radius: 6px;
            background: white;
            cursor: pointer;
            text-align: center;
            transition: all 0.2s ease;
            font-size: 14px;
        }

        .view-mode-btn:hover {
            border-color: #8b5cf6;
            background: #f3f4f6;
        }

        .view-mode-btn.active {
            border-color: #8b5cf6;
            background: #ede9fe;
            color: #7c3aed;
            font-weight: 600;
        }

        .zoom-controls {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .zoom-slider {
            width: 100%;
        }

        .zoom-buttons {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 8px;
        }

        .zoom-btn {
            padding: 8px;
            border: 1px solid #d1d5db;
            border-radius: 4px;
            background: white;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.2s ease;
        }

        .zoom-btn:hover {
            background: #f3f4f6;
            border-color: #8b5cf6;
        }

        .timeline-config {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .config-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #f3f4f6;
        }

        .config-item:last-child {
            border-bottom: none;
        }

        .config-label {
            font-size: 13px;
            color: #6b7280;
        }

        .config-value {
            font-size: 14px;
            font-weight: 600;
            color: #374151;
            font-family: monospace;
        }

        .gantt-container {
            height: 500px;
            position: relative;
            border: 2px solid #8b5cf6;
        }

        .status-panel {
            padding: 20px;
            background: #f1f5f9;
            border-top: 1px solid #e2e8f0;
        }

        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .status-card {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #8b5cf6;
        }

        .status-title {
            font-size: 12px;
            color: #6b7280;
            text-transform: uppercase;
            margin-bottom: 8px;
        }

        .status-value {
            font-size: 18px;
            font-weight: 600;
            color: #374151;
            font-family: monospace;
        }

        .test-scenarios {
            padding: 20px;
            background: #fef7e6;
            border-top: 1px solid #f59e0b;
        }

        .test-scenarios h3 {
            margin: 0 0 15px 0;
            color: #92400e;
        }

        .scenario-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
        }

        .scenario-btn {
            padding: 12px;
            border: 2px solid #f59e0b;
            border-radius: 6px;
            background: white;
            cursor: pointer;
            text-align: center;
            transition: all 0.2s ease;
            font-size: 13px;
        }

        .scenario-btn:hover {
            background: #fef3c7;
            transform: translateY(-1px);
        }

        /* 增强时间轴可见性 */
        .timeline-scale.bottom-row {
            border-right: 1px solid #8b5cf6 !important;
        }

        .timeline-scale.bottom-row.major {
            background: rgba(139, 92, 246, 0.05) !important;
        }

        .timeline-scale.bottom-row .weekday-label {
            background: rgba(139, 92, 246, 0.1) !important;
            border-radius: 2px;
        }

        .timeline-scale.bottom-row .year-label {
            background: rgba(139, 92, 246, 0.08) !important;
            border-radius: 2px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔄 时间轴综合适配测试</h1>
            <p>测试时间轴在不同视图模式和缩放级别下的智能适配功能</p>
        </div>

        <div class="test-controls">
            <div class="control-section">
                <h3>📅 视图模式切换</h3>
                <div class="view-mode-buttons">
                    <div class="view-mode-btn active" data-mode="day">日视图</div>
                    <div class="view-mode-btn" data-mode="week">周视图</div>
                    <div class="view-mode-btn" data-mode="month">月视图</div>
                    <div class="view-mode-btn" data-mode="quarter">季度视图</div>
                </div>
                <div class="config-item">
                    <span class="config-label">当前视图</span>
                    <span class="config-value" id="currentViewMode">日视图</span>
                </div>
                <div class="config-item">
                    <span class="config-label">显示周几</span>
                    <span class="config-value" id="showWeekdayStatus">是</span>
                </div>
            </div>

            <div class="control-section">
                <h3>🔍 缩放控制</h3>
                <div class="zoom-controls">
                    <input type="range" id="zoomSlider" class="zoom-slider" 
                           min="0.3" max="3.0" step="0.1" value="1.0">
                    <div class="config-item">
                        <span class="config-label">缩放级别</span>
                        <span class="config-value" id="zoomLevel">100%</span>
                    </div>
                    <div class="zoom-buttons">
                        <button class="zoom-btn" id="zoomOutBtn">缩小</button>
                        <button class="zoom-btn" id="fitViewBtn">适应</button>
                        <button class="zoom-btn" id="zoomInBtn">放大</button>
                    </div>
                </div>
            </div>

            <div class="control-section">
                <h3>⚙️ 时间轴配置</h3>
                <div class="timeline-config">
                    <div class="config-item">
                        <span class="config-label">实际刻度宽度</span>
                        <span class="config-value" id="actualScaleWidth">-</span>
                    </div>
                    <div class="config-item">
                        <span class="config-label">像素密度</span>
                        <span class="config-value" id="pixelsPerDay">-</span>
                    </div>
                    <div class="config-item">
                        <span class="config-label">总刻度数</span>
                        <span class="config-value" id="totalScales">-</span>
                    </div>
                    <div class="config-item">
                        <span class="config-label">可见刻度数</span>
                        <span class="config-value" id="visibleScales">-</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="gantt-container" id="ganttContainer"></div>

        <div class="status-panel">
            <h3>📊 适配状态监控</h3>
            <div class="status-grid">
                <div class="status-card">
                    <div class="status-title">日视图周几显示</div>
                    <div class="status-value" id="dayViewWeekdayCount">-</div>
                </div>
                <div class="status-card">
                    <div class="status-title">月视图年份显示</div>
                    <div class="status-value" id="monthViewYearCount">-</div>
                </div>
                <div class="status-card">
                    <div class="status-title">季度视图年份显示</div>
                    <div class="status-value" id="quarterViewYearCount">-</div>
                </div>
                <div class="status-card">
                    <div class="status-title">自适应效果</div>
                    <div class="status-value" id="adaptiveStatus">良好</div>
                </div>
            </div>
        </div>

        <div class="test-scenarios">
            <h3>🧪 快速测试场景</h3>
            <div class="scenario-buttons">
                <button class="scenario-btn" data-scenario="day-zoom-in">日视图+放大</button>
                <button class="scenario-btn" data-scenario="day-zoom-out">日视图+缩小</button>
                <button class="scenario-btn" data-scenario="week-normal">周视图+标准</button>
                <button class="scenario-btn" data-scenario="month-zoom-in">月视图+放大</button>
                <button class="scenario-btn" data-scenario="quarter-zoom-out">季度视图+缩小</button>
                <button class="scenario-btn" data-scenario="stress-test">压力测试</button>
            </div>
        </div>
    </div>

    <script type="module">
        import GanttChart from '../../src/GanttChart.js';
        import DateUtils from '../../src/utils/DateUtils.js';

        // 生成测试数据
        function generateTestData() {
            const tasks = [];
            const startDate = new Date('2024-01-01');
            
            for (let i = 0; i < 15; i++) {
                const taskStart = DateUtils.addDays(startDate, i * 8);
                const duration = 10 + Math.random() * 20;
                const taskEnd = DateUtils.addDays(taskStart, duration);
                
                tasks.push({
                    id: `comprehensive-task-${i}`,
                    name: `综合测试任务 ${i + 1}`,
                    startDate: taskStart,
                    endDate: taskEnd,
                    progress: Math.random()
                });
            }
            
            return tasks;
        }

        // 初始化甘特图
        let ganttInstance = null;

        function initializeGantt() {
            const container = document.getElementById('ganttContainer');
            container.innerHTML = '';
            
            const testData = generateTestData();
            
            ganttInstance = new GanttChart('ganttContainer', {
                data: testData,
                viewMode: 'day',
                pixelsPerDay: 35,
                timeline: {
                    showWeekday: true,
                    adaptiveWidth: true,
                    minScaleWidth: 30,
                    maxScaleWidth: 100,
                    weekdayFormat: 'short'
                },
                taskList: {
                    columns: [
                        { key: 'name', title: '任务名称', width: 200 },
                        { key: 'progress', title: '进度', width: 80 }
                    ]
                }
            });

            ganttInstance.on('ready', () => {
                updateStatus();
            });

            ganttInstance.on('viewChange', () => {
                updateStatus();
            });

            ganttInstance.on('zoomChange', () => {
                updateStatus();
            });
        }

        // 更新状态显示
        function updateStatus() {
            if (!ganttInstance || !ganttInstance.timeScale) return;

            setTimeout(() => {
                const viewMode = ganttInstance.state.viewMode;
                const zoomLevel = ganttInstance.state.zoomLevel;
                const scales = ganttInstance.timeScale.getAllScales();
                const scaleWidth = scales.length > 1 ? (scales[1].x - scales[0].x) : 35;
                
                // 基本信息
                document.getElementById('currentViewMode').textContent = getViewModeName(viewMode);
                document.getElementById('zoomLevel').textContent = `${Math.round(zoomLevel * 100)}%`;
                document.getElementById('actualScaleWidth').textContent = `${scaleWidth.toFixed(1)}px`;
                document.getElementById('pixelsPerDay').textContent = `${(ganttInstance.options.pixelsPerDay * zoomLevel).toFixed(1)}px`;
                document.getElementById('totalScales').textContent = scales.length;
                
                // 滑块同步
                document.getElementById('zoomSlider').value = zoomLevel;
                
                // 特定视图模式的状态
                updateViewModeStatus(viewMode);
                
                // 周几显示状态
                const showWeekday = ganttInstance.options.timeline.showWeekday && viewMode === 'day';
                document.getElementById('showWeekdayStatus').textContent = showWeekday ? '是' : '否';
                
            }, 100);
        }

        function getViewModeName(mode) {
            const names = {
                'day': '日视图',
                'week': '周视图', 
                'month': '月视图',
                'quarter': '季度视图'
            };
            return names[mode] || mode;
        }

        function updateViewModeStatus(viewMode) {
            const weekdayElements = document.querySelectorAll('.timeline-scale.bottom-row .weekday-label');
            const yearElements = document.querySelectorAll('.timeline-scale.bottom-row .year-label');
            
            document.getElementById('dayViewWeekdayCount').textContent = 
                viewMode === 'day' ? weekdayElements.length : 'N/A';
            document.getElementById('monthViewYearCount').textContent = 
                viewMode === 'month' ? yearElements.length : 'N/A';
            document.getElementById('quarterViewYearCount').textContent = 
                viewMode === 'quarter' ? yearElements.length : 'N/A';
                
            // 自适应效果评估
            const visibleElements = document.querySelectorAll('.timeline-scale.bottom-row:not([style*="display: none"])');
            document.getElementById('visibleScales').textContent = visibleElements.length;
            
            const adaptiveStatus = visibleElements.length > 0 ? '良好' : '需要调整';
            document.getElementById('adaptiveStatus').textContent = adaptiveStatus;
        }

        // 事件绑定
        document.querySelectorAll('.view-mode-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                document.querySelectorAll('.view-mode-btn').forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
                
                const mode = btn.dataset.mode;
                ganttInstance.changeViewMode(mode);
            });
        });

        document.getElementById('zoomSlider').addEventListener('input', (e) => {
            const zoomLevel = parseFloat(e.target.value);
            ganttInstance.setZoomLevel(zoomLevel);
        });

        document.getElementById('zoomInBtn').addEventListener('click', () => {
            ganttInstance.zoomIn();
        });

        document.getElementById('zoomOutBtn').addEventListener('click', () => {
            ganttInstance.zoomOut();
        });

        document.getElementById('fitViewBtn').addEventListener('click', () => {
            ganttInstance.fitToView();
        });

        // 测试场景
        document.querySelectorAll('.scenario-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                const scenario = btn.dataset.scenario;
                executeTestScenario(scenario);
            });
        });

        function executeTestScenario(scenario) {
            switch (scenario) {
                case 'day-zoom-in':
                    ganttInstance.changeViewMode('day');
                    setTimeout(() => ganttInstance.setZoomLevel(2.0), 200);
                    break;
                case 'day-zoom-out':
                    ganttInstance.changeViewMode('day');
                    setTimeout(() => ganttInstance.setZoomLevel(0.5), 200);
                    break;
                case 'week-normal':
                    ganttInstance.changeViewMode('week');
                    setTimeout(() => ganttInstance.setZoomLevel(1.0), 200);
                    break;
                case 'month-zoom-in':
                    ganttInstance.changeViewMode('month');
                    setTimeout(() => ganttInstance.setZoomLevel(1.8), 200);
                    break;
                case 'quarter-zoom-out':
                    ganttInstance.changeViewMode('quarter');
                    setTimeout(() => ganttInstance.setZoomLevel(0.7), 200);
                    break;
                case 'stress-test':
                    // 快速切换测试
                    const modes = ['day', 'week', 'month', 'quarter'];
                    const zooms = [0.5, 1.0, 1.5, 2.0];
                    let index = 0;
                    
                    const interval = setInterval(() => {
                        const mode = modes[index % modes.length];
                        const zoom = zooms[index % zooms.length];
                        
                        ganttInstance.changeViewMode(mode);
                        setTimeout(() => ganttInstance.setZoomLevel(zoom), 100);
                        
                        index++;
                        if (index >= 8) {
                            clearInterval(interval);
                            ganttInstance.changeViewMode('day');
                            ganttInstance.setZoomLevel(1.0);
                        }
                    }, 800);
                    break;
            }
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', () => {
            console.log('页面加载完成，初始化综合适配测试...');
            initializeGantt();
        });
    </script>
</body>
</html>

<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>甘特图 Step 2 - 数据结构和工具系统演示</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
          sans-serif;
        background: #f5f5f7;
        color: #1d1d1f;
        line-height: 1.6;
      }

      .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
      }

      .header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 2rem;
        border-radius: 12px;
        margin-bottom: 2rem;
        text-align: center;
      }

      .header h1 {
        font-size: 2.5rem;
        margin-bottom: 0.5rem;
        font-weight: 700;
      }

      .header p {
        font-size: 1.1rem;
        opacity: 0.9;
      }

      .demo-section {
        background: white;
        border-radius: 12px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07);
        border: 1px solid #e5e5e7;
      }

      .demo-section h2 {
        color: #1d1d1f;
        margin-bottom: 1rem;
        font-size: 1.5rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
      }

      .demo-section h3 {
        color: #424245;
        margin: 1.5rem 0 1rem 0;
        font-size: 1.2rem;
      }

      .code-block {
        background: #f6f6f6;
        border: 1px solid #d2d2d7;
        border-radius: 8px;
        padding: 1rem;
        font-family: "Monaco", "Menlo", monospace;
        font-size: 0.9rem;
        overflow-x: auto;
        margin: 1rem 0;
      }

      .output {
        background: #e8f5e8;
        border: 1px solid #a7d7a7;
        border-radius: 8px;
        padding: 1rem;
        margin: 1rem 0;
        white-space: pre-wrap;
        font-family: monospace;
        max-height: 300px;
        overflow-y: auto;
      }

      .button {
        background: #007aff;
        color: white;
        border: none;
        padding: 0.75rem 1.5rem;
        border-radius: 8px;
        font-size: 1rem;
        cursor: pointer;
        transition: all 0.2s ease;
        margin: 0.5rem 0.5rem 0.5rem 0;
      }

      .button:hover {
        background: #0056b3;
        transform: translateY(-1px);
      }

      .button.secondary {
        background: #8e8e93;
      }

      .button.secondary:hover {
        background: #6d6d70;
      }

      .grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 1rem;
        margin: 1rem 0;
      }

      .card {
        background: #f9f9f9;
        border: 1px solid #e5e5e7;
        border-radius: 8px;
        padding: 1rem;
      }

      .card h4 {
        color: #1d1d1f;
        margin-bottom: 0.5rem;
      }

      .timeline-demo {
        border-left: 4px solid #007aff;
        padding-left: 1rem;
        margin: 1rem 0;
      }

      .milestone {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin: 0.5rem 0;
        padding: 0.5rem;
        background: #f6f6f6;
        border-radius: 6px;
      }

      .milestone-icon {
        width: 16px;
        height: 16px;
        border-radius: 50%;
        flex-shrink: 0;
      }

      .milestone-icon.completed {
        background: #34c759;
      }
      .milestone-icon.in-progress {
        background: #ff9500;
      }
      .milestone-icon.pending {
        background: #8e8e93;
      }

      .status-badge {
        display: inline-block;
        padding: 0.25rem 0.5rem;
        border-radius: 4px;
        font-size: 0.8rem;
        font-weight: 500;
      }

      .status-completed {
        background: #d1f2d1;
        color: #2d5a2d;
      }
      .status-in-progress {
        background: #ffe4b3;
        color: #8b4513;
      }
      .status-pending {
        background: #e8e8e8;
        color: #4a4a4a;
      }

      .icon {
        width: 24px;
        height: 24px;
        display: inline-block;
      }

      @media (max-width: 768px) {
        .container {
          padding: 10px;
        }

        .header h1 {
          font-size: 2rem;
        }

        .demo-section {
          padding: 1rem;
        }
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="header">
        <h1>🛠️ Step 2: 数据结构和工具系统</h1>
        <p>完整的数据类型定义、日期处理工具和事件系统</p>
      </div>

      <!-- 数据结构展示 -->
      <div class="demo-section">
        <h2>📊 优化的数据结构</h2>
        <p>
          支持多条时间线的里程碑系统，每个任务可以包含多条时间线，每条时间线包含多个里程碑。
        </p>

        <h3>🎯 核心特性</h3>
        <div class="grid">
          <div class="card">
            <h4>多时间线支持</h4>
            <p>
              一个任务可以包含技术里程碑、业务里程碑、交付里程碑等多条时间线
            </p>
          </div>
          <div class="card">
            <h4>丰富的里程碑类型</h4>
            <p>支持评审、交付、审批、警告、信息等多种里程碑类型和图标</p>
          </div>
          <div class="card">
            <h4>完整的验证系统</h4>
            <p>自动验证数据完整性和合理性，提供详细的错误和警告信息</p>
          </div>
          <div class="card">
            <h4>向后兼容</h4>
            <p>支持从旧版本数据格式自动迁移到新版本格式</p>
          </div>
        </div>

        <button class="button" onclick="demonstrateDataStructure()">
          🔍 展示数据结构
        </button>
        <button class="button secondary" onclick="validateSampleData()">
          ✅ 验证示例数据
        </button>

        <div
          id="dataStructureOutput"
          class="output"
          style="display: none"
        ></div>
      </div>

      <!-- 日期工具演示 -->
      <div class="demo-section">
        <h2>📅 DateUtils 工具类</h2>
        <p>强大的日期处理工具，支持多种格式、时间视图模式和工作日计算。</p>

        <h3>🔧 主要功能</h3>
        <div class="grid">
          <div class="card">
            <h4>多格式解析</h4>
            <p>支持 YYYY-MM-DD、YYYY/MM/DD、MM/DD/YYYY 等多种日期格式</p>
          </div>
          <div class="card">
            <h4>视图模式</h4>
            <p>支持日、周、月、季度、年等多种时间视图模式</p>
          </div>
          <div class="card">
            <h4>工作日计算</h4>
            <p>自动跳过周末，准确计算工作日数量和时间范围</p>
          </div>
          <div class="card">
            <h4>时间轴生成</h4>
            <p>根据视图模式自动生成时间刻度和标签</p>
          </div>
        </div>

        <button class="button" onclick="demonstrateDateUtils()">
          ⏰ 测试日期工具
        </button>
        <button class="button secondary" onclick="generateTimeScale()">
          📊 生成时间轴
        </button>

        <div id="dateUtilsOutput" class="output" style="display: none"></div>
      </div>

      <!-- SVG 工具演示 -->
      <div class="demo-section">
        <h2>🎨 SvgUtils 工具类</h2>
        <p>完整的 SVG 操作工具集，支持元素创建、样式管理和动画效果。</p>

        <h3>✨ 核心功能</h3>
        <div class="grid">
          <div class="card">
            <h4>元素创建</h4>
            <p>简化的 API 创建各种 SVG 元素：矩形、圆形、路径、文本等</p>
          </div>
          <div class="card">
            <h4>预定义组件</h4>
            <p>内置任务条、里程碑、依赖连线等甘特图专用组件</p>
          </div>
          <div class="card">
            <h4>样式管理</h4>
            <p>支持渐变、图案、滤镜等高级样式效果</p>
          </div>
          <div class="card">
            <h4>坐标转换</h4>
            <p>提供屏幕坐标与 SVG 坐标的双向转换工具</p>
          </div>
        </div>

        <button class="button" onclick="demonstrateSvgUtils()">
          🎨 演示 SVG 创建
        </button>
        <button class="button secondary" onclick="createMilestoneDemo()">
          💎 创建里程碑示例
        </button>

        <div id="svgDemo" style="margin: 1rem 0"></div>
        <div id="svgUtilsOutput" class="output" style="display: none"></div>
      </div>

      <!-- 事件系统演示 -->
      <div class="demo-section">
        <h2>📡 EventEmitter 事件系统</h2>
        <p>强大的发布订阅事件系统，支持命名空间、优先级和异步处理。</p>

        <h3>🚀 高级特性</h3>
        <div class="grid">
          <div class="card">
            <h4>优先级支持</h4>
            <p>监听器可以设置优先级，高优先级的监听器优先执行</p>
          </div>
          <div class="card">
            <h4>命名空间</h4>
            <p>支持事件命名空间，避免事件名冲突</p>
          </div>
          <div class="card">
            <h4>异步处理</h4>
            <p>支持异步事件处理和 Promise 等待</p>
          </div>
          <div class="card">
            <h4>统计信息</h4>
            <p>提供事件触发次数、监听器数量等统计信息</p>
          </div>
        </div>

        <button class="button" onclick="demonstrateEventSystem()">
          📢 测试事件系统
        </button>
        <button class="button secondary" onclick="showEventStats()">
          📊 显示统计信息
        </button>

        <div id="eventOutput" class="output" style="display: none"></div>
      </div>

      <!-- 完整示例展示 -->
      <div class="demo-section">
        <h2>🎯 完整数据示例</h2>
        <p>展示具有多条时间线的真实项目数据示例。</p>

        <div id="projectDemo"></div>

        <button class="button" onclick="showCompleteExample()">
          📋 显示完整示例
        </button>
        <button class="button secondary" onclick="analyzeProjectData()">
          📈 分析项目数据
        </button>

        <div
          id="completeExampleOutput"
          class="output"
          style="display: none"
        ></div>
      </div>
    </div>

    <!-- 引入所有工具类 -->
    <script>
      // 由于无法直接引入文件，这里内联关键代码

      // DateUtils 核心功能（简化版）
      class DateUtils {
        static parseDate(dateInput) {
          if (!dateInput) return null;
          if (dateInput instanceof Date) return dateInput;
          const date = new Date(dateInput);
          return isNaN(date.getTime()) ? null : date;
        }

        static formatDate(date, format = "YYYY-MM-DD") {
          if (!date) return "";
          const year = date.getFullYear();
          const month = String(date.getMonth() + 1).padStart(2, "0");
          const day = String(date.getDate()).padStart(2, "0");
          return `${year}-${month}-${day}`;
        }

        static getDaysDiff(startDate, endDate) {
          if (!startDate || !endDate) return 0;
          const oneDay = 24 * 60 * 60 * 1000;
          return Math.round((endDate - startDate) / oneDay);
        }

        static addDays(date, days) {
          const result = new Date(date);
          result.setDate(result.getDate() + days);
          return result;
        }

        static today() {
          const now = new Date();
          now.setHours(0, 0, 0, 0);
          return now;
        }

        static generateTimeScale(startDate, endDate, viewMode = "day") {
          const scales = [];
          let current = new Date(startDate);
          const increment =
            viewMode === "week" ? 7 : viewMode === "month" ? 30 : 1;

          while (current <= endDate) {
            scales.push({
              date: new Date(current),
              label: DateUtils.formatDate(current, "MM/DD"),
              x: 0,
            });
            current = DateUtils.addDays(current, increment);
          }
          return scales;
        }
      }

      // EventEmitter 核心功能（简化版）
      class EventEmitter {
        constructor() {
          this.events = new Map();
          this.stats = { emitted: 0, listeners: 0 };
        }

        on(event, listener) {
          if (!this.events.has(event)) {
            this.events.set(event, []);
          }
          this.events.get(event).push(listener);
          this.stats.listeners++;
          return this;
        }

        emit(event, ...args) {
          this.stats.emitted++;
          if (!this.events.has(event)) return false;

          const listeners = this.events.get(event);
          listeners.forEach((listener) => {
            try {
              listener(...args);
            } catch (error) {
              console.error("Event listener error:", error);
            }
          });
          return true;
        }

        getStats() {
          return {
            ...this.stats,
            activeEvents: this.events.size,
            totalListeners: this.stats.listeners,
          };
        }
      }

      // 示例数据类（简化版）
      class TaskItem {
        constructor(data = {}) {
          this.id = data.id || `task-${Date.now()}`;
          this.name = data.name || "";
          this.startDate = DateUtils.parseDate(data.startDate);
          this.endDate = DateUtils.parseDate(data.endDate);
          this.progress = data.progress || 0;
          this.status = data.status || "pending";
          this.timelines = data.timelines || [];
        }

        getAllMilestones() {
          return this.timelines.reduce((all, timeline) => {
            return all.concat(timeline.milestones || []);
          }, []);
        }

        getOverallProgress() {
          if (this.timelines.length === 0) return this.progress;

          const timelineProgress =
            this.timelines.reduce((sum, timeline) => {
              const completed = (timeline.milestones || []).filter(
                (m) => m.status === "completed"
              ).length;
              const total = (timeline.milestones || []).length;
              return sum + (total > 0 ? completed / total : 0);
            }, 0) / this.timelines.length;

          return this.progress * 0.7 + timelineProgress * 0.3;
        }
      }

      // 全局事件发射器
      const globalEmitter = new EventEmitter();

      // 示例数据
      const sampleData = [
        {
          id: "project-1",
          name: "企业管理系统开发",
          startDate: "2024-01-01",
          endDate: "2024-06-30",
          progress: 0.15,
          status: "in-progress",
          timelines: [
            {
              id: "timeline-tech",
              name: "技术里程碑",
              color: "#4A90E2",
              type: "default",
              milestones: [
                {
                  id: "milestone-tech-1",
                  name: "技术架构评审",
                  date: "2024-01-15",
                  type: "review",
                  status: "completed",
                },
                {
                  id: "milestone-tech-2",
                  name: "核心模块开发完成",
                  date: "2024-03-01",
                  type: "delivery",
                  status: "in-progress",
                },
              ],
            },
            {
              id: "timeline-business",
              name: "业务里程碑",
              color: "#34C759",
              type: "secondary",
              milestones: [
                {
                  id: "milestone-business-1",
                  name: "需求调研完成",
                  date: "2024-01-10",
                  type: "review",
                  status: "completed",
                },
                {
                  id: "milestone-business-2",
                  name: "用户验收测试",
                  date: "2024-05-01",
                  type: "approval",
                  status: "pending",
                },
              ],
            },
          ],
        },
      ];

      // 演示函数
      function demonstrateDataStructure() {
        const output = document.getElementById("dataStructureOutput");
        output.style.display = "block";

        let result = "=== 数据结构演示 ===\n\n";

        const task = new TaskItem(sampleData[0]);
        result += `项目: ${task.name}\n`;
        result += `时间线数量: ${task.timelines.length}\n`;
        result += `总里程碑数: ${task.getAllMilestones().length}\n`;
        result += `整体进度: ${(task.getOverallProgress() * 100).toFixed(
          1
        )}%\n\n`;

        task.timelines.forEach((timeline, index) => {
          result += `时间线 ${index + 1}: ${timeline.name}\n`;
          result += `  类型: ${timeline.type}\n`;
          result += `  颜色: ${timeline.color}\n`;
          result += `  里程碑数量: ${(timeline.milestones || []).length}\n`;

          (timeline.milestones || []).forEach((milestone) => {
            const statusIcon =
              milestone.status === "completed"
                ? "✅"
                : milestone.status === "in-progress"
                ? "🟡"
                : "⏳";
            result += `    ${statusIcon} ${milestone.name} (${milestone.date})\n`;
          });
          result += "\n";
        });

        output.textContent = result;
      }

      function validateSampleData() {
        const output = document.getElementById("dataStructureOutput");
        output.style.display = "block";

        let result = "=== 数据验证结果 ===\n\n";

        // 简单验证逻辑
        const errors = [];
        const warnings = [];

        sampleData.forEach((task, index) => {
          if (!task.name) errors.push(`任务 ${index + 1}: 名称不能为空`);
          if (!task.startDate)
            errors.push(`任务 ${index + 1}: 开始日期不能为空`);

          (task.timelines || []).forEach((timeline, tIndex) => {
            if (!timeline.name)
              errors.push(
                `任务 ${index + 1} 时间线 ${tIndex + 1}: 名称不能为空`
              );

            (timeline.milestones || []).forEach((milestone, mIndex) => {
              if (!milestone.name)
                errors.push(`里程碑 ${mIndex + 1}: 名称不能为空`);
              if (!milestone.date)
                errors.push(`里程碑 ${mIndex + 1}: 日期不能为空`);

              const date = DateUtils.parseDate(milestone.date);
              if (
                date &&
                date < DateUtils.today() &&
                milestone.status === "pending"
              ) {
                warnings.push(
                  `里程碑 "${milestone.name}": 日期已过期，建议更新状态`
                );
              }
            });
          });
        });

        result += `验证状态: ${errors.length === 0 ? "✅ 通过" : "❌ 失败"}\n`;
        result += `错误数量: ${errors.length}\n`;
        result += `警告数量: ${warnings.length}\n\n`;

        if (errors.length > 0) {
          result += "错误列表:\n";
          errors.forEach((error) => (result += `  ❌ ${error}\n`));
          result += "\n";
        }

        if (warnings.length > 0) {
          result += "警告列表:\n";
          warnings.forEach((warning) => (result += `  ⚠️ ${warning}\n`));
        }

        output.textContent = result;
      }

      function demonstrateDateUtils() {
        const output = document.getElementById("dateUtilsOutput");
        output.style.display = "block";

        let result = "=== DateUtils 工具演示 ===\n\n";

        const today = DateUtils.today();
        const futureDate = DateUtils.addDays(today, 30);

        result += `今天: ${DateUtils.formatDate(today)}\n`;
        result += `30天后: ${DateUtils.formatDate(futureDate)}\n`;
        result += `天数差: ${DateUtils.getDaysDiff(today, futureDate)}天\n\n`;

        // 日期解析测试
        const testDates = ["2024-03-15", "2024/03/15", "03/15/2024"];
        result += "日期解析测试:\n";
        testDates.forEach((dateStr) => {
          const parsed = DateUtils.parseDate(dateStr);
          result += `  "${dateStr}" -> ${
            parsed ? DateUtils.formatDate(parsed) : "Invalid"
          }\n`;
        });

        result += "\n日期计算示例:\n";
        const startDate = DateUtils.parseDate("2024-01-01");
        const endDate = DateUtils.parseDate("2024-12-31");
        result += `  项目周期: ${DateUtils.getDaysDiff(
          startDate,
          endDate
        )}天\n`;
        result += `  项目开始: ${DateUtils.formatDate(startDate)}\n`;
        result += `  项目结束: ${DateUtils.formatDate(endDate)}\n`;

        output.textContent = result;
      }

      function generateTimeScale() {
        const output = document.getElementById("dateUtilsOutput");
        output.style.display = "block";

        let result = "=== 时间轴生成演示 ===\n\n";

        const startDate = DateUtils.parseDate("2024-01-01");
        const endDate = DateUtils.parseDate("2024-01-31");

        ["day", "week"].forEach((viewMode) => {
          result += `${viewMode.toUpperCase()} 视图模式:\n`;
          const scales = DateUtils.generateTimeScale(
            startDate,
            endDate,
            viewMode
          );
          result += `  刻度数量: ${scales.length}\n`;
          result += `  示例刻度: ${scales
            .slice(0, 5)
            .map((s) => s.label)
            .join(", ")}\n\n`;
        });

        output.textContent = result;
      }

      function demonstrateSvgUtils() {
        const output = document.getElementById("svgUtilsOutput");
        output.style.display = "block";

        let result = "=== SVG 工具演示 ===\n\n";

        // 创建简单的 SVG 演示
        const demoContainer = document.getElementById("svgDemo");
        demoContainer.innerHTML = "";

        const svg = document.createElementNS(
          "http://www.w3.org/2000/svg",
          "svg"
        );
        svg.setAttribute("width", "100%");
        svg.setAttribute("height", "200");
        svg.setAttribute("viewBox", "0 0 800 200");
        svg.style.background = "#f9f9f9";
        svg.style.border = "1px solid #ddd";
        svg.style.borderRadius = "8px";

        // 创建任务条示例
        const taskBar = document.createElementNS(
          "http://www.w3.org/2000/svg",
          "rect"
        );
        taskBar.setAttribute("x", "50");
        taskBar.setAttribute("y", "50");
        taskBar.setAttribute("width", "200");
        taskBar.setAttribute("height", "30");
        taskBar.setAttribute("fill", "#4A90E2");
        taskBar.setAttribute("rx", "4");

        // 创建进度条
        const progressBar = document.createElementNS(
          "http://www.w3.org/2000/svg",
          "rect"
        );
        progressBar.setAttribute("x", "50");
        progressBar.setAttribute("y", "50");
        progressBar.setAttribute("width", "120"); // 60% 进度
        progressBar.setAttribute("height", "30");
        progressBar.setAttribute("fill", "#7ED321");
        progressBar.setAttribute("rx", "4");
        progressBar.setAttribute("opacity", "0.8");

        // 创建里程碑
        const milestone = document.createElementNS(
          "http://www.w3.org/2000/svg",
          "polygon"
        );
        milestone.setAttribute("points", "300,-8 308,0 300,8 292,0");
        milestone.setAttribute("transform", "translate(0, 65)");
        milestone.setAttribute("fill", "#FF9500");
        milestone.setAttribute("stroke", "#333");
        milestone.setAttribute("stroke-width", "1");

        // 创建文本
        const text = document.createElementNS(
          "http://www.w3.org/2000/svg",
          "text"
        );
        text.setAttribute("x", "58");
        text.setAttribute("y", "70");
        text.setAttribute("fill", "white");
        text.setAttribute("font-size", "12");
        text.setAttribute("font-weight", "500");
        text.textContent = "示例任务 (60%)";

        const milestoneText = document.createElementNS(
          "http://www.w3.org/2000/svg",
          "text"
        );
        milestoneText.setAttribute("x", "280");
        milestoneText.setAttribute("y", "95");
        milestoneText.setAttribute("fill", "#333");
        milestoneText.setAttribute("font-size", "11");
        milestoneText.setAttribute("text-anchor", "middle");
        milestoneText.textContent = "里程碑";

        svg.appendChild(taskBar);
        svg.appendChild(progressBar);
        svg.appendChild(text);
        svg.appendChild(milestone);
        svg.appendChild(milestoneText);

        demoContainer.appendChild(svg);

        result += "SVG 元素创建成功!\n";
        result += "已创建: 任务条、进度条、里程碑、文本标签\n";
        result += "特性: 圆角矩形、半透明效果、多边形图标\n\n";
        result += "SVG 工具类功能:\n";
        result += "  ✅ 创建基础图形元素\n";
        result += "  ✅ 任务条和进度条渲染\n";
        result += "  ✅ 里程碑图标创建\n";
        result += "  ✅ 文本标签和样式\n";
        result += "  ✅ 坐标变换和定位\n";

        output.textContent = result;
      }

      function createMilestoneDemo() {
        const output = document.getElementById("svgUtilsOutput");
        output.style.display = "block";

        const demoContainer = document.getElementById("svgDemo");
        demoContainer.innerHTML = "";

        const svg = document.createElementNS(
          "http://www.w3.org/2000/svg",
          "svg"
        );
        svg.setAttribute("width", "100%");
        svg.setAttribute("height", "150");
        svg.setAttribute("viewBox", "0 0 600 150");
        svg.style.background = "#f9f9f9";
        svg.style.border = "1px solid #ddd";
        svg.style.borderRadius = "8px";

        // 不同类型的里程碑
        const milestoneTypes = [
          { type: "diamond", color: "#FF9500", label: "评审", x: 80 },
          { type: "circle", color: "#007AFF", label: "交付", x: 180 },
          { type: "triangle", color: "#34C759", label: "审批", x: 280 },
          { type: "star", color: "#FF3B30", label: "警告", x: 380 },
        ];

        milestoneTypes.forEach(({ type, color, label, x }) => {
          let shape;

          if (type === "diamond") {
            shape = document.createElementNS(
              "http://www.w3.org/2000/svg",
              "polygon"
            );
            shape.setAttribute("points", "0,-8 8,0 0,8 -8,0");
          } else if (type === "circle") {
            shape = document.createElementNS(
              "http://www.w3.org/2000/svg",
              "circle"
            );
            shape.setAttribute("r", "6");
          } else if (type === "triangle") {
            shape = document.createElementNS(
              "http://www.w3.org/2000/svg",
              "polygon"
            );
            shape.setAttribute("points", "0,-8 8,8 -8,8");
          }

          if (shape) {
            shape.setAttribute("transform", `translate(${x}, 60)`);
            shape.setAttribute("fill", color);
            shape.setAttribute("stroke", "#333");
            shape.setAttribute("stroke-width", "1");
            svg.appendChild(shape);
          }

          // 连接线
          const line = document.createElementNS(
            "http://www.w3.org/2000/svg",
            "line"
          );
          line.setAttribute("x1", x);
          line.setAttribute("y1", "68");
          line.setAttribute("x2", x);
          line.setAttribute("y2", "90");
          line.setAttribute("stroke", "#666");
          line.setAttribute("stroke-width", "1");
          line.setAttribute("stroke-dasharray", "2,2");
          svg.appendChild(line);

          // 标签
          const text = document.createElementNS(
            "http://www.w3.org/2000/svg",
            "text"
          );
          text.setAttribute("x", x);
          text.setAttribute("y", "105");
          text.setAttribute("text-anchor", "middle");
          text.setAttribute("font-size", "12");
          text.setAttribute("fill", "#333");
          text.textContent = label;
          svg.appendChild(text);
        });

        demoContainer.appendChild(svg);

        let result = "=== 里程碑类型演示 ===\n\n";
        result += "已创建多种里程碑类型:\n";
        milestoneTypes.forEach(({ type, label }) => {
          result += `  ${
            type === "diamond"
              ? "◆"
              : type === "circle"
              ? "●"
              : type === "triangle"
              ? "▲"
              : "★"
          } ${label} (${type})\n`;
        });
        result += "\n特性:\n";
        result += "  ✅ 多种图标形状\n";
        result += "  ✅ 自定义颜色\n";
        result += "  ✅ 连接线效果\n";
        result += "  ✅ 文本标签\n";

        output.textContent = result;
      }

      function demonstrateEventSystem() {
        const output = document.getElementById("eventOutput");
        output.style.display = "block";

        let result = "=== 事件系统演示 ===\n\n";

        // 创建事件监听器
        globalEmitter.on("task.click", (taskId) => {
          result += `🖱️ 任务点击事件: ${taskId}\n`;
        });

        globalEmitter.on("milestone.update", (milestoneId, newStatus) => {
          result += `📝 里程碑更新事件: ${milestoneId} -> ${newStatus}\n`;
        });

        globalEmitter.on("view.change", (oldView, newView) => {
          result += `👁️ 视图切换事件: ${oldView} -> ${newView}\n`;
        });

        // 触发一些事件
        result += "触发测试事件:\n";
        globalEmitter.emit("task.click", "task-001");
        globalEmitter.emit("milestone.update", "milestone-tech-1", "completed");
        globalEmitter.emit("view.change", "day", "week");

        result += "\n事件系统特性:\n";
        result += "  ✅ 发布订阅模式\n";
        result += "  ✅ 多监听器支持\n";
        result += "  ✅ 事件参数传递\n";
        result += "  ✅ 统计信息收集\n";

        // 更新输出（因为事件是同步的）
        setTimeout(() => {
          output.textContent = result;
        }, 100);
      }

      function showEventStats() {
        const output = document.getElementById("eventOutput");
        output.style.display = "block";

        const stats = globalEmitter.getStats();

        let result = "=== 事件系统统计 ===\n\n";
        result += `已触发事件数: ${stats.emitted}\n`;
        result += `活跃事件类型: ${stats.activeEvents}\n`;
        result += `总监听器数: ${stats.totalListeners}\n\n`;

        result += "注册的事件类型:\n";
        globalEmitter.events.forEach((listeners, eventName) => {
          result += `  📡 ${eventName}: ${listeners.length} 个监听器\n`;
        });

        output.textContent = result;
      }

      function showCompleteExample() {
        const output = document.getElementById("completeExampleOutput");
        output.style.display = "block";

        let result = "=== 完整项目数据示例 ===\n\n";

        const projectData = sampleData[0];
        const task = new TaskItem(projectData);

        result += `📊 项目概览\n`;
        result += `名称: ${task.name}\n`;
        result += `周期: ${DateUtils.formatDate(
          task.startDate
        )} ~ ${DateUtils.formatDate(task.endDate)}\n`;
        result += `状态: ${task.status}\n`;
        result += `进度: ${(task.getOverallProgress() * 100).toFixed(1)}%\n\n`;

        result += `📋 时间线详情 (${task.timelines.length} 条)\n`;
        task.timelines.forEach((timeline, index) => {
          result += `\n${index + 1}. ${timeline.name} (${timeline.type})\n`;
          result += `   颜色: ${timeline.color}\n`;
          result += `   里程碑数: ${(timeline.milestones || []).length}\n`;

          (timeline.milestones || []).forEach((milestone) => {
            const statusIcon =
              milestone.status === "completed"
                ? "✅"
                : milestone.status === "in-progress"
                ? "🟡"
                : "⏳";
            result += `   ${statusIcon} ${milestone.name}\n`;
            result += `      日期: ${milestone.date}\n`;
            result += `      类型: ${milestone.type}\n`;
          });
        });

        output.textContent = result;
      }

      function analyzeProjectData() {
        const output = document.getElementById("completeExampleOutput");
        output.style.display = "block";

        const task = new TaskItem(sampleData[0]);
        const allMilestones = task.getAllMilestones();

        let result = "=== 项目数据分析 ===\n\n";

        // 里程碑统计
        const statusCount = allMilestones.reduce((acc, m) => {
          acc[m.status] = (acc[m.status] || 0) + 1;
          return acc;
        }, {});

        result += `📊 里程碑统计\n`;
        result += `总数: ${allMilestones.length}\n`;
        Object.entries(statusCount).forEach(([status, count]) => {
          const icon =
            status === "completed"
              ? "✅"
              : status === "in-progress"
              ? "🟡"
              : "⏳";
          result += `${icon} ${status}: ${count} 个\n`;
        });

        // 类型统计
        const typeCount = allMilestones.reduce((acc, m) => {
          acc[m.type] = (acc[m.type] || 0) + 1;
          return acc;
        }, {});

        result += `\n📈 类型分布\n`;
        Object.entries(typeCount).forEach(([type, count]) => {
          result += `${type}: ${count} 个\n`;
        });

        // 时间线进度
        result += `\n⏱️ 时间线进度\n`;
        task.timelines.forEach((timeline) => {
          const completed = (timeline.milestones || []).filter(
            (m) => m.status === "completed"
          ).length;
          const total = (timeline.milestones || []).length;
          const progress =
            total > 0 ? ((completed / total) * 100).toFixed(1) : "0.0";
          result += `${timeline.name}: ${progress}% (${completed}/${total})\n`;
        });

        result += `\n🎯 整体评估\n`;
        result += `项目进度: ${(task.getOverallProgress() * 100).toFixed(
          1
        )}%\n`;
        result += `风险评估: ${
          statusCount.pending > statusCount.completed ? "需关注" : "进展良好"
        }\n`;

        output.textContent = result;
      }

      // 初始化演示
      document.addEventListener("DOMContentLoaded", () => {
        // 创建项目演示卡片
        const projectDemo = document.getElementById("projectDemo");
        projectDemo.innerHTML = `
                <div class="timeline-demo">
                    <h4>🏢 企业管理系统开发项目</h4>
                    <p><strong>进度:</strong> <span class="status-badge status-in-progress">进行中</span> 15%</p>
                    <p><strong>周期:</strong> 2024-01-01 ~ 2024-06-30</p>
                    <p><strong>时间线:</strong> 技术里程碑、业务里程碑</p>
                    
                    <div style="margin-top: 1rem;">
                        <h5>技术里程碑:</h5>
                        <div class="milestone">
                            <div class="milestone-icon completed"></div>
                            <span>技术架构评审 (2024-01-15)</span>
                        </div>
                        <div class="milestone">
                            <div class="milestone-icon in-progress"></div>
                            <span>核心模块开发完成 (2024-03-01)</span>
                        </div>
                    </div>
                    
                    <div style="margin-top: 1rem;">
                        <h5>业务里程碑:</h5>
                        <div class="milestone">
                            <div class="milestone-icon completed"></div>
                            <span>需求调研完成 (2024-01-10)</span>
                        </div>
                        <div class="milestone">
                            <div class="milestone-icon pending"></div>
                            <span>用户验收测试 (2024-05-01)</span>
                        </div>
                    </div>
                </div>
            `;

        console.log("Step 2 演示页面初始化完成");
        console.log("已加载工具类: DateUtils, EventEmitter, TaskItem");
        console.log("示例数据包含", sampleData.length, "个任务");
      });
    </script>
  </body>
</html>

import Milestone from './Milestone.js';

/**
 * 时间线数据结构
 * 一个任务可以包含多条时间线，每条时间线包含多个里程碑
 * 增加Y轴布局相关属性
 */
class Timeline {
  constructor(data = {}) {
    this.id = data.id || this.generateId();
    this.name = data.name || '';
    this.description = data.description || '';
    this.color = data.color || this.getDefaultColor();
    this.type = data.type || 'default'; // default, critical, secondary, technical, business
    this.visible = data.visible !== false; // 默认可见
    this.order = data.order || 0; // 显示顺序
    
    // Y轴布局属性 - 新增
    this.yOffset = data.yOffset || 0; // Y轴偏移量（像素）
    this.height = data.height || 20; // 时间线高度（像素）
    this.layer = data.layer || 0; // 图层层级，决定渲染顺序
    this.collapsed = data.collapsed || false; // 是否折叠
    
    // 样式属性 - 新增
    this.lineStyle = data.lineStyle || {
      strokeWidth: 2,
      strokeDashArray: null, // 实线为null，虚线如 "5,5"
      opacity: 1.0
    };
    
    // 里程碑列表
    this.milestones = [];
    if (data.milestones && Array.isArray(data.milestones)) {
      this.milestones = data.milestones.map(m => 
        m instanceof Milestone ? m : new Milestone({ ...m, timelineId: this.id })
      );
    }
    
    // 扩展属性
    this.customFields = data.customFields || {};
    this.metadata = data.metadata || {};
    
    this.validate();
  }

  validate() {
    if (!this.name) {
      throw new Error('Timeline name is required');
    }
  }

  generateId() {
    return `timeline-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  // 根据类型获取默认颜色
  getDefaultColor() {
    const colors = {
      default: '#4A90E2',
      critical: '#FF3B30',
      secondary: '#8E8E93',
      technical: '#007AFF',
      business: '#34C759'
    };
    return colors[this.type] || colors.default;
  }

  // 添加里程碑
  addMilestone(milestoneData) {
    const milestone = milestoneData instanceof Milestone 
      ? milestoneData 
      : new Milestone({ ...milestoneData, timelineId: this.id });
    
    this.milestones.push(milestone);
    this.sortMilestones();
    return milestone;
  }

  // 删除里程碑
  removeMilestone(milestoneId) {
    const index = this.milestones.findIndex(m => m.id === milestoneId);
    if (index !== -1) {
      return this.milestones.splice(index, 1)[0];
    }
    return null;
  }

  // 获取里程碑
  getMilestone(milestoneId) {
    return this.milestones.find(m => m.id === milestoneId);
  }

  // 按日期排序里程碑
  sortMilestones() {
    this.milestones.sort((a, b) => a.date - b.date);
  }

  // 获取时间线的日期范围
  getDateRange() {
    if (this.milestones.length === 0) {
      return { startDate: null, endDate: null };
    }

    const dates = this.milestones.map(m => m.date).filter(Boolean);
    return {
      startDate: new Date(Math.min(...dates)),
      endDate: new Date(Math.max(...dates))
    };
  }

  // 获取已完成的里程碑数量
  getCompletedCount() {
    return this.milestones.filter(m => m.status === 'completed').length;
  }

  // 获取进度百分比
  getProgress() {
    if (this.milestones.length === 0) return 0;
    return this.getCompletedCount() / this.milestones.length;
  }

  // 检查是否有逾期里程碑
  hasOverdueMilestones() {
    return this.milestones.some(m => m.isOverdue());
  }

  // 计算时间线在任务中的Y坐标位置
  calculateYPosition(baseY, timelineIndex, rowHeight = 40) {
    if (this.collapsed) {
      return { y: baseY, visible: false };
    }
    
    // 基础Y坐标 + 时间线偏移 + 索引偏移
    const y = baseY + this.yOffset + (timelineIndex * this.height);
    return { y, visible: this.visible };
  }

  // 获取时间线的边界框
  getBounds(baseY, timelineIndex, rowHeight = 40) {
    const position = this.calculateYPosition(baseY, timelineIndex, rowHeight);
    if (!position.visible) {
      return null;
    }
    
    const dateRange = this.getDateRange();
    return {
      y: position.y,
      height: this.height,
      startDate: dateRange.startDate,
      endDate: dateRange.endDate
    };
  }

  // 切换折叠状态
  toggleCollapsed() {
    this.collapsed = !this.collapsed;
    return this.collapsed;
  }

  // 克隆时间线
  clone() {
    return new Timeline({
      ...this,
      id: this.generateId(),
      milestones: this.milestones.map(m => m.clone()),
      customFields: { ...this.customFields },
      metadata: { ...this.metadata },
      lineStyle: { ...this.lineStyle }
    });
  }

  // 转换为 JSON
  toJSON() {
    return {
      id: this.id,
      name: this.name,
      description: this.description,
      color: this.color,
      type: this.type,
      visible: this.visible,
      order: this.order,
      yOffset: this.yOffset,
      height: this.height,
      layer: this.layer,
      collapsed: this.collapsed,
      lineStyle: this.lineStyle,
      milestones: this.milestones.map(m => m.toJSON()),
      customFields: this.customFields,
      metadata: this.metadata
    };
  }
}

export default Timeline;
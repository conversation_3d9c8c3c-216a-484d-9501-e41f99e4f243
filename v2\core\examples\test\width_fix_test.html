<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>甘特图宽度修复测试</title>
    <link rel="stylesheet" href="../../src/styles/gantt.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }

        .header h1 {
            margin: 0;
            font-size: 24px;
        }

        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
        }

        .controls {
            padding: 20px;
            background: #f8fafc;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }

        .control-btn {
            padding: 8px 16px;
            border: 1px solid #6366f1;
            border-radius: 6px;
            background: white;
            color: #6366f1;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s ease;
        }

        .control-btn:hover {
            background: #6366f1;
            color: white;
        }

        .status {
            margin-left: auto;
            font-size: 14px;
            color: #6b7280;
        }

        .gantt-container {
            height: 500px;
            position: relative;
            border: 2px solid #6366f1;
        }

        .info {
            padding: 20px;
            background: #f1f5f9;
            border-top: 1px solid #e2e8f0;
        }

        .info h3 {
            margin: 0 0 15px 0;
            color: #374151;
        }

        .info-list {
            list-style: none;
            padding: 0;
            margin: 0;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 10px;
        }

        .info-list li {
            padding: 8px 0;
            color: #6b7280;
            font-size: 14px;
            border-bottom: 1px solid #e2e8f0;
        }

        .info-list li::before {
            content: "✓";
            color: #10b981;
            font-weight: bold;
            margin-right: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 甘特图宽度修复测试</h1>
            <p>测试横向渲染完整性和容器宽度同步</p>
        </div>

        <div class="controls">
            <button class="control-btn" onclick="testHorizontalScroll()">测试横向滚动</button>
            <button class="control-btn" onclick="testZoom()">测试缩放</button>
            <button class="control-btn" onclick="testViewMode()">切换视图模式</button>
            <button class="control-btn" onclick="checkWidths()">检查容器宽度</button>
            <button class="control-btn" onclick="addMoreTasks()">添加更多任务</button>
            <div class="status" id="status">准备就绪</div>
        </div>

        <div class="gantt-container" id="ganttContainer"></div>

        <div class="info">
            <h3>修复内容</h3>
            <ul class="info-list">
                <li>修复SVG容器宽度设置，确保与时间轴总宽度一致</li>
                <li>同步所有相关容器的宽度（SVG、时间轴头部、滚动容器）</li>
                <li>在时间轴变化时自动更新容器宽度</li>
                <li>确保横向滚动能够显示所有任务数据</li>
                <li>修复缩放和视图模式切换时的宽度同步问题</li>
                <li>添加容器宽度监控和调试信息</li>
            </ul>
        </div>
    </div>

    <script type="module">
        import GanttChart from '../../src/GanttChart.js';
        import DateUtils from '../../src/utils/DateUtils.js';

        // 生成测试数据
        function generateTestData() {
            const tasks = [];
            const startDate = new Date('2024-01-01');
            
            for (let i = 0; i < 15; i++) {
                const taskStart = DateUtils.addDays(startDate, i * 7);
                const duration = 10 + Math.random() * 20;
                const taskEnd = DateUtils.addDays(taskStart, duration);
                
                tasks.push({
                    id: `width-test-${i}`,
                    name: `宽度测试任务 ${i + 1}`,
                    startDate: taskStart,
                    endDate: taskEnd,
                    progress: Math.random()
                });
            }
            
            return tasks;
        }

        // 初始化甘特图
        let ganttInstance = null;

        function initializeGantt() {
            const container = document.getElementById('ganttContainer');
            container.innerHTML = '';
            
            const testData = generateTestData();
            
            ganttInstance = new GanttChart('ganttContainer', {
                data: testData,
                viewMode: 'day',
                pixelsPerDay: 40,
                theme: {
                    mode: 'light'
                },
                timeline: {
                    showWeekday: true,
                    adaptiveWidth: true,
                    minScaleWidth: 35,
                    maxScaleWidth: 120
                },
                taskList: {
                    columns: [
                        { key: 'name', title: '任务名称', width: 200 },
                        { key: 'progress', title: '进度', width: 80 }
                    ]
                }
            });

            ganttInstance.on('ready', () => {
                updateStatus('甘特图初始化完成');
                checkWidths();
            });
        }

        // 更新状态
        function updateStatus(message) {
            document.getElementById('status').textContent = message;
        }

        // 测试函数
        window.testHorizontalScroll = function() {
            if (!ganttInstance) return;
            
            const chartBody = ganttInstance.elements.chartBody;
            if (chartBody) {
                // 滚动到中间位置
                const maxScroll = chartBody.scrollWidth - chartBody.clientWidth;
                chartBody.scrollLeft = maxScroll / 2;
                updateStatus(`横向滚动测试 - 滚动到: ${chartBody.scrollLeft}px`);
            }
        };

        window.testZoom = function() {
            if (!ganttInstance) return;
            
            ganttInstance.zoomIn();
            updateStatus('缩放测试 - 放大');
            
            setTimeout(() => {
                checkWidths();
            }, 500);
        };

        window.testViewMode = function() {
            if (!ganttInstance) return;
            
            const modes = ['day', 'week', 'month'];
            const currentMode = ganttInstance.state.viewMode;
            const currentIndex = modes.indexOf(currentMode);
            const nextMode = modes[(currentIndex + 1) % modes.length];
            
            ganttInstance.setViewMode(nextMode);
            updateStatus(`视图模式切换到: ${nextMode}`);
            
            setTimeout(() => {
                checkWidths();
            }, 500);
        };

        window.checkWidths = function() {
            if (!ganttInstance) return;
            
            const svg = ganttInstance.elements.svg;
            const timelineScales = ganttInstance.elements.timelineScales;
            const chartBody = ganttInstance.elements.chartBody;
            const timeScale = ganttInstance.timeScale;
            
            const widths = {
                timeScaleTotal: timeScale ? timeScale.getTotalWidth() : 0,
                svgWidth: svg ? svg.style.width : 'N/A',
                timelineWidth: timelineScales ? timelineScales.style.width : 'N/A',
                scrollWidth: chartBody ? chartBody.scrollWidth : 0,
                clientWidth: chartBody ? chartBody.clientWidth : 0
            };
            
            console.log('容器宽度检查:', widths);
            updateStatus(`时间轴总宽度: ${widths.timeScaleTotal}px, 滚动宽度: ${widths.scrollWidth}px`);
        };

        window.addMoreTasks = function() {
            if (!ganttInstance) return;
            
            const newTasks = [];
            const startDate = new Date('2024-06-01');
            
            for (let i = 0; i < 10; i++) {
                const taskStart = DateUtils.addDays(startDate, i * 5);
                const duration = 8 + Math.random() * 12;
                const taskEnd = DateUtils.addDays(taskStart, duration);
                
                newTasks.push({
                    id: `additional-${i}`,
                    name: `新增任务 ${i + 1}`,
                    startDate: taskStart,
                    endDate: taskEnd,
                    progress: Math.random()
                });
            }
            
            const currentData = ganttInstance.getData();
            ganttInstance.setData([...currentData, ...newTasks]);
            updateStatus(`添加了 ${newTasks.length} 个新任务`);
            
            setTimeout(() => {
                checkWidths();
            }, 500);
        };

        // 初始化
        document.addEventListener('DOMContentLoaded', () => {
            console.log('页面加载完成，初始化宽度修复测试...');
            initializeGantt();
        });
    </script>
</body>
</html>

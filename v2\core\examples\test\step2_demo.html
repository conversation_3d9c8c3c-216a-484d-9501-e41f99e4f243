<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>甘特图演示 - 集成工具类版本</title>

    <!-- CSS 样式 -->
    <style>
      /* 重置样式 */
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Arial, sans-serif;
        background-color: #f5f5f5;
        color: #333;
        line-height: 1.6;
      }

      .demo-container {
        display: flex;
        flex-direction: column;
        height: 100vh;
        max-height: 100vh;
      }

      .demo-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 20px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      }

      .demo-title {
        font-size: 24px;
        font-weight: 600;
        margin-bottom: 8px;
      }

      .demo-subtitle {
        font-size: 14px;
        opacity: 0.9;
      }

      .demo-controls {
        background: white;
        padding: 15px 20px;
        border-bottom: 1px solid #e0e0e0;
        display: flex;
        align-items: center;
        gap: 15px;
        flex-wrap: wrap;
      }

      .control-group {
        display: flex;
        align-items: center;
        gap: 8px;
      }

      .control-label {
        font-size: 12px;
        font-weight: 500;
        color: #666;
      }

      .btn {
        padding: 8px 16px;
        background: #4a90e2;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 12px;
        font-weight: 500;
        transition: all 0.2s ease;
      }

      .btn:hover {
        background: #357abd;
        transform: translateY(-1px);
      }

      .btn.secondary {
        background: #6c757d;
      }

      .btn.secondary:hover {
        background: #545b62;
      }

      .btn.success {
        background: #28a745;
      }

      .btn.success:hover {
        background: #1e7e34;
      }

      .btn.danger {
        background: #dc3545;
      }

      .btn.danger:hover {
        background: #bd2130;
      }

      .select-control {
        padding: 6px 12px;
        border: 1px solid #ddd;
        border-radius: 4px;
        background: white;
        font-size: 12px;
      }

      .gantt-content {
        flex: 1;
        overflow: hidden;
        background: white;
        margin: 0 20px 20px 20px;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      }

      /* 甘特图基础样式 */
      .gantt-container {
        height: 100%;
        width: 100%;
        display: flex;
        flex-direction: column;
        font-family: inherit;
      }

      .gantt-header {
        border-bottom: 1px solid #e0e0e0;
        background: #fafafa;
      }

      .gantt-toolbar {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 12px 16px;
        gap: 16px;
      }

      .gantt-view-controls {
        display: flex;
        gap: 4px;
      }

      .gantt-btn {
        padding: 6px 12px;
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        color: #495057;
        cursor: pointer;
        font-size: 12px;
        border-radius: 4px;
        transition: all 0.2s ease;
      }

      .gantt-btn:hover {
        background: #e9ecef;
        border-color: #adb5bd;
      }

      .gantt-btn.active {
        background: #4a90e2;
        border-color: #4a90e2;
        color: white;
      }

      .gantt-actions {
        display: flex;
        gap: 6px;
      }

      .gantt-main {
        display: flex;
        flex: 1;
        min-height: 0;
      }

      .gantt-left-panel {
        background: #fafafa;
        border-right: 1px solid #e0e0e0;
        display: flex;
        flex-direction: column;
        min-width: 200px;
        max-width: 600px;
      }

      .gantt-table-header {
        display: flex;
        background: #f1f3f4;
        border-bottom: 1px solid #e0e0e0;
        font-weight: 500;
        font-size: 12px;
      }

      .gantt-table-header-cell {
        padding: 10px 8px;
        border-right: 1px solid #e0e0e0;
        color: #333;
        display: flex;
        align-items: center;
      }

      .gantt-table-body {
        flex: 1;
        overflow-y: auto;
        overflow-x: hidden;
      }

      .gantt-table-row {
        display: flex;
        border-bottom: 1px solid #f0f0f0;
        cursor: pointer;
        transition: background-color 0.2s ease;
      }

      .gantt-table-row:hover {
        background-color: #f8f9fa;
      }

      .gantt-table-row.selected {
        background-color: #e3f2fd !important;
        border-color: #2196f3;
      }

      .gantt-table-row.status-completed {
        opacity: 0.7;
      }

      .gantt-table-row.status-overdue {
        background-color: #ffebee;
      }

      .gantt-table-cell {
        padding: 8px;
        border-right: 1px solid #f0f0f0;
        font-size: 12px;
        display: flex;
        align-items: center;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .expand-icon {
        cursor: pointer;
        margin-right: 4px;
        user-select: none;
        color: #666;
        width: 12px;
        display: inline-block;
      }

      .expand-icon:hover {
        color: #333;
      }

      .gantt-splitter {
        width: 4px;
        background: #e0e0e0;
        cursor: col-resize;
        transition: background-color 0.2s ease;
      }

      .gantt-splitter:hover {
        background: #4a90e2;
      }

      .gantt-right-panel {
        flex: 1;
        display: flex;
        flex-direction: column;
        min-width: 0;
      }

      .gantt-timeline-header {
        height: 60px;
        border-bottom: 1px solid #e0e0e0;
        background: #fafafa;
        position: relative;
        overflow: hidden;
      }

      .gantt-timeline-scales {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        overflow: hidden;
      }

      .timeline-scale {
        position: absolute;
        top: 0;
        bottom: 0;
        border-right: 1px solid #e0e0e0;
        display: flex;
        align-items: center;
        padding-left: 4px;
      }

      .scale-label {
        font-size: 11px;
        color: #666;
        font-weight: 500;
      }

      .gantt-chart-body {
        flex: 1;
        overflow: auto;
        position: relative;
      }

      .gantt-svg {
        display: block;
        width: 100%;
        background: white;
      }

      /* SVG 样式 */
      .task-group {
        cursor: pointer;
      }

      .task-group:hover .task-main-bar {
        stroke: #333;
        stroke-width: 1px;
      }

      .task-group.selected .task-main-bar {
        stroke: #2196f3;
        stroke-width: 2px;
      }

      .task-main-bar {
        transition: stroke 0.2s ease;
      }

      .task-progress-bar {
        pointer-events: none;
      }

      .task-text {
        pointer-events: none;
        user-select: none;
      }

      .milestone {
        cursor: pointer;
        transition: transform 0.2s ease;
      }

      .milestone:hover {
        transform: scale(1.1);
      }

      .milestone.selected {
        filter: drop-shadow(0 0 4px #2196f3);
      }

      .grid-line-vertical,
      .grid-line-horizontal {
        pointer-events: none;
      }

      .gantt-footer {
        background: #fafafa;
        border-top: 1px solid #e0e0e0;
        padding: 8px 16px;
        font-size: 12px;
        color: #666;
      }

      .gantt-status {
        display: flex;
        gap: 20px;
        align-items: center;
      }

      .gantt-status span {
        white-space: nowrap;
      }

      .message {
        font-weight: 500;
      }

      .message-info {
        color: #2196f3;
      }

      .message-error {
        color: #f44336;
      }

      .message-success {
        color: #4caf50;
      }

      /* 响应式设计 */
      @media (max-width: 768px) {
        .demo-controls {
          flex-direction: column;
          align-items: flex-start;
          gap: 10px;
        }

        .gantt-actions {
          flex-wrap: wrap;
        }

        .gantt-left-panel {
          min-width: 150px;
        }

        .gantt-table-cell {
          padding: 6px 4px;
          font-size: 11px;
        }
      }

      /* 加载动画 */
      .loading {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 200px;
        color: #666;
      }

      .loading::after {
        content: "";
        width: 20px;
        height: 20px;
        border: 2px solid #f3f3f3;
        border-top: 2px solid #4a90e2;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-left: 10px;
      }

      @keyframes spin {
        0% {
          transform: rotate(0deg);
        }
        100% {
          transform: rotate(360deg);
        }
      }

      /* 滚动条美化 */
      .gantt-table-body::-webkit-scrollbar,
      .gantt-chart-body::-webkit-scrollbar {
        width: 8px;
        height: 8px;
      }

      .gantt-table-body::-webkit-scrollbar-track,
      .gantt-chart-body::-webkit-scrollbar-track {
        background: #f1f1f1;
      }

      .gantt-table-body::-webkit-scrollbar-thumb,
      .gantt-chart-body::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 4px;
      }

      .gantt-table-body::-webkit-scrollbar-thumb:hover,
      .gantt-chart-body::-webkit-scrollbar-thumb:hover {
        background: #a8a8a8;
      }

      /* 演示页面特有样式 */
      .demo-info {
        background: #e3f2fd;
        border-left: 4px solid #2196f3;
        padding: 12px;
        margin-bottom: 15px;
        border-radius: 0 4px 4px 0;
        font-size: 13px;
        color: #1565c0;
      }

      .api-demo {
        margin-top: 20px;
        padding: 20px;
        background: #f8f9fa;
        border-radius: 8px;
        border: 1px solid #e9ecef;
      }

      .api-demo h3 {
        margin-bottom: 12px;
        color: #495057;
        font-size: 16px;
      }

      .api-buttons {
        display: flex;
        gap: 8px;
        flex-wrap: wrap;
      }

      .console-output {
        margin-top: 15px;
        padding: 12px;
        background: #2d3748;
        color: #a0aec0;
        border-radius: 4px;
        font-family: "Courier New", monospace;
        font-size: 12px;
        max-height: 200px;
        overflow-y: auto;
        white-space: pre-wrap;
      }

      .stats-panel {
        background: white;
        padding: 15px;
        margin: 15px 0;
        border-radius: 6px;
        border: 1px solid #e9ecef;
      }

      .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: 12px;
      }

      .stat-item {
        text-align: center;
        padding: 8px;
        background: #f8f9fa;
        border-radius: 4px;
      }

      .stat-value {
        font-size: 18px;
        font-weight: 600;
        color: #495057;
      }

      .stat-label {
        font-size: 11px;
        color: #6c757d;
        margin-top: 2px;
      }
    </style>
  </head>

  <body>
    <div class="demo-container">
      <!-- 页面头部 -->
      <div class="demo-header">
        <div class="demo-title">甘特图组件演示</div>
        <div class="demo-subtitle">集成 DateUtils、EventEmitter、SvgUtils 和增强数据结构的完整版本</div>
      </div>

      <!-- 控制面板 -->
      <div class="demo-controls">
        <div class="control-group">
          <span class="control-label">演示数据:</span>
          <select id="data-selector" class="select-control">
            <option value="default">默认项目数据</option>
            <option value="complex">复杂多时间线项目</option>
            <option value="simple">简单任务列表</option>
            <option value="large">大量数据测试</option>
          </select>
        </div>

        <div class="control-group">
          <button id="reload-data" class="btn">重载数据</button>
          <button id="add-task" class="btn success">添加任务</button>
          <button id="export-data" class="btn secondary">导出数据</button>
        </div>

        <div class="control-group">
          <span class="control-label">快速操作:</span>
          <button id="scroll-to-today" class="btn secondary">跳转到今天</button>
          <button id="select-first" class="btn secondary">选择第一个任务</button>
          <button id="show-stats" class="btn secondary">显示统计</button>
        </div>
      </div>

      <!-- 甘特图容器 -->
      <div class="gantt-content">
        <div class="demo-info">
          💡 <strong>功能说明：</strong>
          点击任务条查看详情 | 点击里程碑查看信息 | 使用工具栏切换视图模式 | 拖拽分割器调整列宽 | 滚动同步显示
        </div>

        <div id="gantt-container" class="loading">正在初始化甘特图...</div>
      </div>
    </div>

    <!-- API 演示面板（可选显示） -->
    <div id="api-demo-panel" class="api-demo" style="display: none">
      <h3>🔧 API 演示</h3>
      <div class="api-buttons">
        <button onclick="demoAPI.showData()" class="btn">显示数据</button>
        <button onclick="demoAPI.showSelection()" class="btn">显示选择</button>
        <button onclick="demoAPI.addRandomTask()" class="btn success">随机添加任务</button>
        <button onclick="demoAPI.updateFirstTask()" class="btn">更新第一个任务</button>
        <button onclick="demoAPI.testZoom()" class="btn">测试缩放</button>
        <button onclick="demoAPI.testViewMode()" class="btn">切换视图模式</button>
        <button onclick="demoAPI.clearConsole()" class="btn danger">清空输出</button>
      </div>
      <div id="console-output" class="console-output">控制台输出将在这里显示...</div>
    </div>

    <!-- JavaScript 代码 -->
    <script type="module">
      // 模拟导入（实际项目中使用真实的导入）
      // import GanttChart from './src/GanttChart.js';
      // import { TaskItem, Timeline, Milestone, DataTransformer } from './src/utils/dataStructure.js';
      // import DateUtils from './src/utils/DateUtils.js';

      // 临时解决方案：直接包含必要的工具类代码
      class DateUtils {
        static VIEW_MODES = {
          DAY: "day",
          WEEK: "week",
          MONTH: "month",
          QUARTER: "quarter",
          YEAR: "year",
        };

        static parseDate(dateInput) {
          if (!dateInput) return null;
          if (dateInput instanceof Date) {
            return isNaN(dateInput.getTime()) ? null : dateInput;
          }
          const date = new Date(dateInput);
          return isNaN(date.getTime()) ? null : date;
        }

        static formatDate(date, format = "YYYY-MM-DD") {
          if (!date || !(date instanceof Date)) return "";
          const year = date.getFullYear();
          const month = String(date.getMonth() + 1).padStart(2, "0");
          const day = String(date.getDate()).padStart(2, "0");
          return format.replace("YYYY", year).replace("MM", month).replace("DD", day);
        }

        static addDays(date, days) {
          if (!date) return null;
          const result = new Date(date);
          result.setDate(result.getDate() + days);
          return result;
        }

        static today() {
          const now = new Date();
          now.setHours(0, 0, 0, 0);
          return now;
        }

        static getTimeUnit(viewMode) {
          const units = {
            day: { name: "日", duration: 1 },
            week: { name: "周", duration: 7 },
            month: { name: "月", duration: 30 },
            quarter: { name: "季度", duration: 90 },
            year: { name: "年", duration: 365 },
          };
          return units[viewMode] || units.day;
        }
      }

      // 示例数据生成器
      class DataGenerator {
        static generateDefaultData() {
          const today = DateUtils.today();

          return [
            {
              id: "task-1",
              name: "项目启动",
              startDate: DateUtils.formatDate(today),
              endDate: DateUtils.formatDate(DateUtils.addDays(today, 5)),
              duration: 5,
              progress: 1.0,
              assignee: "张三",
              level: 0,
              status: "completed",
              priority: "high",
              customFields: {
                department: "项目组",
                cost: "¥10,000",
              },
              timelines: [
                {
                  name: "主要里程碑",
                  type: "default",
                  milestones: [
                    {
                      name: "项目启动会",
                      date: DateUtils.formatDate(DateUtils.addDays(today, 1)),
                      type: "review",
                      status: "completed",
                    },
                  ],
                },
              ],
            },
            {
              id: "task-2",
              name: "需求分析",
              startDate: DateUtils.formatDate(DateUtils.addDays(today, 6)),
              endDate: DateUtils.formatDate(DateUtils.addDays(today, 20)),
              duration: 15,
              progress: 0.8,
              assignee: "李四",
              level: 1,
              status: "in-progress",
              priority: "high",
              customFields: {
                department: "产品组",
                cost: "¥25,000",
              },
              timelines: [
                {
                  name: "需求里程碑",
                  type: "business",
                  milestones: [
                    {
                      name: "需求调研完成",
                      date: DateUtils.formatDate(DateUtils.addDays(today, 10)),
                      type: "info",
                      status: "completed",
                    },
                    {
                      name: "需求评审",
                      date: DateUtils.formatDate(DateUtils.addDays(today, 15)),
                      type: "review",
                      status: "pending",
                    },
                  ],
                },
              ],
            },
            {
              id: "task-3",
              name: "系统设计",
              startDate: DateUtils.formatDate(DateUtils.addDays(today, 21)),
              endDate: DateUtils.formatDate(DateUtils.addDays(today, 35)),
              duration: 15,
              progress: 0.4,
              assignee: "王五",
              level: 1,
              status: "in-progress",
              priority: "medium",
              customFields: {
                department: "架构组",
                cost: "¥30,000",
              },
              timelines: [
                {
                  name: "技术设计",
                  type: "technical",
                  milestones: [
                    {
                      name: "技术方案评审",
                      date: DateUtils.formatDate(DateUtils.addDays(today, 28)),
                      type: "review",
                      status: "pending",
                    },
                  ],
                },
                {
                  name: "UI设计",
                  type: "secondary",
                  milestones: [
                    {
                      name: "UI设计完成",
                      date: DateUtils.formatDate(DateUtils.addDays(today, 30)),
                      type: "delivery",
                      status: "pending",
                    },
                  ],
                },
              ],
            },
            {
              id: "task-4",
              name: "开发实现",
              startDate: DateUtils.formatDate(DateUtils.addDays(today, 36)),
              endDate: DateUtils.formatDate(DateUtils.addDays(today, 70)),
              duration: 35,
              progress: 0.2,
              assignee: "赵六",
              level: 0,
              status: "pending",
              priority: "critical",
              customFields: {
                department: "开发组",
                cost: "¥80,000",
              },
              timelines: [
                {
                  name: "后端开发",
                  type: "technical",
                  milestones: [
                    {
                      name: "API开发完成",
                      date: DateUtils.formatDate(DateUtils.addDays(today, 50)),
                      type: "delivery",
                      status: "pending",
                    },
                  ],
                },
                {
                  name: "前端开发",
                  type: "technical",
                  milestones: [
                    {
                      name: "前端界面完成",
                      date: DateUtils.formatDate(DateUtils.addDays(today, 60)),
                      type: "delivery",
                      status: "pending",
                    },
                  ],
                },
              ],
            },
            {
              id: "task-5",
              name: "测试验收",
              startDate: DateUtils.formatDate(DateUtils.addDays(today, 71)),
              endDate: DateUtils.formatDate(DateUtils.addDays(today, 85)),
              duration: 15,
              progress: 0.0,
              assignee: "孙七",
              level: 0,
              status: "pending",
              priority: "high",
              customFields: {
                department: "测试组",
                cost: "¥15,000",
              },
              timelines: [
                {
                  name: "测试里程碑",
                  type: "default",
                  milestones: [
                    {
                      name: "功能测试完成",
                      date: DateUtils.formatDate(DateUtils.addDays(today, 78)),
                      type: "review",
                      status: "pending",
                    },
                    {
                      name: "项目验收",
                      date: DateUtils.formatDate(DateUtils.addDays(today, 83)),
                      type: "approval",
                      status: "pending",
                    },
                  ],
                },
              ],
            },
          ];
        }

        static generateComplexData() {
          // 生成包含更多时间线的复杂数据
          const data = this.generateDefaultData();

          // 为每个任务添加更多时间线
          data.forEach((task) => {
            if (task.timelines.length < 3) {
              task.timelines.push({
                name: "风险管控",
                type: "warning",
                milestones: [
                  {
                    name: "风险评估",
                    date: task.startDate,
                    type: "warning",
                    status: "pending",
                  },
                ],
              });
            }
          });

          return data;
        }

        static generateSimpleData() {
          const today = DateUtils.today();
          return [
            {
              id: "simple-1",
              name: "简单任务1",
              startDate: DateUtils.formatDate(today),
              endDate: DateUtils.formatDate(DateUtils.addDays(today, 3)),
              duration: 3,
              progress: 0.6,
              assignee: "用户A",
              status: "in-progress",
            },
            {
              id: "simple-2",
              name: "简单任务2",
              startDate: DateUtils.formatDate(DateUtils.addDays(today, 4)),
              endDate: DateUtils.formatDate(DateUtils.addDays(today, 8)),
              duration: 5,
              progress: 0.0,
              assignee: "用户B",
              status: "pending",
            },
          ];
        }

        static generateLargeData() {
          const data = [];
          const today = DateUtils.today();

          for (let i = 0; i < 50; i++) {
            const startOffset = i * 2;
            const duration = Math.floor(Math.random() * 10) + 3;

            data.push({
              id: `large-task-${i + 1}`,
              name: `批量任务 ${i + 1}`,
              startDate: DateUtils.formatDate(DateUtils.addDays(today, startOffset)),
              endDate: DateUtils.formatDate(DateUtils.addDays(today, startOffset + duration)),
              duration: duration,
              progress: Math.random(),
              assignee: `用户${String.fromCharCode(65 + (i % 26))}`,
              level: i % 3,
              status: ["pending", "in-progress", "completed"][Math.floor(Math.random() * 3)],
              priority: ["low", "normal", "high"][Math.floor(Math.random() * 3)],
            });
          }

          return data;
        }
      }

      // 简化的甘特图类（演示用）
      class SimpleGanttChart {
        constructor(containerId, options = {}) {
          this.container = document.getElementById(containerId);
          this.options = {
            viewMode: "day",
            rowHeight: 40,
            pixelsPerDay: 30,
            taskList: {
              width: 300,
              columns: [
                { key: "name", title: "任务名称", width: 160 },
                { key: "assignee", title: "负责人", width: 70 },
                { key: "duration", title: "工期", width: 50 },
              ],
            },
            ...options,
          };
          this.data = options.data || [];
          this.state = {
            viewMode: this.options.viewMode,
            selectedTasks: new Set(),
            zoomLevel: 1.0,
          };
          this.events = new Map();

          this.init();
        }

        init() {
          this.createLayout();
          this.renderTable();
          this.renderChart();
          this.bindEvents();
          this.updateStatus();
          this.emit("ready");
        }

        createLayout() {
          this.container.className = "gantt-container";
          this.container.innerHTML = `
                    <div class="gantt-header">
                        <div class="gantt-toolbar">
                            <div class="gantt-view-controls">
                                <button class="gantt-btn active" data-view="day">日</button>
                                <button class="gantt-btn" data-view="week">周</button>
                                <button class="gantt-btn" data-view="month">月</button>
                                <button class="gantt-btn" data-view="quarter">季度</button>
                            </div>
                            <div class="gantt-actions">
                                <button class="gantt-btn" id="gantt-zoom-in">放大</button>
                                <button class="gantt-btn" id="gantt-zoom-out">缩小</button>
                                <button class="gantt-btn" id="gantt-fit-view">适应窗口</button>
                                <button class="gantt-btn" id="gantt-refresh">刷新</button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="gantt-main">
                        <div class="gantt-left-panel" style="width: ${this.options.taskList.width}px">
                            <div class="gantt-table-header"></div>
                            <div class="gantt-table-body"></div>
                        </div>
                        
                        <div class="gantt-splitter"></div>
                          
                        <div class="gantt-right-panel">
                            <div class="gantt-timeline-header">
                                <div class="gantt-timeline-scales"></div>
                            </div>
                            <div class="gantt-chart-body">
                                <svg class="gantt-svg" width="100%" height="400">
                                    <defs>
                                        <linearGradient id="taskGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                                            <stop offset="0%" style="stop-color:#4A90E2;stop-opacity:1" />
                                            <stop offset="100%" style="stop-color:#4A90E2;stop-opacity:0.8" />
                                        </linearGradient>
                                        <linearGradient id="progressGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                                            <stop offset="0%" style="stop-color:#7ED321;stop-opacity:1" />
                                            <stop offset="100%" style="stop-color:#7ED321;stop-opacity:0.9" />
                                        </linearGradient>
                                    </defs>
                                    <g class="gantt-background-layer"></g>
                                    <g class="gantt-grid-layer"></g>
                                    <g class="gantt-tasks-layer"></g>
                                    <g class="gantt-milestones-layer"></g>
                                </svg>
                            </div>
                        </div>
                    </div>
                    
                    <div class="gantt-footer">
                        <div class="gantt-status">
                            <span>准备中...</span>
                        </div>
                    </div>
                `;

          this.elements = {
            tableHeader: this.container.querySelector(".gantt-table-header"),
            tableBody: this.container.querySelector(".gantt-table-body"),
            chartBody: this.container.querySelector(".gantt-chart-body"),
            svg: this.container.querySelector(".gantt-svg"),
            status: this.container.querySelector(".gantt-status"),
            toolbar: this.container.querySelector(".gantt-toolbar"),
          };
        }

        renderTable() {
          // 渲染表头
          const headerHtml = this.options.taskList.columns
            .map((col) => `<div class="gantt-table-header-cell" style="width: ${col.width}px">${col.title}</div>`)
            .join("");
          this.elements.tableHeader.innerHTML = headerHtml;

          // 渲染表格内容
          const rowsHtml = this.data
            .map((task) => {
              const cellsHtml = this.options.taskList.columns
                .map((col) => {
                  let value = this.getCellValue(task, col);
                  return `<div class="gantt-table-cell" style="width: ${col.width}px">${value}</div>`;
                })
                .join("");

              return `<div class="gantt-table-row" data-task-id="${task.id}">${cellsHtml}</div>`;
            })
            .join("");

          this.elements.tableBody.innerHTML = rowsHtml;
        }

        getCellValue(task, column) {
          switch (column.key) {
            case "name":
              return task.name;
            case "assignee":
              return task.assignee || "-";
            case "duration":
              return `${task.duration}天`;
            case "progress":
              return `${Math.round(task.progress * 100)}%`;
            default:
              return task[column.key] || "-";
          }
        }

        renderChart() {
          const svg = this.elements.svg;
          const tasksLayer = svg.querySelector(".gantt-tasks-layer");
          const milestonesLayer = svg.querySelector(".gantt-milestones-layer");

          // 清空现有内容
          tasksLayer.innerHTML = "";
          milestonesLayer.innerHTML = "";

          // 设置SVG高度
          const totalHeight = this.data.length * this.options.rowHeight;
          svg.style.height = `${Math.max(totalHeight, 400)}px`;

          // 渲染任务
          this.data.forEach((task, index) => {
            const y = index * this.options.rowHeight;

            // 创建任务组
            const taskGroup = document.createElementNS("http://www.w3.org/2000/svg", "g");
            taskGroup.setAttribute("class", "task-group");
            taskGroup.setAttribute("data-task-id", task.id);

            // 任务条
            const rect = document.createElementNS("http://www.w3.org/2000/svg", "rect");
            rect.setAttribute("x", 50 + index * 120);
            rect.setAttribute("y", y + 8);
            rect.setAttribute("width", task.duration * 8);
            rect.setAttribute("height", 24);
            rect.setAttribute("fill", "url(#taskGradient)");
            rect.setAttribute("rx", 4);
            rect.setAttribute("class", "task-main-bar");

            // 进度条
            const progressRect = document.createElementNS("http://www.w3.org/2000/svg", "rect");
            progressRect.setAttribute("x", 50 + index * 120);
            progressRect.setAttribute("y", y + 8);
            progressRect.setAttribute("width", task.duration * 8 * (task.progress || 0));
            progressRect.setAttribute("height", 24);
            progressRect.setAttribute("fill", "url(#progressGradient)");
            progressRect.setAttribute("rx", 4);
            progressRect.setAttribute("class", "task-progress-bar");

            // 任务文本
            const text = document.createElementNS("http://www.w3.org/2000/svg", "text");
            text.setAttribute("x", 50 + index * 120 + 8);
            text.setAttribute("y", y + 24);
            text.setAttribute("font-size", "11px");
            text.setAttribute("fill", "white");
            text.setAttribute("class", "task-text");
            text.textContent = task.name.length > 12 ? task.name.substring(0, 12) + "..." : task.name;

            taskGroup.appendChild(rect);
            taskGroup.appendChild(progressRect);
            taskGroup.appendChild(text);
            tasksLayer.appendChild(taskGroup);

            // 渲染里程碑
            if (task.timelines) {
              task.timelines.forEach((timeline) => {
                if (timeline.milestones) {
                  timeline.milestones.forEach((milestone, mIndex) => {
                    const milestoneGroup = document.createElementNS("http://www.w3.org/2000/svg", "g");
                    milestoneGroup.setAttribute("class", "milestone");
                    milestoneGroup.setAttribute("data-milestone-id", milestone.name);

                    const diamond = document.createElementNS("http://www.w3.org/2000/svg", "polygon");
                    diamond.setAttribute("points", "0,-8 8,0 0,8 -8,0");
                    diamond.setAttribute("fill", "#FF9500");
                    diamond.setAttribute("stroke", "#333");
                    diamond.setAttribute("stroke-width", 1);
                    diamond.setAttribute(
                      "transform",
                      `translate(${50 + index * 120 + task.duration * 8 + 20 + mIndex * 30}, ${y + 20})`
                    );

                    milestoneGroup.appendChild(diamond);
                    milestonesLayer.appendChild(milestoneGroup);
                  });
                }
              });
            }
          });
        }

        bindEvents() {
          // 任务点击
          this.elements.svg.addEventListener("click", (e) => {
            const taskGroup = e.target.closest("[data-task-id]");
            if (taskGroup) {
              const taskId = taskGroup.dataset.taskId;
              this.selectTask(taskId);
              const task = this.data.find((t) => t.id === taskId);
              this.emit("taskClick", task);
              return;
            }

            const milestone = e.target.closest("[data-milestone-id]");
            if (milestone) {
              const milestoneName = milestone.dataset.milestoneId;
              this.emit("milestoneClick", { name: milestoneName });
            }
          });

          // 工具栏事件
          this.elements.toolbar.addEventListener("click", (e) => {
            const viewBtn = e.target.closest("[data-view]");
            if (viewBtn) {
              this.changeViewMode(viewBtn.dataset.view);
              return;
            }

            const buttonId = e.target.id;
            switch (buttonId) {
              case "gantt-zoom-in":
                this.zoomIn();
                break;
              case "gantt-zoom-out":
                this.zoomOut();
                break;
              case "gantt-fit-view":
                this.fitToView();
                break;
              case "gantt-refresh":
                this.refresh();
                break;
            }
          });

          // 同步滚动
          this.elements.tableBody.addEventListener("scroll", (e) => {
            this.elements.chartBody.scrollTop = e.target.scrollTop;
          });

          this.elements.chartBody.addEventListener("scroll", (e) => {
            this.elements.tableBody.scrollTop = e.target.scrollTop;
          });
        }

        selectTask(taskId) {
          // 清除之前的选择
          this.container.querySelectorAll(".selected").forEach((el) => {
            el.classList.remove("selected");
          });

          // 添加新的选择
          this.state.selectedTasks.clear();
          this.state.selectedTasks.add(taskId);

          const tableRow = this.container.querySelector(`[data-task-id="${taskId}"]`);
          const taskGroup = this.elements.svg.querySelector(`[data-task-id="${taskId}"]`);

          if (tableRow) tableRow.classList.add("selected");
          if (taskGroup) taskGroup.classList.add("selected");
        }

        changeViewMode(newMode) {
          this.state.viewMode = newMode;
          this.container.querySelectorAll("[data-view]").forEach((btn) => {
            btn.classList.toggle("active", btn.dataset.view === newMode);
          });
          this.emit("viewChange", { mode: newMode });
        }

        zoomIn() {
          this.state.zoomLevel = Math.min(this.state.zoomLevel * 1.2, 3.0);
          this.emit("zoom", this.state.zoomLevel);
        }

        zoomOut() {
          this.state.zoomLevel = Math.max(this.state.zoomLevel / 1.2, 0.3);
          this.emit("zoom", this.state.zoomLevel);
        }

        fitToView() {
          this.state.zoomLevel = 1.0;
          this.emit("zoom", this.state.zoomLevel);
        }

        refresh() {
          this.renderTable();
          this.renderChart();
          this.updateStatus();
          this.emit("refresh");
        }

        updateStatus() {
          const completed = this.data.filter((t) => t.status === "completed").length;
          const inProgress = this.data.filter((t) => t.status === "in-progress").length;
          const pending = this.data.filter((t) => t.status === "pending").length;

          this.elements.status.innerHTML = `
                    <span>总任务: ${this.data.length}</span>
                    <span>已完成: ${completed}</span>
                    <span>进行中: ${inProgress}</span>
                    <span>待开始: ${pending}</span>
                    <span>视图: ${DateUtils.getTimeUnit(this.state.viewMode).name}</span>
                    <span>缩放: ${Math.round(this.state.zoomLevel * 100)}%</span>
                `;
        }

        // 简单的事件系统
        on(event, callback) {
          if (!this.events.has(event)) {
            this.events.set(event, []);
          }
          this.events.get(event).push(callback);
        }

        emit(event, data) {
          if (this.events.has(event)) {
            this.events.get(event).forEach((callback) => callback(data));
          }
        }

        // 公共API
        setData(data) {
          this.data = data;
          this.renderTable();
          this.renderChart();
          this.updateStatus();
          this.emit("dataChange", data);
        }

        getData() {
          return this.data;
        }

        getSelectedTasks() {
          return Array.from(this.state.selectedTasks)
            .map((id) => this.data.find((task) => task.id === id))
            .filter(Boolean);
        }

        addTask(taskData) {
          const task = {
            id: `task-${Date.now()}`,
            progress: 0,
            status: "pending",
            ...taskData,
          };
          this.data.push(task);
          this.renderTable();
          this.renderChart();
          this.updateStatus();
          return task;
        }

        getStats() {
          return {
            totalTasks: this.data.length,
            completedTasks: this.data.filter((t) => t.status === "completed").length,
            inProgressTasks: this.data.filter((t) => t.status === "in-progress").length,
            pendingTasks: this.data.filter((t) => t.status === "pending").length,
            totalMilestones: this.data.reduce((count, task) => {
              return (
                count +
                (task.timelines || []).reduce((mCount, timeline) => {
                  return mCount + (timeline.milestones || []).length;
                }, 0)
              );
            }, 0),
          };
        }

        scrollToTask(taskId) {
          const taskIndex = this.data.findIndex((task) => task.id === taskId);
          if (taskIndex !== -1) {
            const y = taskIndex * this.options.rowHeight;
            this.elements.chartBody.scrollTop = y;
            this.elements.tableBody.scrollTop = y;
            this.selectTask(taskId);
          }
        }

        exportData(format = "json") {
          if (format === "json") {
            return JSON.stringify(this.data, null, 2);
          }
          // 可以扩展其他格式
          return this.data;
        }
      }

      // 全局变量
      let ganttInstance = null;
      let consoleOutput = null;

      // API 演示对象
      const demoAPI = {
        showData() {
          this.log("当前数据:", JSON.stringify(ganttInstance.getData(), null, 2));
        },

        showSelection() {
          const selected = ganttInstance.getSelectedTasks();
          this.log("选中的任务:", selected.length > 0 ? selected : "无");
        },

        addRandomTask() {
          const today = DateUtils.today();
          const randomTask = {
            name: `随机任务 ${Date.now()}`,
            startDate: DateUtils.formatDate(DateUtils.addDays(today, Math.floor(Math.random() * 30))),
            endDate: DateUtils.formatDate(DateUtils.addDays(today, Math.floor(Math.random() * 30) + 10)),
            duration: Math.floor(Math.random() * 10) + 3,
            progress: Math.random(),
            assignee: `用户${Math.floor(Math.random() * 10)}`,
            status: ["pending", "in-progress", "completed"][Math.floor(Math.random() * 3)],
          };

          const added = ganttInstance.addTask(randomTask);
          this.log("添加任务:", added.name);
        },

        updateFirstTask() {
          const tasks = ganttInstance.getData();
          if (tasks.length > 0) {
            const firstTask = tasks[0];
            firstTask.progress = Math.min(1.0, firstTask.progress + 0.1);
            ganttInstance.setData(tasks);
            this.log("更新第一个任务进度:", `${Math.round(firstTask.progress * 100)}%`);
          }
        },

        testZoom() {
          ganttInstance.zoomIn();
          this.log("放大视图");
          setTimeout(() => {
            ganttInstance.zoomOut();
            this.log("缩小视图");
          }, 1000);
        },

        testViewMode() {
          const modes = ["day", "week", "month", "quarter"];
          const currentIndex = modes.indexOf(ganttInstance.state.viewMode);
          const nextIndex = (currentIndex + 1) % modes.length;
          ganttInstance.changeViewMode(modes[nextIndex]);
          this.log("切换视图模式:", modes[nextIndex]);
        },

        clearConsole() {
          if (consoleOutput) {
            consoleOutput.textContent = "控制台已清空...\n";
          }
        },

        log(message, data) {
          if (!consoleOutput) {
            consoleOutput = document.getElementById("console-output");
          }

          const timestamp = new Date().toLocaleTimeString();
          const logMessage = `[${timestamp}] ${message}\n`;

          if (data) {
            consoleOutput.textContent += logMessage + JSON.stringify(data, null, 2) + "\n\n";
          } else {
            consoleOutput.textContent += logMessage + "\n";
          }

          consoleOutput.scrollTop = consoleOutput.scrollHeight;
        },
      };

      // 显示统计面板
      function showStatsPanel() {
        const stats = ganttInstance.getStats();

        const existingPanel = document.querySelector(".stats-panel");
        if (existingPanel) {
          existingPanel.remove();
        }

        const statsPanel = document.createElement("div");
        statsPanel.className = "stats-panel";
        statsPanel.innerHTML = `
                <h3>📊 项目统计</h3>
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-value">${stats.totalTasks}</div>
                        <div class="stat-label">总任务数</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">${stats.completedTasks}</div>
                        <div class="stat-label">已完成</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">${stats.inProgressTasks}</div>
                        <div class="stat-label">进行中</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">${stats.pendingTasks}</div>
                        <div class="stat-label">待开始</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">${stats.totalMilestones}</div>
                        <div class="stat-label">里程碑数</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">${Math.round((stats.completedTasks / stats.totalTasks) * 100)}%</div>
                        <div class="stat-label">完成率</div>
                    </div>
                </div>
            `;

        document.querySelector(".gantt-content").appendChild(statsPanel);

        // 3秒后自动移除
        setTimeout(() => {
          if (statsPanel.parentNode) {
            statsPanel.remove();
          }
        }, 5000);
      }

      // 初始化
      document.addEventListener("DOMContentLoaded", () => {
        console.log("🚀 开始初始化甘特图演示...");

        // 初始化甘特图
        ganttInstance = new SimpleGanttChart("gantt-container", {
          data: DataGenerator.generateDefaultData(),
          taskList: {
            columns: [
              { key: "name", title: "任务名称", width: 160 },
              { key: "assignee", title: "负责人", width: 70 },
              { key: "duration", title: "工期", width: 50 },
            ],
          },
        });

        // 绑定甘特图事件
        ganttInstance.on("taskClick", (task) => {
          console.log("任务被点击:", task);
          alert(
            `📋 任务: ${task.name}\n👤 负责人: ${task.assignee || "未分配"}\n📊 进度: ${Math.round(
              (task.progress || 0) * 100
            )}%\n📅 工期: ${task.duration}天`
          );
        });

        ganttInstance.on("milestoneClick", (milestone) => {
          console.log("里程碑被点击:", milestone);
          alert(`🎯 里程碑: ${milestone.name}`);
        });

        ganttInstance.on("viewChange", (data) => {
          console.log("视图模式改变:", data);
          demoAPI.log("视图模式改变", data.mode);
        });

        ganttInstance.on("dataChange", () => {
          console.log("数据已更新");
          demoAPI.log("数据已更新");
        });

        // 绑定控制面板事件
        document.getElementById("data-selector").addEventListener("change", (e) => {
          const selectedType = e.target.value;
          let newData = [];

          switch (selectedType) {
            case "default":
              newData = DataGenerator.generateDefaultData();
              break;
            case "complex":
              newData = DataGenerator.generateComplexData();
              break;
            case "simple":
              newData = DataGenerator.generateSimpleData();
              break;
            case "large":
              newData = DataGenerator.generateLargeData();
              break;
          }

          ganttInstance.setData(newData);
          console.log(`已切换到 ${selectedType} 数据，共 ${newData.length} 个任务`);
        });

        document.getElementById("reload-data").addEventListener("click", () => {
          const currentType = document.getElementById("data-selector").value;
          document.getElementById("data-selector").dispatchEvent(new Event("change"));
        });

        document.getElementById("add-task").addEventListener("click", () => {
          const today = DateUtils.today();
          const newTask = {
            name: `新任务 ${Date.now()}`,
            startDate: DateUtils.formatDate(today),
            endDate: DateUtils.formatDate(DateUtils.addDays(today, 5)),
            duration: 5,
            progress: 0,
            assignee: "新用户",
            status: "pending",
          };

          ganttInstance.addTask(newTask);
          console.log("已添加新任务:", newTask.name);
        });

        document.getElementById("export-data").addEventListener("click", () => {
          try {
            const jsonData = ganttInstance.exportData("json");
            const blob = new Blob([jsonData], { type: "application/json" });
            const url = URL.createObjectURL(blob);

            const link = document.createElement("a");
            link.href = url;
            link.download = `gantt-data-${DateUtils.formatDate(DateUtils.today())}.json`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            URL.revokeObjectURL(url);
            console.log("数据已导出");
          } catch (error) {
            console.error("导出失败:", error);
            alert("导出失败: " + error.message);
          }
        });

        document.getElementById("scroll-to-today").addEventListener("click", () => {
          console.log("跳转到今天");
          // 简化实现：滚动到第一个任务
          if (ganttInstance.getData().length > 0) {
            ganttInstance.scrollToTask(ganttInstance.getData()[0].id);
          }
        });

        document.getElementById("select-first").addEventListener("click", () => {
          const tasks = ganttInstance.getData();
          if (tasks.length > 0) {
            ganttInstance.scrollToTask(tasks[0].id);
            console.log("已选择第一个任务:", tasks[0].name);
          }
        });

        document.getElementById("show-stats").addEventListener("click", () => {
          showStatsPanel();
        });

        // 暴露到全局作用域，便于调试
        window.ganttInstance = ganttInstance;
        window.demoAPI = demoAPI;
        window.DataGenerator = DataGenerator;

        // 显示API演示面板
        const apiPanel = document.getElementById("api-demo-panel");
        apiPanel.style.display = "block";
        document.body.appendChild(apiPanel);

        console.log("✅ 甘特图演示初始化完成！");
        console.log("💡 提示：");
        console.log("- 在控制台输入 ganttInstance 查看甘特图实例");
        console.log("- 在控制台输入 demoAPI 查看演示API");
        console.log("- 点击任务条或里程碑进行交互");
        console.log("- 使用工具栏切换视图和操作");

        demoAPI.log("甘特图演示初始化完成", {
          version: "2.0-enhanced",
          features: ["多时间线", "SVG渲染", "事件系统", "数据验证"],
          totalTasks: ganttInstance.getData().length,
        });
      });
    </script>
  </body>
</html>

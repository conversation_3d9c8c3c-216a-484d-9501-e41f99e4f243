# 甘特图组件 Step2 使用指南

## 📖 概述

这是甘特图组件开发计划中 **Step2: 数据结构和工具系统** 的实现版本，集成了以下核心工具类：

- **DateUtils**: 日期处理工具类
- **EventEmitter**: 事件发布订阅系统
- **SvgUtils**: SVG 操作工具类
- **dataStructure**: 增强的数据结构（TaskItem、Timeline、Milestone 等）

## 🚀 快速开始

### 基础使用

```html
<!DOCTYPE html>
<html>
  <head>
    <link rel="stylesheet" href="path/to/gantt.css" />
  </head>
  <body>
    <div id="gantt-container" style="width: 100%; height: 600px;"></div>

    <script type="module">
      import GanttChart from "./path/to/GanttChart.js";

      const gantt = new GanttChart("gantt-container", {
        data: [
          {
            id: "task-1",
            name: "项目启动",
            startDate: "2024-01-01",
            endDate: "2024-01-05",
            duration: 5,
            progress: 1.0,
            assignee: "张三",
            status: "completed",
          },
        ],
        viewMode: "day",
        onTaskClick: (task) => {
          console.log("任务被点击:", task);
        },
      });
    </script>
  </body>
</html>
```

### 演示页面

打开 `demo.html` 即可查看完整的功能演示，包括：

- 多种数据类型切换
- 实时 API 操作演示
- 统计面板显示
- 完整的交互功能

## 🛠️ 工具类详解

### DateUtils - 日期处理工具

```javascript
// 解析日期
const date = DateUtils.parseDate("2024-01-01");

// 格式化日期
const formatted = DateUtils.formatDate(new Date(), "YYYY-MM-DD");

// 计算日期差
const diff = DateUtils.getDaysDiff(startDate, endDate);

// 添加天数
const futureDate = DateUtils.addDays(new Date(), 7);

// 生成时间刻度
const scales = DateUtils.generateTimeScale(startDate, endDate, "day");

// 视图模式常量
DateUtils.VIEW_MODES.DAY; // 'day'
DateUtils.VIEW_MODES.WEEK; // 'week'
DateUtils.VIEW_MODES.MONTH; // 'month'
DateUtils.VIEW_MODES.QUARTER; // 'quarter'
```

### EventEmitter - 事件系统

```javascript
// 监听事件
gantt.on("taskClick", (task) => {
  console.log("任务被点击:", task);
});

// 一次性监听
gantt.once("ready", () => {
  console.log("甘特图已就绪");
});

// 触发事件
gantt.emit("customEvent", { data: "test" });

// 移除监听器
gantt.off("taskClick", handler);

// 等待事件
const result = await gantt.waitFor("dataLoad", 5000);
```

### SvgUtils - SVG 操作工具

```javascript
// 创建SVG元素
const rect = SvgUtils.createRect(10, 10, 100, 50, {
  fill: "#4A90E2",
  stroke: "#333",
  borderRadius: 4,
});

// 创建任务条
const taskBar = SvgUtils.createTaskBar(x, y, width, height, {
  progress: 0.6,
  text: "任务名称",
  fill: "#4A90E2",
});

// 创建里程碑
const milestone = SvgUtils.createMilestone(x, y, "diamond", {
  status: "review",
  showLine: true,
});

// 创建依赖关系线
const depLine = SvgUtils.createDependencyLine(fromPoint, toPoint, {
  stroke: "#666",
  showArrow: true,
});
```

### 数据结构使用

```javascript
// 创建任务
const task = new TaskItem({
  name: "开发任务",
  startDate: "2024-01-01",
  endDate: "2024-01-10",
  progress: 0.5,
  assignee: "开发者",
  timelines: [
    {
      name: "主要里程碑",
      type: "default",
      milestones: [
        {
          name: "需求确认",
          date: "2024-01-03",
          type: "review",
        },
      ],
    },
  ],
});

// 添加时间线
task.addTimeline({
  name: "技术评审",
  type: "technical",
  milestones: [
    {
      name: "技术方案评审",
      date: "2024-01-05",
      type: "review",
    },
  ],
});

// 获取所有里程碑
const milestones = task.getAllMilestones();

// 计算任务高度
const height = task.getRenderHeight();
```

## 🎛️ 配置选项

### 基础配置

```javascript
const options = {
  // 视图模式
  viewMode: "day", // 'day' | 'week' | 'month' | 'quarter'

  // 行高设置
  rowHeight: 40,

  // 像素比例
  pixelsPerDay: 30,

  // 日期范围（可选，自动计算）
  startDate: "2024-01-01",
  endDate: "2024-03-31",

  // 表格配置
  taskList: {
    display: true,
    width: 300,
    columns: [
      { key: "name", title: "任务名称", width: 180 },
      { key: "assignee", title: "负责人", width: 80 },
      { key: "duration", title: "工期", width: 60 },
    ],
  },

  // 主题颜色
  colors: {
    primary: "#4A90E2",
    success: "#7ED321",
    warning: "#F5A623",
    danger: "#D0021B",
  },

  // 功能开关
  enableMilestones: true,
  enableMultiTimeline: true,
  enableVirtualScroll: false, // Step2暂未实现

  // 事件回调
  onTaskClick: (task) => {},
  onMilestoneClick: (milestone) => {},
  onDataChange: (data) => {},
  onRenderComplete: (stats) => {},
};
```

### 任务数据格式

```javascript
const taskData = {
  // 基础信息
  id: "task-1",
  name: "任务名称",
  description: "任务描述",

  // 时间信息
  startDate: "2024-01-01",
  endDate: "2024-01-10",
  duration: 10, // 自动计算

  // 进度和状态
  progress: 0.5, // 0-1
  status: "in-progress", // 'pending' | 'in-progress' | 'completed' | 'cancelled'
  priority: "normal", // 'low' | 'normal' | 'high' | 'critical'

  // 层级结构
  level: 0,
  parentId: null,
  children: [],
  collapsed: false,

  // 资源分配
  assignee: "负责人",
  team: ["成员1", "成员2"],

  // 多时间线（新特性）
  timelines: [
    {
      id: "timeline-1",
      name: "主要里程碑",
      type: "default", // 'default' | 'critical' | 'secondary' | 'technical' | 'business'
      color: "#4A90E2",
      visible: true,
      milestones: [
        {
          id: "milestone-1",
          name: "里程碑名称",
          date: "2024-01-05",
          type: "review", // 'review' | 'delivery' | 'approval' | 'warning' | 'info'
          status: "pending", // 'pending' | 'completed' | 'overdue'
          icon: "diamond", // 'diamond' | 'circle' | 'triangle' | 'star'
        },
      ],
    },
  ],

  // 自定义字段
  customFields: {
    department: "开发部",
    cost: 10000,
    priority: "high",
  },
};
```

## 📊 事件系统

### 内置事件

```javascript
// 生命周期事件
gantt.on("init", () => {}); // 初始化开始
gantt.on("ready", () => {}); // 初始化完成
gantt.on("destroy", () => {}); // 销毁时触发

// 数据事件
gantt.on("dataChange", (data) => {}); // 数据变更
gantt.on("dataLoad", (data) => {}); // 数据加载
gantt.on("dataError", (error) => {}); // 数据错误

// 交互事件
gantt.on("taskClick", (task) => {}); // 任务点击
gantt.on("taskSelect", (task) => {}); // 任务选择
gantt.on("milestoneClick", (milestone) => {}); // 里程碑点击

// 视图事件
gantt.on("viewChange", (data) => {}); // 视图模式变更
gantt.on("zoomChange", (level) => {}); // 缩放变更
gantt.on("scroll", (position) => {}); // 滚动事件

// 渲染事件
gantt.on("renderStart", () => {}); // 渲染开始
gantt.on("renderEnd", (stats) => {}); // 渲染结束
gantt.on("renderError", (error) => {}); // 渲染错误

// 系统事件
gantt.on("error", (error) => {}); // 通用错误
gantt.on("warning", (message) => {}); // 警告信息
gantt.on("resize", () => {}); // 窗口调整
```

## 🔧 API 方法

### 数据操作

```javascript
// 设置数据
gantt.setData(newData);

// 获取数据
const data = gantt.getData();

// 添加任务
const newTask = gantt.addTask({
  name: "新任务",
  startDate: "2024-01-01",
  endDate: "2024-01-05",
  assignee: "负责人",
});

// 删除任务
gantt.removeTask("task-id");

// 更新任务
gantt.updateTask("task-id", {
  progress: 0.8,
  status: "completed",
});

// 获取选中的任务
const selectedTasks = gantt.getSelectedTasks();

// 获取统计信息
const stats = gantt.getStats();
```

### 视图控制

```javascript
// 改变视图模式
gantt.changeViewMode("week");

// 缩放控制
gantt.zoomIn();
gantt.zoomOut();
gantt.fitToView();

// 滚动控制
gantt.scrollToTask("task-id");
gantt.scrollToDate("2024-01-15");

// 刷新视图
gantt.refresh();
```

### 选择操作

```javascript
// 选择任务
gantt.selectTask("task-id");

// 选择里程碑
gantt.selectMilestone("milestone-id");

// 切换任务折叠状态
gantt.toggleTaskCollapse("task-id");

// 获取可见任务
const visibleTasks = gantt.getVisibleTasks();
```

### 数据导出

```javascript
// 导出为JSON
const jsonData = gantt.exportData("json");

// 导出为CSV
const csvData = gantt.exportData("csv");

// 导出图表为SVG
gantt.exportChart();
```

## 🎨 样式定制

### CSS 变量

```css
:root {
  /* 主题颜色 */
  --gantt-primary: #4a90e2;
  --gantt-success: #7ed321;
  --gantt-warning: #f5a623;
  --gantt-danger: #d0021b;

  /* 背景颜色 */
  --gantt-bg-primary: #ffffff;
  --gantt-bg-secondary: #fafafa;
  --gantt-bg-tertiary: #f1f3f4;

  /* 尺寸设置 */
  --gantt-row-height: 40px;
  --gantt-header-height: 50px;
  --gantt-font-size-normal: 12px;
}
```

### 主题类

```html
<!-- 现代主题 -->
<div id="gantt" class="gantt-theme-modern"></div>

<!-- 经典主题 -->
<div id="gantt" class="gantt-theme-classic"></div>

<!-- 极简主题 -->
<div id="gantt" class="gantt-theme-minimal"></div>
```

### 自定义任务样式

```css
/* 高优先级任务 */
.gantt-table-row.priority-high {
  border-left: 3px solid var(--gantt-warning);
}

/* 关键任务 */
.gantt-table-row.priority-critical {
  border-left: 3px solid var(--gantt-danger);
}

/* 已完成任务 */
.task-group.status-completed .task-main-bar {
  opacity: 0.6;
}

/* 逾期任务 */
.task-group.status-overdue .task-main-bar {
  fill: var(--gantt-danger);
}
```

## 📱 响应式设计

组件内置响应式支持：

- **大屏幕 (≥1024px)**: 完整功能显示
- **中等屏幕 (768px-1023px)**: 工具栏自适应布局
- **小屏幕 (≤767px)**: 紧凑模式，隐藏部分操作
- **移动设备 (≤480px)**: 极简模式

```css
/* 自定义响应式断点 */
@media (max-width: 768px) {
  .gantt-left-panel {
    min-width: 150px;
    width: 250px;
  }
}
```

## 🔍 数据验证

### 自动数据验证

```javascript
import { DataValidator } from "./utils/dataStructure.js";

// 验证甘特图数据
const validation = DataValidator.validateGanttData(data);
if (!validation.valid) {
  console.error("数据验证失败:", validation.errors);
}

// 验证单个任务
const taskValidation = DataValidator.validateTask(taskData);

// 验证时间线
const timelineValidation = DataValidator.validateTimeline(timelineData);
```

### 常见验证规则

- 任务名称不能为空
- 开始日期不能晚于结束日期
- 进度值必须在 0-1 之间
- 任务 ID 必须唯一
- 里程碑日期必须有效
- 时间线必须有名称

## 🚀 性能优化

### 大数据处理

```javascript
const gantt = new GanttChart("container", {
  // 最大可见任务数（虚拟滚动）
  maxVisibleTasks: 1000,

  // 渲染批次大小
  renderBatchSize: 50,

  // 启用虚拟滚动（Step4实现）
  enableVirtualScroll: false,
});
```

### 渲染优化

- SVG 分层渲染
- 元素复用机制
- 增量更新算法
- 事件委托处理

### 内存管理

- 自动清理未使用元素
- 事件监听器自动移除
- 数据引用及时释放

## 🐛 调试支持

### 开发模式

```javascript
// 启用调试模式
gantt.setDebug(true);

// 获取性能统计
const stats = gantt.getStats();
console.log("渲染时间:", stats.renderTime);

// 获取事件统计
const eventStats = gantt.getEventStats();
```

### 控制台 API

在浏览器控制台中：

```javascript
// 全局甘特图实例
window.ganttInstance;

// 演示API
window.demoAPI.showData();
window.demoAPI.addRandomTask();
window.demoAPI.testZoom();

// 数据生成器
window.DataGenerator.generateLargeData(100);
```

## 📋 数据迁移

### 从旧版本升级

```javascript
import { DataTransformer } from "./utils/dataStructure.js";

// 从 v1.x 数据迁移
const oldData = [
  /* 旧版本数据 */
];
const newData = DataTransformer.migrateFromV1(oldData);

// 优化时间线布局
const optimizedData = DataTransformer.optimizeTimelineLayout(newData);

// 导出标准格式
const standardData = DataTransformer.exportToStandard(optimizedData);
```

### 数据格式转换

```javascript
// 生成示例数据
const sampleData = DataTransformer.generateSampleData(20, 3);

// 获取时间线统计
const timelineStats = DataTransformer.exportTimelineStatistics(data);
console.log("时间线复杂度:", timelineStats.layoutComplexity);
```

## 🔒 浏览器兼容性

### 支持的浏览器

- **Chrome**: 60+
- **Firefox**: 55+
- **Safari**: 12+
- **Edge**: 79+
- **移动浏览器**: iOS Safari 12+, Chrome Mobile 60+

### Polyfill 需求

对于较老的浏览器，可能需要以下 Polyfill：

```html
<!-- ES6 Promise -->
<script src="https://cdn.jsdelivr.net/npm/es6-promise@4/dist/es6-promise.auto.min.js"></script>

<!-- Array.from -->
<script src="https://cdn.jsdelivr.net/npm/core-js-bundle@3/minified.js"></script>
```

## ❗ 常见问题

### Q: 如何处理大量数据？

A: Step2 版本建议任务数量控制在 1000 个以内。后续 Step4 将实现虚拟滚动支持大量数据。

### Q: 如何自定义任务颜色？

A: 可以通过 CSS 变量或任务数据中的 `style` 字段自定义：

```javascript
const task = {
  name: "自定义任务",
  style: {
    fill: "#FF6B6B",
    stroke: "#FF5252",
  },
};
```

### Q: 里程碑不显示怎么办？

A: 检查以下项目：

1. `enableMilestones` 选项是否为 `true`
2. 里程碑数据中的 `date` 字段是否有效
3. 时间线是否设置为可见 `visible: true`

### Q: 如何监听任务拖拽事件？

A: Step2 版本暂不支持拖拽功能，将在后续步骤中实现。

### Q: 如何导出高分辨率图片？

A: 当前支持 SVG 导出，可以在专业软件中转换为高分辨率图片，或使用第三方库：

```javascript
// 使用 html2canvas 导出 PNG
import html2canvas from "html2canvas";

html2canvas(gantt.elements.svg).then((canvas) => {
  const link = document.createElement("a");
  link.download = "gantt-chart.png";
  link.href = canvas.toDataURL();
  link.click();
});
```

## 🔮 后续版本预览

Step2 是基础版本，后续步骤将添加：

- **Step 3**: 进度显示和里程碑增强
- **Step 4**: 虚拟滚动和性能优化
- **Step 5**: 拖拽交互和编辑功能
- **Step 6**: 依赖关系和高级功能
- **Step 7**: Vue/React 框架适配器
- **Step 8**: 测试、文档和发布

## 📚 相关资源

- [项目开发计划](./甘特图组件分步实现计划.md)
- [数据结构文档](./dataStructure.js)
- [工具类 API](./utils/)
- [演示页面](./demo.html)
- [CSS 样式指南](./gantt.css)

---

**版本**: Step2 Enhanced  
**更新时间**: 2024-01-07  
**作者**: 甘特图开发团队

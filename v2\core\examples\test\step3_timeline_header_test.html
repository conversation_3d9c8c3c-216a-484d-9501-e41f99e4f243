<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Step3: 时间轴头部渲染测试</title>
    <link rel="stylesheet" href="../../src/styles/gantt.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }

        .header h1 {
            margin: 0;
            font-size: 24px;
        }

        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
        }

        .test-controls {
            padding: 20px;
            border-bottom: 1px solid #eee;
            background: #f9f9f9;
        }

        .control-group {
            display: inline-block;
            margin-right: 20px;
            margin-bottom: 10px;
        }

        .control-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #333;
        }

        .control-group select,
        .control-group input,
        .control-group button {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        .control-group button {
            background: #28a745;
            color: white;
            border: none;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .control-group button:hover {
            background: #218838;
        }

        .gantt-container {
            height: 600px;
            position: relative;
        }

        .test-info {
            padding: 20px;
            background: #f8f9fa;
            border-top: 1px solid #eee;
        }

        .test-info h3 {
            margin: 0 0 10px 0;
            color: #333;
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }

        .info-item {
            background: white;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid #28a745;
        }

        .info-label {
            font-size: 12px;
            color: #666;
            text-transform: uppercase;
            margin-bottom: 5px;
        }

        .info-value {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            font-family: monospace;
        }

        /* 高亮时间轴头部样式 */
        .gantt-timeline-header {
            border: 2px solid #28a745 !important;
            background: rgba(40, 167, 69, 0.1) !important;
        }

        /* 顶部行样式增强 */
        .timeline-scales-top-row {
            background: linear-gradient(135deg, rgba(40, 167, 69, 0.1) 0%, rgba(40, 167, 69, 0.05) 100%) !important;
        }

        .timeline-scale-group.top-row {
            transition: all 0.2s ease;
            border-right: 2px solid #28a745 !important;
        }

        .timeline-scale-group.top-row:hover {
            background: rgba(40, 167, 69, 0.2) !important;
            transform: translateY(-1px);
        }

        .scale-group-label {
            font-weight: 600 !important;
            color: #28a745 !important;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
        }

        /* 底部行样式增强 */
        .timeline-scales-bottom-row {
            background: rgba(40, 167, 69, 0.05) !important;
        }

        .timeline-scale.bottom-row {
            transition: all 0.2s ease;
        }

        .timeline-scale.bottom-row.major {
            background: rgba(40, 167, 69, 0.1) !important;
            border-right: 2px solid #28a745 !important;
        }

        .timeline-scale.bottom-row.minor {
            background: rgba(40, 167, 69, 0.03) !important;
            border-right: 1px solid #28a745 !important;
        }

        .timeline-scale.bottom-row:hover {
            background: rgba(40, 167, 69, 0.2) !important;
            transform: translateY(-1px);
        }

        .timeline-scale.bottom-row .scale-label {
            font-weight: 500 !important;
            color: #28a745 !important;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
        }

        .scroll-indicator {
            position: fixed;
            top: 50%;
            right: 20px;
            transform: translateY(-50%);
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 15px;
            border-radius: 8px;
            font-family: monospace;
            font-size: 12px;
            z-index: 1000;
            min-width: 200px;
        }

        .scroll-indicator h4 {
            margin: 0 0 10px 0;
            color: #28a745;
        }

        .scroll-indicator div {
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📅 Step3: 时间轴头部渲染测试（两行布局）</h1>
            <p>测试时间轴头部的两行显示：第一行显示月份/年份，第二行显示具体日期</p>
        </div>

        <div class="test-controls">
            <div class="control-group">
                <label>视图模式</label>
                <select id="viewModeSelect">
                    <option value="day">日视图</option>
                    <option value="week">周视图</option>
                    <option value="month">月视图</option>
                    <option value="quarter">季度视图</option>
                </select>
            </div>

            <div class="control-group">
                <label>缩放级别</label>
                <input type="range" id="zoomSlider" min="0.3" max="3.0" step="0.1" value="1.0">
                <span id="zoomValue">100%</span>
            </div>

            <div class="control-group">
                <button id="scrollToStartBtn">滚动到开始</button>
                <button id="scrollToMiddleBtn">滚动到中间</button>
                <button id="scrollToEndBtn">滚动到结束</button>
            </div>

            <div class="control-group">
                <button id="testScrollBtn">测试滚动性能</button>
                <button id="refreshHeaderBtn">刷新头部</button>
            </div>
        </div>

        <div class="gantt-container" id="ganttContainer"></div>

        <div class="test-info">
            <h3>时间轴头部信息</h3>
            <div class="info-grid">
                <div class="info-item">
                    <div class="info-label">当前视图模式</div>
                    <div class="info-value" id="currentViewMode">-</div>
                </div>
                <div class="info-item">
                    <div class="info-label">可见刻度数量</div>
                    <div class="info-value" id="visibleScalesCount">-</div>
                </div>
                <div class="info-item">
                    <div class="info-label">总刻度数量</div>
                    <div class="info-value" id="totalScalesCount">-</div>
                </div>
                <div class="info-item">
                    <div class="info-label">时间轴总宽度</div>
                    <div class="info-value" id="timelineWidth">-</div>
                </div>
                <div class="info-item">
                    <div class="info-label">头部更新次数</div>
                    <div class="info-value" id="headerUpdateCount">0</div>
                </div>
                <div class="info-item">
                    <div class="info-label">顶部行组数</div>
                    <div class="info-value" id="topRowGroups">-</div>
                </div>
                <div class="info-item">
                    <div class="info-label">底部行刻度数</div>
                    <div class="info-value" id="bottomRowScales">-</div>
                </div>
                <div class="info-item">
                    <div class="info-label">滚动位置</div>
                    <div class="info-value" id="scrollPosition">0, 0</div>
                </div>
            </div>
        </div>
    </div>

    <div class="scroll-indicator" id="scrollIndicator">
        <h4>滚动信息</h4>
        <div>水平: <span id="scrollX">0</span>px</div>
        <div>垂直: <span id="scrollY">0</span>px</div>
        <div>可见范围: <span id="visibleRange">-</span></div>
        <div>缓冲范围: <span id="bufferRange">-</span></div>
    </div>

    <script type="module">
        import GanttChart from '../../src/GanttChart.js';
        import DateUtils from '../../src/utils/DateUtils.js';

        // 生成测试数据
        function generateTestData() {
            const tasks = [];
            const startDate = new Date('2024-01-01');
            
            for (let i = 0; i < 30; i++) {
                const taskStart = DateUtils.addDays(startDate, i * 10);
                const duration = 5 + Math.random() * 15; // 5-20天
                const taskEnd = DateUtils.addDays(taskStart, duration);
                
                tasks.push({
                    id: `task-${i}`,
                    name: `任务 ${i + 1} - 测试时间轴头部渲染`,
                    startDate: taskStart,
                    endDate: taskEnd,
                    progress: Math.random(),
                    status: ['pending', 'in-progress', 'completed'][Math.floor(Math.random() * 3)],
                    priority: ['low', 'medium', 'high'][Math.floor(Math.random() * 3)]
                });
            }
            
            return tasks;
        }

        // 初始化甘特图
        let ganttInstance = null;
        let headerUpdateCount = 0;

        function initializeGantt() {
            const container = document.getElementById('ganttContainer');
            container.innerHTML = '';
            
            const testData = generateTestData();
            
            ganttInstance = new GanttChart('ganttContainer', {
                data: testData,
                viewMode: 'day',
                pixelsPerDay: 30,
                taskList: {
                    columns: [
                        { key: 'name', label: '任务名称', width: 250 },
                        { key: 'startDate', label: '开始日期', width: 100 },
                        { key: 'endDate', label: '结束日期', width: 100 }
                    ]
                }
            });

            // 监听事件
            ganttInstance.on('ready', () => {
                console.log('甘特图初始化完成');
                updateInfo();
            });

            ganttInstance.on('scroll', (data) => {
                updateScrollInfo(data.x, data.y);
            });

            ganttInstance.on('viewChange', () => {
                updateInfo();
            });

            ganttInstance.on('zoomChange', () => {
                updateInfo();
            });

            // 监听头部更新（通过重写方法）
            const originalRenderTimelineHeader = ganttInstance.renderTimelineHeader;
            ganttInstance.renderTimelineHeader = function() {
                headerUpdateCount++;
                document.getElementById('headerUpdateCount').textContent = headerUpdateCount;
                return originalRenderTimelineHeader.call(this);
            };
        }

        // 更新信息显示
        function updateInfo() {
            if (!ganttInstance || !ganttInstance.timeScale) return;

            const stats = ganttInstance.timeScale.getPerformanceStats();
            const viewportX = ganttInstance.elements.chartBody?.scrollLeft || 0;
            const viewportWidth = ganttInstance.elements.chartBody?.offsetWidth || 1000;
            const visibleScales = ganttInstance.timeScale.getVisibleScales(viewportX, viewportWidth);

            document.getElementById('currentViewMode').textContent = stats.viewMode;
            document.getElementById('visibleScalesCount').textContent = visibleScales.filter(s => s.isVisible).length;
            document.getElementById('totalScalesCount').textContent = stats.totalScales;
            document.getElementById('timelineWidth').textContent = `${Math.round(stats.totalWidth)}px`;

            // 统计两行结构信息
            setTimeout(() => {
                const topRowGroups = document.querySelectorAll('.timeline-scale-group.top-row').length;
                const bottomRowScales = document.querySelectorAll('.timeline-scale.bottom-row').length;

                document.getElementById('topRowGroups').textContent = topRowGroups;
                document.getElementById('bottomRowScales').textContent = bottomRowScales;
            }, 100);
        }

        // 更新滚动信息
        function updateScrollInfo(x, y) {
            document.getElementById('scrollX').textContent = Math.round(x);
            document.getElementById('scrollY').textContent = Math.round(y);
            document.getElementById('scrollPosition').textContent = `${Math.round(x)}, ${Math.round(y)}`;

            if (ganttInstance && ganttInstance.timeScale) {
                const viewportWidth = ganttInstance.elements.chartBody?.offsetWidth || 1000;
                const visibleRange = ganttInstance.timeScale.getVisibleRange(x, viewportWidth);
                
                document.getElementById('visibleRange').textContent = 
                    `${Math.round(visibleRange.viewport.start)} - ${Math.round(visibleRange.viewport.end)}`;
                document.getElementById('bufferRange').textContent = 
                    `${Math.round(visibleRange.buffer.start)} - ${Math.round(visibleRange.buffer.end)}`;
            }
        }

        // 事件绑定
        document.getElementById('viewModeSelect').addEventListener('change', (e) => {
            ganttInstance.changeViewMode(e.target.value);
        });

        document.getElementById('zoomSlider').addEventListener('input', (e) => {
            const zoomLevel = parseFloat(e.target.value);
            document.getElementById('zoomValue').textContent = `${Math.round(zoomLevel * 100)}%`;
            ganttInstance.setZoomLevel(zoomLevel);
        });

        document.getElementById('scrollToStartBtn').addEventListener('click', () => {
            ganttInstance.elements.chartBody.scrollLeft = 0;
        });

        document.getElementById('scrollToMiddleBtn').addEventListener('click', () => {
            const totalWidth = ganttInstance.timeScale.getTotalWidth();
            const viewportWidth = ganttInstance.elements.chartBody.offsetWidth;
            ganttInstance.elements.chartBody.scrollLeft = (totalWidth - viewportWidth) / 2;
        });

        document.getElementById('scrollToEndBtn').addEventListener('click', () => {
            const totalWidth = ganttInstance.timeScale.getTotalWidth();
            const viewportWidth = ganttInstance.elements.chartBody.offsetWidth;
            ganttInstance.elements.chartBody.scrollLeft = totalWidth - viewportWidth;
        });

        document.getElementById('testScrollBtn').addEventListener('click', () => {
            console.log('开始滚动性能测试...');
            const totalWidth = ganttInstance.timeScale.getTotalWidth();
            const viewportWidth = ganttInstance.elements.chartBody.offsetWidth;
            const maxScroll = totalWidth - viewportWidth;
            
            let currentScroll = 0;
            const step = maxScroll / 100;
            
            function animateScroll() {
                ganttInstance.elements.chartBody.scrollLeft = currentScroll;
                currentScroll += step;
                
                if (currentScroll <= maxScroll) {
                    requestAnimationFrame(animateScroll);
                } else {
                    console.log('滚动性能测试完成');
                }
            }
            
            animateScroll();
        });

        document.getElementById('refreshHeaderBtn').addEventListener('click', () => {
            ganttInstance.renderTimelineHeader();
        });

        // 初始化
        document.addEventListener('DOMContentLoaded', () => {
            console.log('页面加载完成，初始化甘特图...');
            initializeGantt();
        });
    </script>
</body>
</html>

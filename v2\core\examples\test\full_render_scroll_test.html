<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>全渲染滚动跟随测试</title>
    <link rel="stylesheet" href="../../src/styles/gantt.css">
    <style>
        body {
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
        }

        .test-container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .test-header {
            background: linear-gradient(135deg, #17a2b8 0%, #28a745 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }

        .test-header h1 {
            margin: 0 0 10px 0;
            font-size: 24px;
        }

        .test-header p {
            margin: 0;
            opacity: 0.9;
            font-size: 14px;
        }

        .test-instructions {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            border-radius: 6px;
            padding: 15px;
            margin: 20px;
            color: #0c5460;
        }

        .test-instructions h4 {
            margin: 0 0 10px 0;
            color: #0c5460;
        }

        .test-instructions ol {
            margin: 10px 0 0 20px;
            padding: 0;
        }

        .test-instructions li {
            margin-bottom: 5px;
        }

        .gantt-container {
            height: 600px;
            margin: 0;
            border-radius: 0;
            border: none;
            box-shadow: none;
        }

        .scroll-info {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(23, 162, 184, 0.9);
            color: white;
            padding: 15px;
            border-radius: 8px;
            font-family: monospace;
            font-size: 12px;
            z-index: 1000;
            min-width: 200px;
        }

        .scroll-info h4 {
            margin: 0 0 10px 0;
            font-size: 14px;
            color: #fff;
        }

        .scroll-info div {
            margin-bottom: 5px;
            display: flex;
            justify-content: space-between;
        }

        .scroll-info div:last-child {
            margin-bottom: 0;
        }

        /* 高亮时间轴头部以便观察 */
        .gantt-timeline-header {
            border: 2px solid #17a2b8 !important;
            background: rgba(23, 162, 184, 0.1) !important;
        }

        .timeline-scale {
            border: 1px solid rgba(23, 162, 184, 0.3) !important;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>🔄 全渲染滚动跟随测试</h1>
            <p>测试时间轴全渲染后是否正确跟随甘特图横向滚动</p>
        </div>

        <div class="test-instructions">
            <h4>🧪 测试步骤：</h4>
            <ol>
                <li>观察初始状态下时间轴显示的日期范围</li>
                <li>横向滚动甘特图内容区域</li>
                <li>检查时间轴头部是否同步滚动，显示对应的日期</li>
                <li>验证滚动是否流畅，无卡顿现象</li>
                <li>观察右上角的滚动位置信息</li>
            </ol>
            <p><strong>预期结果</strong>：时间轴头部应该与甘特图内容同步滚动，始终显示当前可视区域对应的日期范围。</p>
        </div>

        <div id="gantt-container" class="gantt-container"></div>
    </div>

    <div class="scroll-info" id="scrollInfo">
        <h4>📊 滚动状态</h4>
        <div>横向位置: <span id="scrollX">0</span></div>
        <div>纵向位置: <span id="scrollY">0</span></div>
        <div>总刻度数: <span id="totalScales">-</span></div>
        <div>渲染模式: <span>全渲染</span></div>
        <div>滚动同步: <span style="color: #28a745;">✅ 已启用</span></div>
    </div>

    <script type="module">
        import GanttChart from '../../src/GanttChart.js';

        // 生成测试数据 - 更大的时间范围以便测试滚动
        function generateTestData() {
            const data = [];
            const startDate = new Date('2024-01-01');
            
            for (let i = 0; i < 30; i++) {
                const taskStartDate = new Date(startDate);
                taskStartDate.setDate(startDate.getDate() + i * 10);
                
                const taskEndDate = new Date(taskStartDate);
                taskEndDate.setDate(taskStartDate.getDate() + 8);
                
                data.push({
                    id: `task-${i + 1}`,
                    name: `任务 ${i + 1}`,
                    startDate: taskStartDate.toISOString().split('T')[0],
                    endDate: taskEndDate.toISOString().split('T')[0],
                    progress: Math.random(),
                    assignee: `用户${i + 1}`,
                    status: ['pending', 'in-progress', 'completed'][Math.floor(Math.random() * 3)],
                    level: 0
                });
            }
            
            return data;
        }

        // 初始化甘特图
        let ganttInstance;
        
        try {
            ganttInstance = new GanttChart('gantt-container', {
                data: generateTestData(),
                viewMode: 'day',
                pixelsPerDay: 50, // 增加像素密度以便更好地测试滚动
                taskList: {
                    width: 250,
                    columns: [
                        { key: 'name', title: '任务名称', width: 150 },
                        { key: 'assignee', title: '负责人', width: 100 }
                    ]
                },
                timeline: {
                    showWeekday: true,
                    adaptiveWidth: true,
                    weekdayFormat: 'short'
                }
            });

            console.log('甘特图初始化成功');

            // 更新总刻度数
            if (ganttInstance.timeScale) {
                const allScales = ganttInstance.timeScale.getAllScales();
                document.getElementById('totalScales').textContent = allScales.length;
            }

            // 监听滚动事件
            ganttInstance.on('scroll', (data) => {
                document.getElementById('scrollX').textContent = Math.round(data.x);
                document.getElementById('scrollY').textContent = Math.round(data.y);
            });

            // 添加一些测试按钮
            const testButtons = document.createElement('div');
            testButtons.style.cssText = `
                position: fixed;
                bottom: 20px;
                right: 20px;
                display: flex;
                flex-direction: column;
                gap: 10px;
                z-index: 1000;
            `;

            const scrollToMiddleBtn = document.createElement('button');
            scrollToMiddleBtn.textContent = '滚动到中间';
            scrollToMiddleBtn.style.cssText = `
                padding: 10px 15px;
                background: #17a2b8;
                color: white;
                border: none;
                border-radius: 4px;
                cursor: pointer;
            `;
            scrollToMiddleBtn.onclick = () => {
                const chartBody = ganttInstance.elements.chartBody;
                const totalWidth = ganttInstance.timeScale.getTotalWidth();
                const scrollX = (totalWidth - chartBody.offsetWidth) / 2;
                chartBody.scrollLeft = scrollX;
            };

            const scrollToEndBtn = document.createElement('button');
            scrollToEndBtn.textContent = '滚动到末尾';
            scrollToEndBtn.style.cssText = scrollToMiddleBtn.style.cssText;
            scrollToEndBtn.onclick = () => {
                const chartBody = ganttInstance.elements.chartBody;
                const totalWidth = ganttInstance.timeScale.getTotalWidth();
                chartBody.scrollLeft = totalWidth;
            };

            testButtons.appendChild(scrollToMiddleBtn);
            testButtons.appendChild(scrollToEndBtn);
            document.body.appendChild(testButtons);

        } catch (error) {
            console.error('甘特图初始化失败:', error);
        }
    </script>
</body>
</html>

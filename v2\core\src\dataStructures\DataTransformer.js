import TimelineLayoutManager from './TimelineLayoutManager.js';
import TaskItem from './TaskItem.js';

/**
 * 数据转换器 - 增强版本
 * 支持多时间线布局的数据转换
 */
class DataTransformer {
  /**
   * 将旧版本数据转换为新版本格式
   * @param {Array} oldData - 旧版本数据
   * @returns {Array} 新版本数据
   */
  static migrateFromV1(oldData) {
    if (!Array.isArray(oldData)) return [];

    return oldData.map(task => {
      const newTask = { ...task };
      
      // 转换里程碑为时间线格式
      if (task.milestones && Array.isArray(task.milestones)) {
        newTask.timelines = [{
          name: '主要里程碑',
          type: 'default',
          visible: true,
          order: 0,
          yOffset: 0,
          height: 20,
          milestones: task.milestones.map(m => ({
            ...m,
            id: m.id || `milestone-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
          }))
        }];
        delete newTask.milestones;
      }

      // 设置默认的布局属性
      newTask.rowHeight = newTask.rowHeight || 40;
      newTask.timelineSpacing = newTask.timelineSpacing || 8;
      newTask.showTimelineLabels = newTask.showTimelineLabels !== false;

      return newTask;
    });
  }

  /**
   * 优化多时间线布局
   * @param {Array} data - 甘特图数据
   * @returns {Array} 优化后的数据
   */
  static optimizeTimelineLayout(data) {
    const layoutManager = new TimelineLayoutManager();
    
    return data.map(taskData => {
      const task = new TaskItem(taskData);
      
      // 自动优化时间线间距
      if (task.timelines.length > 1) {
        layoutManager.optimizeSpacing(task);
      }
      
      return task.toJSON();
    });
  }

  /**
   * 导出为标准 JSON 格式
   * @param {Array} data - 甘特图数据
   * @returns {object} 标准格式数据
   */
  static exportToStandard(data) {
    return {
      version: '2.1',
      exportTime: new Date().toISOString(),
      features: ['multiTimeline', 'yAxisLayout', 'milestoneChains'],
      tasks: data.map(task => {
        if (task instanceof TaskItem) {
          return task.toJSON();
        }
        return task;
      })
    };
  }

  /**
   * 从标准格式导入
   * @param {object} standardData - 标准格式数据
   * @returns {Array} 任务数组
   */
  static importFromStandard(standardData) {
    if (!standardData.tasks) {
      throw new Error('Invalid standard format: missing tasks');
    }

    // 检查版本兼容性
    if (standardData.version && parseFloat(standardData.version) < 2.0) {
      // 需要版本升级
      return DataTransformer.migrateFromV1(standardData.tasks).map(taskData => new TaskItem(taskData));
    }

    return standardData.tasks.map(taskData => new TaskItem(taskData));
  }

  /**
   * 导出时间线统计信息
   * @param {Array} data - 甘特图数据
   * @returns {object} 统计信息
   */
  static exportTimelineStatistics(data) {
    const stats = {
      totalTasks: data.length,
      totalTimelines: 0,
      totalMilestones: 0,
      timelineTypes: {},
      milestoneTypes: {},
      averageTimelinesPerTask: 0,
      averageMilestonesPerTimeline: 0,
      maxTaskHeight: 0,
      layoutComplexity: 'simple'
    };

    data.forEach(task => {
      if (task.timelines && task.timelines.length > 0) {
        stats.totalTimelines += task.timelines.length;
        
        task.timelines.forEach(timeline => {
          // 统计时间线类型
          stats.timelineTypes[timeline.type] = (stats.timelineTypes[timeline.type] || 0) + 1;
          
          if (timeline.milestones && timeline.milestones.length > 0) {
            stats.totalMilestones += timeline.milestones.length;
            
            timeline.milestones.forEach(milestone => {
              // 统计里程碑类型
              stats.milestoneTypes[milestone.type] = (stats.milestoneTypes[milestone.type] || 0) + 1;
            });
          }
        });
        
        // 计算任务高度
        const taskHeight = task.expandedHeight || task.rowHeight || 40;
        stats.maxTaskHeight = Math.max(stats.maxTaskHeight, taskHeight);
      }
    });

    // 计算平均值
    if (stats.totalTasks > 0) {
      stats.averageTimelinesPerTask = stats.totalTimelines / stats.totalTasks;
    }
    
    if (stats.totalTimelines > 0) {
      stats.averageMilestonesPerTimeline = stats.totalMilestones / stats.totalTimelines;
    }

    // 评估布局复杂度
    if (stats.averageTimelinesPerTask > 5 || stats.maxTaskHeight > 200) {
      stats.layoutComplexity = 'complex';
    } else if (stats.averageTimelinesPerTask > 2 || stats.maxTaskHeight > 80) {
      stats.layoutComplexity = 'medium';
    }

    return stats;
  }

  /**
   * 生成示例数据
   * @param {number} taskCount - 任务数量
   * @param {number} maxTimelines - 每个任务最大时间线数
   * @returns {Array} 示例数据
   */
  static generateSampleData(taskCount = 10, maxTimelines = 3) {
    const tasks = [];
    const today = new Date();
    
    for (let i = 0; i < taskCount; i++) {
      const startDate = new Date(today.getTime() + i * 7 * 24 * 60 * 60 * 1000);
      const endDate = new Date(startDate.getTime() + (10 + i * 3) * 24 * 60 * 60 * 1000);
      
      const timelineCount = Math.floor(Math.random() * maxTimelines) + 1;
      const timelines = [];
      
      for (let j = 0; j < timelineCount; j++) {
        const timelineTypes = ['technical', 'business', 'delivery'];
        const milestoneTypes = ['review', 'delivery', 'approval'];
        
        const milestones = [];
        const milestoneCount = Math.floor(Math.random() * 4) + 1;
        
        for (let k = 0; k < milestoneCount; k++) {
          const milestoneDate = new Date(
            startDate.getTime() + 
            (k + 1) * (endDate - startDate) / (milestoneCount + 1)
          );
          
          milestones.push({
            name: `里程碑 ${j + 1}-${k + 1}`,
            date: milestoneDate,
            type: milestoneTypes[Math.floor(Math.random() * milestoneTypes.length)],
            status: Math.random() > 0.3 ? 'completed' : 'pending'
          });
        }
        
        timelines.push({
          name: `时间线 ${j + 1}`,
          type: timelineTypes[j % timelineTypes.length],
          order: j,
          milestones: milestones
        });
      }
      
      tasks.push({
        name: `任务 ${i + 1}`,
        startDate: startDate,
        endDate: endDate,
        progress: Math.random(),
        status: Math.random() > 0.2 ? 'in-progress' : 'completed',
        priority: ['normal', 'high', 'critical'][Math.floor(Math.random() * 3)],
        assignee: `用户${i + 1}`,
        timelines: timelines
      });
    }
    
    return tasks;
  }
}

export default DataTransformer;
import DateUtils from '../utils/DateUtils.js';

/**
 * 里程碑数据结构
 */
class Milestone {
  constructor(data = {}) {
    this.id = data.id || this.generateId();
    this.name = data.name || '';
    this.date = DateUtils.parseDate(data.date);
    this.type = data.type || 'review'; // review, delivery, approval, warning, info
    this.status = data.status || 'pending'; // pending, completed, overdue, cancelled
    this.description = data.description || '';
    this.icon = data.icon || 'diamond'; // diamond, circle, triangle, star
    this.color = data.color || null; // 自定义颜色，null 时使用类型默认颜色
    this.priority = data.priority || 'normal'; // low, normal, high, critical
    
    // 扩展属性
    this.customFields = data.customFields || {};
    this.metadata = data.metadata || {};
    
    // 时间线关联
    this.timelineId = data.timelineId || null;
    
    this.validate();
  }

  validate() {
    if (!this.name) {
      throw new Error('Milestone name is required');
    }
    if (!this.date) {
      throw new Error('Milestone date is required');
    }
  }

  generateId() {
    return `milestone-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  // 获取里程碑颜色
  getColor() {
    if (this.color) return this.color;
    
    const colors = {
      review: '#FF9500',      // 橙色
      delivery: '#007AFF',    // 蓝色
      approval: '#34C759',    // 绿色
      warning: '#FF3B30',     // 红色
      info: '#5AC8FA'         // 浅蓝色
    };
    
    return colors[this.type] || colors.info;
  }

  // 检查是否逾期
  isOverdue() {
    if (this.status === 'completed') return false;
    return this.date < DateUtils.today();
  }

  // 克隆里程碑
  clone() {
    return new Milestone({
      ...this,
      id: this.generateId(),
      customFields: { ...this.customFields },
      metadata: { ...this.metadata }
    });
  }

  // 转换为 JSON
  toJSON() {
    return {
      id: this.id,
      name: this.name,
      date: DateUtils.formatDate(this.date),
      type: this.type,
      status: this.status,
      description: this.description,
      icon: this.icon,
      color: this.color,
      priority: this.priority,
      timelineId: this.timelineId,
      customFields: this.customFields,
      metadata: this.metadata
    };
  }
}

export default Milestone;
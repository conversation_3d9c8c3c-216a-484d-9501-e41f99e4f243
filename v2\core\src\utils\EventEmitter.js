/**
 * 事件发布订阅系统
 * 提供完整的事件管理和通信机制
 */
class EventEmitter {
  constructor() {
    // 事件监听器存储
    this.events = new Map();
    // 一次性事件监听器
    this.onceEvents = new Map();
    // 事件处理统计
    this.stats = {
      emitted: 0,
      listeners: 0,
    };
    // DEBUG 模式
    this.debug = false;
  }

  /**
   * 添加事件监听器
   * @param {string} event - 事件名称
   * @param {Function} listener - 监听函数
   * @param {object} options - 选项
   * @returns {EventEmitter} 返回自身，支持链式调用
   */
  on(event, listener, options = {}) {
    if (typeof listener !== "function") {
      throw new TypeError("Listener must be a function");
    }

    if (!this.events.has(event)) {
      this.events.set(event, []);
    }

    const listenerInfo = {
      fn: listener,
      context: options.context || null,
      priority: options.priority || 0,
      once: false,
      id: this.generateListenerId(),
    };

    const listeners = this.events.get(event);

    // 按优先级插入（高优先级在前）
    const insertIndex = listeners.findIndex(
      (l) => l.priority < listenerInfo.priority
    );
    if (insertIndex === -1) {
      listeners.push(listenerInfo);
    } else {
      listeners.splice(insertIndex, 0, listenerInfo);
    }

    this.stats.listeners++;

    if (this.debug) {
      console.log(
        `[EventEmitter] Added listener for event "${event}", total listeners: ${this.stats.listeners}`
      );
    }

    return this;
  }

  /**
   * 添加一次性事件监听器
   * @param {string} event - 事件名称
   * @param {Function} listener - 监听函数
   * @param {object} options - 选项
   * @returns {EventEmitter} 返回自身，支持链式调用
   */
  once(event, listener, options = {}) {
    const onceListener = (...args) => {
      this.off(event, onceListener);
      listener.apply(options.context || null, args);
    };

    // 保留原始监听器引用，用于 off 方法
    onceListener.originalListener = listener;

    return this.on(event, onceListener, { ...options, once: true });
  }

  /**
   * 移除事件监听器
   * @param {string} event - 事件名称
   * @param {Function} listener - 监听函数（可选）
   * @returns {EventEmitter} 返回自身，支持链式调用
   */
  off(event, listener) {
    if (!this.events.has(event)) {
      return this;
    }

    const listeners = this.events.get(event);

    if (!listener) {
      // 移除所有监听器
      this.stats.listeners -= listeners.length;
      this.events.delete(event);

      if (this.debug) {
        console.log(
          `[EventEmitter] Removed all listeners for event "${event}"`
        );
      }
    } else {
      // 移除特定监听器
      const index = listeners.findIndex(
        (l) => l.fn === listener || l.fn.originalListener === listener
      );

      if (index !== -1) {
        listeners.splice(index, 1);
        this.stats.listeners--;

        if (listeners.length === 0) {
          this.events.delete(event);
        }

        if (this.debug) {
          console.log(`[EventEmitter] Removed listener for event "${event}"`);
        }
      }
    }

    return this;
  }

  /**
   * 触发事件
   * @param {string} event - 事件名称
   * @param {...any} args - 事件参数
   * @returns {boolean} 是否有监听器处理了事件
   */
  emit(event, ...args) {
    this.stats.emitted++;

    if (this.debug) {
      console.log(`[EventEmitter] Emitting event "${event}" with args:`, args);
    }

    if (!this.events.has(event)) {
      if (this.debug) {
        console.log(`[EventEmitter] No listeners for event "${event}"`);
      }
      return false;
    }

    const listeners = this.events.get(event).slice(); // 复制数组，避免在执行过程中被修改
    let hasListeners = false;

    for (const listenerInfo of listeners) {
      try {
        hasListeners = true;

        if (listenerInfo.context) {
          listenerInfo.fn.apply(listenerInfo.context, args);
        } else {
          listenerInfo.fn(...args);
        }

        if (this.debug) {
          console.log(`[EventEmitter] Executed listener for event "${event}"`);
        }
      } catch (error) {
        console.error(
          `[EventEmitter] Error in listener for event "${event}":`,
          error
        );

        // 触发错误事件
        if (event !== "error") {
          this.emit("error", error, event, listenerInfo);
        }
      }
    }

    return hasListeners;
  }

  /**
   * 异步触发事件
   * @param {string} event - 事件名称
   * @param {...any} args - 事件参数
   * @returns {Promise<boolean>} 是否有监听器处理了事件
   */
  async emitAsync(event, ...args) {
    this.stats.emitted++;

    if (this.debug) {
      console.log(
        `[EventEmitter] Emitting async event "${event}" with args:`,
        args
      );
    }

    if (!this.events.has(event)) {
      return false;
    }

    const listeners = this.events.get(event).slice();
    let hasListeners = false;

    for (const listenerInfo of listeners) {
      try {
        hasListeners = true;

        const result = listenerInfo.context
          ? listenerInfo.fn.apply(listenerInfo.context, args)
          : listenerInfo.fn(...args);

        // 等待异步结果
        if (result && typeof result.then === "function") {
          await result;
        }

        if (this.debug) {
          console.log(
            `[EventEmitter] Executed async listener for event "${event}"`
          );
        }
      } catch (error) {
        console.error(
          `[EventEmitter] Error in async listener for event "${event}":`,
          error
        );

        if (event !== "error") {
          this.emit("error", error, event, listenerInfo);
        }
      }
    }

    return hasListeners;
  }

  /**
   * 获取事件的监听器数量
   * @param {string} event - 事件名称
   * @returns {number} 监听器数量
   */
  listenerCount(event) {
    if (!this.events.has(event)) {
      return 0;
    }
    return this.events.get(event).length;
  }

  /**
   * 获取事件列表
   * @returns {Array} 事件名称数组
   */
  eventNames() {
    return Array.from(this.events.keys());
  }

  /**
   * 获取事件的所有监听器
   * @param {string} event - 事件名称
   * @returns {Array} 监听器数组
   */
  listeners(event) {
    if (!this.events.has(event)) {
      return [];
    }
    return this.events.get(event).map((l) => l.fn);
  }

  /**
   * 移除所有事件监听器
   * @returns {EventEmitter} 返回自身，支持链式调用
   */
  removeAllListeners() {
    this.events.clear();
    this.onceEvents.clear();
    this.stats.listeners = 0;

    if (this.debug) {
      console.log("[EventEmitter] Removed all listeners");
    }

    return this;
  }

  /**
   * 设置最大监听器数量
   * @param {number} max - 最大数量
   */
  setMaxListeners(max) {
    this.maxListeners = max;
    return this;
  }

  /**
   * 获取最大监听器数量
   * @returns {number} 最大数量
   */
  getMaxListeners() {
    return this.maxListeners || 10;
  }

  /**
   * 启用/禁用调试模式
   * @param {boolean} enabled - 是否启用
   */
  setDebug(enabled) {
    this.debug = enabled;
    return this;
  }

  /**
   * 获取统计信息
   * @returns {object} 统计信息
   */
  getStats() {
    return {
      ...this.stats,
      activeEvents: this.events.size,
      totalListeners: this.stats.listeners,
    };
  }

  /**
   * 创建命名空间事件发射器
   * @param {string} namespace - 命名空间
   * @returns {object} 命名空间发射器
   */
  namespace(namespace) {
    const self = this;

    return {
      on(event, listener, options) {
        return self.on(`${namespace}:${event}`, listener, options);
      },

      once(event, listener, options) {
        return self.once(`${namespace}:${event}`, listener, options);
      },

      off(event, listener) {
        return self.off(`${namespace}:${event}`, listener);
      },

      emit(event, ...args) {
        return self.emit(`${namespace}:${event}`, ...args);
      },

      emitAsync(event, ...args) {
        return self.emitAsync(`${namespace}:${event}`, ...args);
      },
    };
  }

  /**
   * 等待事件触发
   * @param {string} event - 事件名称
   * @param {number} timeout - 超时时间（毫秒）
   * @returns {Promise} Promise对象
   */
  waitFor(event, timeout = 0) {
    return new Promise((resolve, reject) => {
      let timeoutId;

      const listener = (...args) => {
        if (timeoutId) {
          clearTimeout(timeoutId);
        }
        resolve(args);
      };

      this.once(event, listener);

      if (timeout > 0) {
        timeoutId = setTimeout(() => {
          this.off(event, listener);
          reject(new Error(`Event "${event}" timeout after ${timeout}ms`));
        }, timeout);
      }
    });
  }

  /**
   * 管道事件到另一个发射器
   * @param {string} event - 事件名称
   * @param {EventEmitter} target - 目标发射器
   * @param {string} targetEvent - 目标事件名称（可选）
   */
  pipe(event, target, targetEvent) {
    if (!(target instanceof EventEmitter)) {
      throw new TypeError("Target must be an EventEmitter instance");
    }

    const eventName = targetEvent || event;

    this.on(event, (...args) => {
      target.emit(eventName, ...args);
    });

    return this;
  }

  /**
   * 生成监听器ID
   * @returns {string} 唯一ID
   */
  generateListenerId() {
    return `listener-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 销毁事件发射器
   */
  destroy() {
    this.removeAllListeners();
    this.events = null;
    this.onceEvents = null;
    this.stats = null;

    if (this.debug) {
      console.log("[EventEmitter] Destroyed");
    }
  }
}

/**
 * 全局事件发射器单例
 */
EventEmitter.global = new EventEmitter();

/**
 * 事件常量定义
 */
EventEmitter.EVENTS = {
  // 生命周期事件
  INIT: "init",
  READY: "ready",
  DESTROY: "destroy",

  // 数据事件
  DATA_CHANGE: "dataChange",
  DATA_LOAD: "dataLoad",
  DATA_ERROR: "dataError",

  // 任务事件
  TASK_CLICK: "taskClick",
  TASK_SELECT: "taskSelect",
  TASK_UPDATE: "taskUpdate",
  TASK_ADD: "taskAdd",
  TASK_DELETE: "taskDelete",

  // 里程碑事件
  MILESTONE_CLICK: "milestoneClick",
  MILESTONE_UPDATE: "milestoneUpdate",

  // 视图事件
  VIEW_CHANGE: "viewChange",
  ZOOM_CHANGE: "zoomChange",
  SCROLL: "scroll",

  // 表格事件
  COLUMN_SORT: "columnSort",
  COLUMN_RESIZE: "columnResize",

  // 交互事件
  DRAG_START: "dragStart",
  DRAG_MOVE: "dragMove",
  DRAG_END: "dragEnd",

  // 渲染事件
  RENDER_START: "renderStart",
  RENDER_END: "renderEnd",
  RENDER_ERROR: "renderError",

  // 系统事件
  ERROR: "error",
  WARNING: "warning",
  RESIZE: "resize",
};

// 导出常用事件名称
EventEmitter.prototype.EVENTS = EventEmitter.EVENTS;

export default EventEmitter;
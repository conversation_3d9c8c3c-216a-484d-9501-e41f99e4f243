<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>项目甘特图</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            padding: 20px;
        }
        
        h1 {
            color: #333;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .controls {
            margin-bottom: 20px;
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            align-items: center;
        }
        
        .controls button {
            padding: 8px 16px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: #fff;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        
        .controls button:hover {
            background-color: #f0f0f0;
        }
        
        .controls button.active {
            background-color: #4CAF50;
            color: white;
            border-color: #4CAF50;
        }
        
        .controls input[type="file"] {
            display: none;
        }
        
        .controls label {
            padding: 8px 16px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: #fff;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        
        .controls label:hover {
            background-color: #f0f0f0;
        }
        
        #ganttChart {
            width: 100%;
            height: 600px;
            border: 1px solid #ddd;
            border-radius: 4px;
            overflow: auto;
            position: relative;
            background-color: #fafafa;
        }
        
        #ganttSvg {
            display: block;
            width: 100%;
            height: 100%;
        }
        
        .grid-line {
            stroke: #e0e0e0;
            stroke-width: 1;
        }
        
        .time-tick {
            stroke: #ccc;
            stroke-width: 1;
        }
        
        .time-label {
            font-size: 12px;
            fill: #666;
            text-anchor: middle;
        }
        
        .time-axis-line {
            stroke: #333;
            stroke-width: 2;
        }
        
        .task-label {
            font-size: 12px;
            fill: #333;
            text-anchor: start;
        }
        
        .task-bar {
            cursor: pointer;
        }
        
        .time-line {
            stroke: #666;
            stroke-width: 2;
            fill: none;
        }
        
        .time-point {
            cursor: pointer;
        }
        
        .time-point.start {
            fill: #28a745;
        }
        
        .time-point.checkpoint {
            fill: #0366d6;
        }
        
        .time-point.end {
            fill: #dc3545;
        }
        
        .time-point.milestone {
            fill: #f66a0a;
        }
        
        .dependency-line {
            stroke: #586069;
            stroke-width: 2;
            fill: none;
            marker-end: url(#arrowhead);
        }
        
        .tooltip {
            position: absolute;
            background-color: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 12px;
            pointer-events: none;
            z-index: 1000;
            display: none;
        }
        
        .loading {
            display: none;
            text-align: center;
            padding: 20px;
            color: #666;
        }
        
        .error {
            display: none;
            color: #d32f2f;
            padding: 10px;
            background-color: #ffebee;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        
        .info {
            margin-bottom: 20px;
            padding: 10px;
            background-color: #e3f2fd;
            border-radius: 4px;
            color: #1976d2;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>项目甘特图</h1>
        
        <div class="info">
            支持导入CSV格式的项目数据。CSV文件应包含以下列：任务名称、开始日期、结束日期、负责人、进度等。
        </div>
        
        <div class="error" id="errorMessage"></div>
        
        <div class="controls">
            <label for="fileInput">
                导入CSV文件
                <input type="file" id="fileInput" accept=".csv">
            </label>
            <label for="jsonFileInput">
                导入JSON文件
                <input type="file" id="jsonFileInput" accept=".json">
            </label>
            <select id="sampleDataSelect" aria-label="选择示例数据">
                <option value="">选择示例数据</option>
                <option value="builtin">内置示例</option>
                <option value="data/sample-project.json">软件开发项目</option>
                <option value="data/simple-project.json">简单项目</option>
            </select>
            <button id="exportBtn">导出图片</button>
            <button id="zoomInBtn">放大</button>
            <button id="zoomOutBtn">缩小</button>
            <button id="fitBtn">适应屏幕</button>
            <button id="todayBtn">今天</button>
            <button id="viewMonthBtn" class="active">月视图</button>
            <button id="viewWeekBtn">周视图</button>
            <button id="viewDayBtn">日视图</button>
            <button id="toggleMilestonesBtn">显示里程碑</button>
            <button id="toggleDependenciesBtn">显示依赖</button>
        </div>
        
        <div class="loading" id="loading">正在加载...</div>
        
        <div id="ganttChart">
            <svg id="ganttSvg" width="1200" height="600"></svg>
        </div>
        
        <div class="tooltip" id="tooltip"></div>
    </div>
    
    <!-- 引入外部的GanttChart类 -->
    <script src="GanttChart.js"></script>
    
    <script>
        // 初始化甘特图
        const gantt = new GanttChart('ganttChart');
        
        // 工具提示
        const tooltip = document.getElementById('tooltip');
        const chartContainer = document.getElementById('ganttChart');
        
        // 示例数据选择
        document.getElementById('sampleDataSelect').addEventListener('change', async (e) => {
            const selectedValue = e.target.value;
            if (!selectedValue) return;
            
            const loading = document.getElementById('loading');
            const errorMessage = document.getElementById('errorMessage');
            
            loading.style.display = 'block';
            errorMessage.style.display = 'none';
            
            try {
                if (selectedValue === 'builtin') {
                    gantt.loadBuiltInSampleData();
                    gantt.render();
                } else {
                    const result = await gantt.loadFromJSONFile(selectedValue);
                    if (!result.success) {
                        throw new Error(result.error);
                    }
                    // 显示项目信息
                    if (result.metadata) {
                        console.log('加载项目:', result.metadata.name);
                        console.log('项目描述:', result.metadata.description);
                    }
                }
            } catch (error) {
                errorMessage.textContent = '加载数据失败: ' + error.message;
                errorMessage.style.display = 'block';
            } finally {
                loading.style.display = 'none';
            }
        });
        
        // JSON文件导入
        document.getElementById('jsonFileInput').addEventListener('change', async (e) => {
            const file = e.target.files[0];
            if (!file) return;
            
            const loading = document.getElementById('loading');
            const errorMessage = document.getElementById('errorMessage');
            
            loading.style.display = 'block';
            errorMessage.style.display = 'none';
            
            try {
                const text = await file.text();
                const data = JSON.parse(text);
                
                const result = gantt.loadFromJSONObject(data);
                if (!result.success) {
                    throw new Error(result.error);
                }
                
                // 显示项目信息
                if (result.metadata) {
                    console.log('加载项目:', result.metadata.name);
                    console.log('项目描述:', result.metadata.description);
                }
            } catch (error) {
                errorMessage.textContent = '导入JSON失败: ' + error.message;
                errorMessage.style.display = 'block';
            } finally {
                loading.style.display = 'none';
            }
        });
        
        // 文件导入
        document.getElementById('fileInput').addEventListener('change', async (e) => {
            const file = e.target.files[0];
            if (!file) return;
            
            const loading = document.getElementById('loading');
            const errorMessage = document.getElementById('errorMessage');
            
            loading.style.display = 'block';
            errorMessage.style.display = 'none';
            
            try {
                const text = await file.text();
                const lines = text.split('\n');
                const headers = lines[0].split(',').map(h => h.trim());
                
                const tasks = [];
                for (let i = 1; i < lines.length; i++) {
                    if (lines[i].trim() === '') continue;
                    
                    const values = lines[i].split(',').map(v => v.trim());
                    const task = {
                        id: `imported_${i}`,
                        name: values[0],
                        startDate: new Date(values[1]),
                        endDate: new Date(values[2]),
                        assignee: values[3] || '未分配',
                        progress: parseInt(values[4]) || 0,
                        status: values[5] || 'notStarted',
                        dependencies: values[6] ? values[6].split(';') : [],
                        priority: values[7] || 'medium'
                    };
                    
                    if (!isNaN(task.startDate.getTime()) && !isNaN(task.endDate.getTime())) {
                        tasks.push(task);
                    }
                }
                
                if (tasks.length > 0) {
                    gantt.setData(tasks);
                } else {
                    throw new Error('没有找到有效的任务数据');
                }
            } catch (error) {
                errorMessage.textContent = '导入失败: ' + error.message;
                errorMessage.style.display = 'block';
            } finally {
                loading.style.display = 'none';
            }
        });
        
        // 导出功能
        document.getElementById('exportBtn').addEventListener('click', () => {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            const chartElement = document.getElementById('ganttChart');
            
            // 这里需要使用html2canvas等库来实现截图功能
            // 简化起见，这里只是提示
            alert('导出功能需要额外的库支持（如html2canvas）');
        });
        
        // 缩放控制
        let currentScale = 1;
        
        document.getElementById('zoomInBtn').addEventListener('click', () => {
            currentScale *= 1.2;
            applyZoom();
        });
        
        document.getElementById('zoomOutBtn').addEventListener('click', () => {
            currentScale *= 0.8;
            applyZoom();
        });
        
        document.getElementById('fitBtn').addEventListener('click', () => {
            currentScale = 1;
            applyZoom();
            // 滚动到开始位置
            chartContainer.scrollLeft = 0;
            chartContainer.scrollTop = 0;
        });
        
        function applyZoom() {
            const svgElement = chartContainer.querySelector('svg');
            if (svgElement) {
                svgElement.style.transform = `scale(${currentScale})`;
                svgElement.style.transformOrigin = 'top left';
            }
        }
        
        // 今天按钮
        document.getElementById('todayBtn').addEventListener('click', () => {
            const today = new Date();
            const dayWidth = 30; // 假设每天的宽度
            const scrollPosition = Math.floor((today - gantt.getStartDate()) / (1000 * 60 * 60 * 24)) * dayWidth - chartContainer.clientWidth / 2;
            chartContainer.scrollLeft = Math.max(0, scrollPosition);
        });
        
        // 视图切换
        const viewButtons = {
            month: document.getElementById('viewMonthBtn'),
            week: document.getElementById('viewWeekBtn'),
            day: document.getElementById('viewDayBtn')
        };
        
        Object.entries(viewButtons).forEach(([view, button]) => {
            button.addEventListener('click', () => {
                // 移除所有active类
                Object.values(viewButtons).forEach(btn => btn.classList.remove('active'));
                // 添加active类到当前按钮
                button.classList.add('active');
                // 更新视图
                gantt.config.viewMode = view;
                gantt.updatePixelPerDay();
                gantt.render();
            });
        });
        
        // 里程碑切换
        document.getElementById('toggleMilestonesBtn').addEventListener('click', () => {
            gantt.config.showMilestones = !gantt.config.showMilestones;
            gantt.render();
        });
        
        // 依赖关系切换
        let showDependencies = true;
        document.getElementById('toggleDependenciesBtn').addEventListener('click', () => {
            showDependencies = !showDependencies;
            // 这里需要添加依赖关系的显示/隐藏逻辑
            gantt.render();
        });
    </script>
</body>
</html>
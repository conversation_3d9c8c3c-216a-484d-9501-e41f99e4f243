<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Step3: 智能周几显示演示</title>
    <link rel="stylesheet" href="../../src/styles/gantt.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }

        .header h1 {
            margin: 0;
            font-size: 24px;
        }

        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
        }

        .demo-section {
            padding: 20px;
            border-bottom: 1px solid #e5e7eb;
        }

        .demo-section h3 {
            margin: 0 0 15px 0;
            color: #374151;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .demo-controls {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .control-card {
            background: #f9fafb;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
        }

        .control-card h4 {
            margin: 0 0 10px 0;
            color: #374151;
            font-size: 14px;
        }

        .slider-group {
            margin: 10px 0;
        }

        .slider-group label {
            display: block;
            font-size: 12px;
            color: #6b7280;
            margin-bottom: 5px;
        }

        .slider-group input[type="range"] {
            width: 100%;
            margin-bottom: 5px;
        }

        .slider-value {
            font-size: 12px;
            color: #374151;
            font-weight: 600;
            text-align: center;
        }

        .gantt-container {
            height: 400px;
            position: relative;
            border: 2px solid #10b981;
            margin: 20px 0;
        }

        .status-panel {
            background: #f0fdf4;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #10b981;
            margin: 20px 0;
        }

        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-top: 10px;
        }

        .status-item {
            text-align: center;
        }

        .status-label {
            font-size: 11px;
            color: #6b7280;
            text-transform: uppercase;
            margin-bottom: 5px;
        }

        .status-value {
            font-size: 16px;
            font-weight: 600;
            color: #059669;
            font-family: monospace;
        }

        .demo-explanation {
            background: #fef3c7;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #f59e0b;
            margin: 20px 0;
        }

        .demo-explanation h4 {
            margin: 0 0 10px 0;
            color: #92400e;
        }

        .demo-explanation p {
            margin: 0;
            color: #78350f;
            font-size: 14px;
            line-height: 1.5;
        }

        .format-showcase {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
            margin: 20px 0;
        }

        .format-card {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border: 2px solid #e5e7eb;
            text-align: center;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .format-card:hover {
            border-color: #10b981;
            transform: translateY(-2px);
        }

        .format-card.active {
            border-color: #10b981;
            background: #f0fdf4;
        }

        .format-card h4 {
            margin: 0 0 10px 0;
            color: #374151;
        }

        .format-example {
            font-size: 18px;
            font-weight: 600;
            color: #10b981;
            margin: 10px 0;
        }

        .format-description {
            font-size: 12px;
            color: #6b7280;
        }

        /* 增强周几标签的可见性 */
        .timeline-scale.bottom-row .weekday-label {
            background: rgba(16, 185, 129, 0.1) !important;
            border-radius: 2px;
            padding: 1px 2px;
        }

        .timeline-scale.bottom-row .date-number {
            background: rgba(16, 185, 129, 0.05) !important;
            border-radius: 2px;
            padding: 1px 2px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧠 智能周几显示演示</h1>
            <p>展示周几标签的智能显示、自动隐藏和字号优化功能</p>
        </div>

        <div class="demo-section">
            <h3>🎛️ 实时控制面板</h3>
            
            <div class="demo-controls">
                <div class="control-card">
                    <h4>像素密度控制</h4>
                    <div class="slider-group">
                        <label>像素密度 (pixelsPerDay)</label>
                        <input type="range" id="pixelsPerDaySlider" min="15" max="80" value="30">
                        <div class="slider-value" id="pixelsPerDayValue">30px</div>
                    </div>
                </div>

                <div class="control-card">
                    <h4>缩放控制</h4>
                    <div class="slider-group">
                        <label>缩放级别</label>
                        <input type="range" id="zoomSlider" min="0.3" max="2.0" step="0.1" value="1.0">
                        <div class="slider-value" id="zoomValue">100%</div>
                    </div>
                </div>

                <div class="control-card">
                    <h4>显示阈值</h4>
                    <div class="slider-group">
                        <label>最小显示宽度</label>
                        <input type="range" id="thresholdSlider" min="25" max="60" value="45">
                        <div class="slider-value" id="thresholdValue">45px</div>
                    </div>
                </div>
            </div>

            <div class="status-panel">
                <h4>📊 实时状态监控</h4>
                <div class="status-grid">
                    <div class="status-item">
                        <div class="status-label">实际刻度宽度</div>
                        <div class="status-value" id="actualWidth">-</div>
                    </div>
                    <div class="status-item">
                        <div class="status-label">显示周几数量</div>
                        <div class="status-value" id="visibleWeekdays">-</div>
                    </div>
                    <div class="status-item">
                        <div class="status-label">隐藏周几数量</div>
                        <div class="status-value" id="hiddenWeekdays">-</div>
                    </div>
                    <div class="status-item">
                        <div class="status-label">显示比例</div>
                        <div class="status-value" id="visibilityRatio">-</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h3>📝 周几格式选择</h3>
            
            <div class="format-showcase">
                <div class="format-card active" data-format="short">
                    <h4>中文简写</h4>
                    <div class="format-example">周一</div>
                    <div class="format-description">需要宽度: 45px</div>
                </div>
                <div class="format-card" data-format="min">
                    <h4>中文最简</h4>
                    <div class="format-example">一</div>
                    <div class="format-description">需要宽度: 35px</div>
                </div>
                <div class="format-card" data-format="abbr">
                    <h4>英文缩写</h4>
                    <div class="format-example">Mon</div>
                    <div class="format-description">需要宽度: 40px</div>
                </div>
            </div>
        </div>

        <div class="gantt-container" id="ganttContainer"></div>

        <div class="demo-explanation">
            <h4>💡 智能显示机制说明</h4>
            <p>
                系统会根据刻度的实际宽度智能决定是否显示周几标签。当宽度不足时，周几会自动隐藏，确保界面整洁。
                日期数字使用较大字号保证可读性，周几使用较小字号节省空间。不同格式的周几有不同的最小宽度要求。
            </p>
        </div>
    </div>

    <script type="module">
        import GanttChart from '../../src/GanttChart.js';
        import DateUtils from '../../src/utils/DateUtils.js';

        // 生成测试数据
        function generateTestData() {
            const tasks = [];
            const startDate = new Date('2024-01-01');
            
            for (let i = 0; i < 8; i++) {
                const taskStart = DateUtils.addDays(startDate, i * 4);
                const duration = 6 + Math.random() * 8;
                const taskEnd = DateUtils.addDays(taskStart, duration);
                
                tasks.push({
                    id: `smart-task-${i}`,
                    name: `智能显示任务 ${i + 1}`,
                    startDate: taskStart,
                    endDate: taskEnd,
                    progress: Math.random()
                });
            }
            
            return tasks;
        }

        // 初始化甘特图
        let ganttInstance = null;
        let currentFormat = 'short';

        function initializeGantt() {
            const container = document.getElementById('ganttContainer');
            container.innerHTML = '';
            
            const testData = generateTestData();
            const pixelsPerDay = parseInt(document.getElementById('pixelsPerDaySlider').value);
            
            ganttInstance = new GanttChart('ganttContainer', {
                data: testData,
                viewMode: 'day',
                pixelsPerDay: pixelsPerDay,
                timeline: {
                    showWeekday: true,
                    adaptiveWidth: true,
                    minScaleWidth: 20,
                    maxScaleWidth: 100,
                    weekdayFormat: currentFormat
                },
                taskList: {
                    columns: [
                        { key: 'name', title: '任务名称', width: 180 },
                        { key: 'progress', title: '进度', width: 80 }
                    ]
                }
            });

            ganttInstance.on('ready', () => {
                updateStatus();
            });
        }

        // 更新状态显示
        function updateStatus() {
            if (!ganttInstance || !ganttInstance.timeScale) return;

            setTimeout(() => {
                const scales = ganttInstance.timeScale.getAllScales();
                const scaleWidth = scales.length > 1 ? (scales[1].x - scales[0].x) : 30;
                
                const weekdayElements = document.querySelectorAll('.timeline-scale.bottom-row .weekday-label');
                const totalScales = scales.length;
                const visibleWeekdays = weekdayElements.length;
                const hiddenWeekdays = totalScales - visibleWeekdays;
                const visibilityRatio = totalScales > 0 ? (visibleWeekdays / totalScales * 100).toFixed(1) : 0;

                document.getElementById('actualWidth').textContent = `${scaleWidth.toFixed(1)}px`;
                document.getElementById('visibleWeekdays').textContent = visibleWeekdays;
                document.getElementById('hiddenWeekdays').textContent = hiddenWeekdays;
                document.getElementById('visibilityRatio').textContent = `${visibilityRatio}%`;
            }, 100);
        }

        // 事件绑定
        document.getElementById('pixelsPerDaySlider').addEventListener('input', (e) => {
            const value = e.target.value;
            document.getElementById('pixelsPerDayValue').textContent = `${value}px`;
            initializeGantt();
        });

        document.getElementById('zoomSlider').addEventListener('input', (e) => {
            const zoomLevel = parseFloat(e.target.value);
            document.getElementById('zoomValue').textContent = `${Math.round(zoomLevel * 100)}%`;
            if (ganttInstance) {
                ganttInstance.setZoomLevel(zoomLevel);
                updateStatus();
            }
        });

        document.getElementById('thresholdSlider').addEventListener('input', (e) => {
            const value = e.target.value;
            document.getElementById('thresholdValue').textContent = `${value}px`;
            
            // 更新阈值（这里只是演示，实际需要重新初始化）
            if (ganttInstance) {
                ganttInstance.options.timeline.minScaleWidth = parseInt(value);
                initializeGantt();
            }
        });

        // 格式选择
        document.querySelectorAll('.format-card').forEach(card => {
            card.addEventListener('click', () => {
                document.querySelectorAll('.format-card').forEach(c => c.classList.remove('active'));
                card.classList.add('active');
                
                currentFormat = card.dataset.format;
                if (ganttInstance) {
                    ganttInstance.options.timeline.weekdayFormat = currentFormat;
                    initializeGantt();
                }
            });
        });

        // 初始化
        document.addEventListener('DOMContentLoaded', () => {
            console.log('页面加载完成，初始化智能周几显示演示...');
            initializeGantt();
        });
    </script>
</body>
</html>

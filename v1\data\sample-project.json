{"metadata": {"name": "软件开发项目示例", "description": "一个完整的软件开发项目甘特图示例，包含需求分析、设计、开发、测试、部署等各个阶段", "version": "1.0.0", "created": "2024-01-01", "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "project": {"name": "企业级Web应用开发", "startDate": "2024-01-01", "endDate": "2024-02-05", "description": "开发一个企业级Web应用，包含用户管理、业务功能、第三方集成等功能"}, "tasks": [{"id": "task1", "name": "项目启动", "startDate": "2024-01-01", "endDate": "2024-01-06", "progress": 100, "status": "completed", "dependencies": [], "assignee": "项目经理", "priority": "high", "timePoints": [{"id": "p1", "date": "2024-01-01", "name": "项目启动会议", "type": "start"}, {"id": "p2", "date": "2024-01-03", "name": "团队组建", "type": "checkpoint"}, {"id": "p3", "date": "2024-01-05", "name": "项目章程确认", "type": "checkpoint"}, {"id": "p4", "date": "2024-01-06", "name": "启动完成", "type": "end"}]}, {"id": "task2", "name": "需求调研", "startDate": "2024-01-03", "endDate": "2024-01-11", "progress": 100, "status": "completed", "dependencies": ["task1"], "assignee": "业务分析师", "priority": "high", "timePoints": [{"id": "p5", "date": "2024-01-03", "name": "用户访谈", "type": "start"}, {"id": "p6", "date": "2024-01-06", "name": "竞品分析", "type": "checkpoint"}, {"id": "p7", "date": "2024-01-09", "name": "需求整理", "type": "checkpoint"}, {"id": "p8", "date": "2024-01-11", "name": "调研报告", "type": "end"}]}, {"id": "task3", "name": "需求分析", "startDate": "2024-01-09", "endDate": "2024-01-16", "progress": 100, "status": "completed", "dependencies": ["task2"], "assignee": "系统分析师", "priority": "high", "timePoints": [{"id": "p9", "date": "2024-01-09", "name": "功能需求分析", "type": "start"}, {"id": "p10", "date": "2024-01-12", "name": "非功能需求分析", "type": "checkpoint"}, {"id": "p11", "date": "2024-01-14", "name": "需求评审", "type": "checkpoint"}, {"id": "p12", "date": "2024-01-16", "name": "需求确认", "type": "end"}]}, {"id": "task4", "name": "系统架构设计", "startDate": "2024-01-13", "endDate": "2024-01-21", "progress": 100, "status": "completed", "dependencies": ["task3"], "assignee": "架构师", "priority": "high", "timePoints": [{"id": "p13", "date": "2024-01-13", "name": "技术选型", "type": "start"}, {"id": "p14", "date": "2024-01-16", "name": "架构设计", "type": "checkpoint"}, {"id": "p15", "date": "2024-01-19", "name": "架构评审", "type": "checkpoint"}, {"id": "p16", "date": "2024-01-21", "name": "架构确认", "type": "end"}]}, {"id": "task5", "name": "数据库设计", "startDate": "2024-01-15", "endDate": "2024-01-23", "progress": 100, "status": "completed", "dependencies": ["task4"], "assignee": "数据库工程师", "priority": "medium", "timePoints": [{"id": "p17", "date": "2024-01-15", "name": "数据建模", "type": "start"}, {"id": "p18", "date": "2024-01-18", "name": "表结构设计", "type": "checkpoint"}, {"id": "p19", "date": "2024-01-21", "name": "索引优化", "type": "checkpoint"}, {"id": "p20", "date": "2024-01-23", "name": "数据库设计完成", "type": "end"}]}, {"id": "task6", "name": "UI/UX设计", "startDate": "2024-01-17", "endDate": "2024-01-26", "progress": 80, "status": "inProgress", "dependencies": ["task4"], "assignee": "UI设计师", "priority": "medium", "timePoints": [{"id": "p21", "date": "2024-01-17", "name": "用户研究", "type": "start"}, {"id": "p22", "date": "2024-01-20", "name": "原型设计", "type": "checkpoint"}, {"id": "p23", "date": "2024-01-23", "name": "视觉设计", "type": "checkpoint"}, {"id": "p24", "date": "2024-01-26", "name": "设计交付", "type": "end"}]}, {"id": "task7", "name": "前端环境搭建", "startDate": "2024-01-19", "endDate": "2024-01-23", "progress": 100, "status": "completed", "dependencies": ["task4"], "assignee": "前端工程师", "priority": "medium", "timePoints": [{"id": "p25", "date": "2024-01-19", "name": "技术栈选择", "type": "start"}, {"id": "p26", "date": "2024-01-21", "name": "环境配置", "type": "checkpoint"}, {"id": "p27", "date": "2024-01-23", "name": "环境就绪", "type": "end"}]}, {"id": "task8", "name": "用户认证模块", "startDate": "2024-01-21", "endDate": "2024-01-29", "progress": 90, "status": "inProgress", "dependencies": ["task7"], "assignee": "前端工程师", "priority": "high", "timePoints": [{"id": "p28", "date": "2024-01-21", "name": "登录页面", "type": "start"}, {"id": "p29", "date": "2024-01-24", "name": "注册页面", "type": "checkpoint"}, {"id": "p30", "date": "2024-01-27", "name": "权限控制", "type": "checkpoint"}, {"id": "p31", "date": "2024-01-29", "name": "认证完成", "type": "end"}]}, {"id": "task9", "name": "核心功能模块", "startDate": "2024-01-23", "endDate": "2024-02-06", "progress": 60, "status": "inProgress", "dependencies": ["task7", "task8"], "assignee": "前端工程师", "priority": "high", "timePoints": [{"id": "p32", "date": "2024-01-23", "name": "组件开发", "type": "start"}, {"id": "p33", "date": "2024-01-26", "name": "页面开发", "type": "checkpoint"}, {"id": "p34", "date": "2024-01-29", "name": "功能集成", "type": "checkpoint"}, {"id": "p35", "date": "2024-02-06", "name": "前端完成", "type": "end"}]}, {"id": "task10", "name": "后端环境搭建", "startDate": "2024-01-19", "endDate": "2024-01-23", "progress": 100, "status": "completed", "dependencies": ["task4"], "assignee": "后端工程师", "priority": "medium", "timePoints": [{"id": "p36", "date": "2024-01-19", "name": "框架选择", "type": "start"}, {"id": "p37", "date": "2024-01-21", "name": "环境配置", "type": "checkpoint"}, {"id": "p38", "date": "2024-01-23", "name": "环境就绪", "type": "end"}]}, {"id": "task11", "name": "用户管理API", "startDate": "2024-01-21", "endDate": "2024-01-28", "progress": 85, "status": "inProgress", "dependencies": ["task10", "task5"], "assignee": "后端工程师", "priority": "high", "timePoints": [{"id": "p39", "date": "2024-01-21", "name": "用户CRUD", "type": "start"}, {"id": "p40", "date": "2024-01-24", "name": "权限管理", "type": "checkpoint"}, {"id": "p41", "date": "2024-01-26", "name": "API测试", "type": "checkpoint"}, {"id": "p42", "date": "2024-01-28", "name": "用户API完成", "type": "end"}]}, {"id": "task12", "name": "业务逻辑API", "startDate": "2024-01-23", "endDate": "2024-02-04", "progress": 70, "status": "inProgress", "dependencies": ["task10", "task5"], "assignee": "后端工程师", "priority": "high", "timePoints": [{"id": "p43", "date": "2024-01-23", "name": "核心业务API", "type": "start"}, {"id": "p44", "date": "2024-01-26", "name": "数据处理", "type": "checkpoint"}, {"id": "p45", "date": "2024-01-29", "name": "业务逻辑", "type": "checkpoint"}, {"id": "p46", "date": "2024-02-04", "name": "业务API完成", "type": "end"}]}, {"id": "task13", "name": "第三方集成", "startDate": "2024-01-25", "endDate": "2024-02-09", "progress": 40, "status": "inProgress", "dependencies": ["task10"], "assignee": "后端工程师", "priority": "medium", "timePoints": [{"id": "p47", "date": "2024-01-25", "name": "支付集成", "type": "start"}, {"id": "p48", "date": "2024-01-28", "name": "邮件服务", "type": "checkpoint"}, {"id": "p49", "date": "2024-02-03", "name": "短信服务", "type": "checkpoint"}, {"id": "p50", "date": "2024-02-09", "name": "集成完成", "type": "end"}]}, {"id": "task14", "name": "单元测试", "startDate": "2024-01-26", "endDate": "2024-02-11", "progress": 30, "status": "inProgress", "dependencies": ["task8", "task11"], "assignee": "测试工程师", "priority": "medium", "timePoints": [{"id": "p51", "date": "2024-01-26", "name": "测试计划", "type": "start"}, {"id": "p52", "date": "2024-01-29", "name": "前端测试", "type": "checkpoint"}, {"id": "p53", "date": "2024-02-03", "name": "后端测试", "type": "checkpoint"}, {"id": "p54", "date": "2024-02-11", "name": "单元测试完成", "type": "end"}]}, {"id": "task15", "name": "集成测试", "startDate": "2024-02-06", "endDate": "2024-02-19", "progress": 0, "status": "notStarted", "dependencies": ["task9", "task12", "task14"], "assignee": "测试工程师", "priority": "high", "timePoints": [{"id": "p55", "date": "2024-02-06", "name": "接口测试", "type": "start"}, {"id": "p56", "date": "2024-02-09", "name": "端到端测试", "type": "checkpoint"}, {"id": "p57", "date": "2024-02-13", "name": "性能测试", "type": "checkpoint"}, {"id": "p58", "date": "2024-02-19", "name": "集成测试完成", "type": "end"}]}, {"id": "task16", "name": "用户验收测试", "startDate": "2024-02-16", "endDate": "2024-02-26", "progress": 0, "status": "notStarted", "dependencies": ["task15"], "assignee": "产品经理", "priority": "high", "timePoints": [{"id": "p59", "date": "2024-02-16", "name": "UAT准备", "type": "start"}, {"id": "p60", "date": "2024-02-19", "name": "用户测试", "type": "checkpoint"}, {"id": "p61", "date": "2024-02-23", "name": "问题修复", "type": "checkpoint"}, {"id": "p62", "date": "2024-02-26", "name": "UAT完成", "type": "end"}]}, {"id": "task17", "name": "生产环境准备", "startDate": "2024-02-21", "endDate": "2024-02-29", "progress": 0, "status": "notStarted", "dependencies": ["task16"], "assignee": "运维工程师", "priority": "high", "timePoints": [{"id": "p63", "date": "2024-02-21", "name": "服务器配置", "type": "start"}, {"id": "p64", "date": "2024-02-24", "name": "数据库部署", "type": "checkpoint"}, {"id": "p65", "date": "2024-02-27", "name": "环境测试", "type": "checkpoint"}, {"id": "p66", "date": "2024-02-29", "name": "环境就绪", "type": "end"}]}, {"id": "task18", "name": "应用部署", "startDate": "2024-02-26", "endDate": "2024-03-01", "progress": 0, "status": "notStarted", "dependencies": ["task17"], "assignee": "运维工程师", "priority": "high", "timePoints": [{"id": "p67", "date": "2024-02-26", "name": "代码部署", "type": "start"}, {"id": "p68", "date": "2024-02-28", "name": "配置更新", "type": "checkpoint"}, {"id": "p69", "date": "2024-03-01", "name": "部署完成", "type": "end"}]}, {"id": "task19", "name": "技术文档编写", "startDate": "2024-01-23", "endDate": "2024-02-16", "progress": 50, "status": "inProgress", "dependencies": ["task4"], "assignee": "技术文档工程师", "priority": "medium", "timePoints": [{"id": "p70", "date": "2024-01-23", "name": "API文档", "type": "start"}, {"id": "p71", "date": "2024-01-28", "name": "用户手册", "type": "checkpoint"}, {"id": "p72", "date": "2024-02-06", "name": "部署文档", "type": "checkpoint"}, {"id": "p73", "date": "2024-02-16", "name": "文档完成", "type": "end"}]}, {"id": "task20", "name": "用户培训", "startDate": "2024-02-29", "endDate": "2024-03-06", "progress": 0, "status": "notStarted", "dependencies": ["task18", "task19"], "assignee": "培训师", "priority": "medium", "timePoints": [{"id": "p74", "date": "2024-02-29", "name": "培训材料准备", "type": "start"}, {"id": "p75", "date": "2024-03-01", "name": "管理员培训", "type": "checkpoint"}, {"id": "p76", "date": "2024-03-04", "name": "用户培训", "type": "checkpoint"}, {"id": "p77", "date": "2024-03-06", "name": "培训完成", "type": "end"}]}, {"id": "milestone1", "name": "需求确认里程碑", "startDate": "2024-01-16", "endDate": "2024-01-16", "progress": 100, "status": "completed", "dependencies": ["task3"], "assignee": "项目经理", "priority": "high", "isMilestone": true, "timePoints": [{"id": "m1", "date": "2024-01-16", "name": "需求确认", "type": "milestone"}]}, {"id": "milestone2", "name": "设计完成里程碑", "startDate": "2024-01-26", "endDate": "2024-01-26", "progress": 80, "status": "inProgress", "dependencies": ["task4", "task5", "task6"], "assignee": "项目经理", "priority": "high", "isMilestone": true, "timePoints": [{"id": "m2", "date": "2024-01-26", "name": "设计完成", "type": "milestone"}]}, {"id": "milestone3", "name": "开发完成里程碑", "startDate": "2024-02-06", "endDate": "2024-02-06", "progress": 0, "status": "notStarted", "dependencies": ["task9", "task12", "task13"], "assignee": "项目经理", "priority": "high", "isMilestone": true, "timePoints": [{"id": "m3", "date": "2024-02-06", "name": "开发完成", "type": "milestone"}]}, {"id": "milestone4", "name": "测试完成里程碑", "startDate": "2024-02-26", "endDate": "2024-02-26", "progress": 0, "status": "notStarted", "dependencies": ["task16"], "assignee": "项目经理", "priority": "high", "isMilestone": true, "timePoints": [{"id": "m4", "date": "2024-02-26", "name": "测试完成", "type": "milestone"}]}, {"id": "milestone5", "name": "项目上线里程碑", "startDate": "2024-03-01", "endDate": "2024-03-01", "progress": 0, "status": "notStarted", "dependencies": ["task18"], "assignee": "项目经理", "priority": "high", "isMilestone": true, "timePoints": [{"id": "m5", "date": "2024-03-01", "name": "项目上线", "type": "milestone"}]}]}
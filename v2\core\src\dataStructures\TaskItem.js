import DateUtils from "../utils/DateUtils.js";
import Timeline from "./Timeline.js";

/**
 * 任务数据结构 - 增强版本
 * 增加多时间线Y轴布局管理功能
 */
class TaskItem {
  constructor(data = {}) {
    this.id = data.id || this.generateId();
    this.name = data.name || '';
    this.description = data.description || '';
    
    // 时间相关
    this.startDate = DateUtils.parseDate(data.startDate);
    this.endDate = DateUtils.parseDate(data.endDate);
    this.duration = data.duration || 0; // 天数
    this.progress = Math.max(0, Math.min(1, data.progress || 0)); // 0-1
    
    // 层级结构
    this.level = data.level || 0;
    this.parentId = data.parentId || null;
    this.children = data.children || [];
    this.collapsed = data.collapsed || false;
    
    // 状态和优先级
    this.status = data.status || 'pending'; // pending, in-progress, completed, cancelled, overdue
    this.priority = data.priority || 'normal'; // low, normal, high, critical
    this.type = data.type || 'task'; // task, milestone, summary, project
    
    // 资源分配
    this.assignee = data.assignee || '';
    this.team = data.team || [];
    this.resources = data.resources || [];
    
    // 多时间线支持 - 增强
    this.timelines = [];
    if (data.timelines && Array.isArray(data.timelines)) {
      this.timelines = data.timelines.map(t => 
        t instanceof Timeline ? t : new Timeline(t)
      );
    }
    
    // 兼容旧版本的里程碑数据
    if (data.milestones && Array.isArray(data.milestones) && this.timelines.length === 0) {
      const defaultTimeline = new Timeline({
        name: '主要里程碑',
        type: 'default',
        milestones: data.milestones
      });
      this.timelines.push(defaultTimeline);
    }
    
    // Y轴布局属性 - 新增
    this.rowHeight = data.rowHeight || 40; // 基础行高
    this.expandedHeight = data.expandedHeight || null; // 展开后的总高度，null表示自动计算
    this.timelineSpacing = data.timelineSpacing || 8; // 时间线之间的间距
    this.showTimelineLabels = data.showTimelineLabels !== false; // 是否显示时间线标签
    
    // 依赖关系
    this.dependencies = data.dependencies || []; // { id, type: 'FS'|'SS'|'FF'|'SF', lag: 0 }
    
    // 约束
    this.constraints = data.constraints || {}; // { type: 'ASAP'|'ALAP'|'MSO'|'MFO', date: null }
    
    // 扩展字段
    this.customFields = data.customFields || {};
    this.metadata = data.metadata || {};
    
    // 样式
    this.style = data.style || {};
    
    this.validate();
    this.calculateDuration();
    this.updateTimelineLayout();
  }

  validate() {
    if (!this.name) {
      throw new Error('Task name is required');
    }
    
    if (this.startDate && this.endDate && this.startDate > this.endDate) {
      throw new Error('Start date cannot be later than end date');
    }
  }

  generateId() {
    return `task-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  // 计算工期
  calculateDuration() {
    if (this.startDate && this.endDate) {
      this.duration = DateUtils.getDaysDiff(this.startDate, this.endDate) + 1;
    }
  }

  // 更新时间线布局
  updateTimelineLayout() {
    if (this.timelines.length === 0) return;

    // 按order排序时间线
    this.sortTimelines();

    let currentY = 0;
    this.timelines.forEach((timeline, index) => {
      if (timeline.visible && !timeline.collapsed) {
        timeline.yOffset = currentY;
        currentY += timeline.height + this.timelineSpacing;
      } else {
        timeline.yOffset = 0; // 隐藏或折叠的时间线
      }
    });

    // 计算展开后的总高度
    this.expandedHeight = Math.max(
      this.rowHeight,
      currentY - this.timelineSpacing + (this.showTimelineLabels ? 20 : 0)
    );
  }

  // 添加时间线
  addTimeline(timelineData) {
    const timeline = timelineData instanceof Timeline 
      ? timelineData 
      : new Timeline(timelineData);
    
    this.timelines.push(timeline);
    this.updateTimelineLayout();
    return timeline;
  }

  // 删除时间线
  removeTimeline(timelineId) {
    const index = this.timelines.findIndex(t => t.id === timelineId);
    if (index !== -1) {
      const removed = this.timelines.splice(index, 1)[0];
      this.updateTimelineLayout();
      return removed;
    }
    return null;
  }

  // 获取时间线
  getTimeline(timelineId) {
    return this.timelines.find(t => t.id === timelineId);
  }

  // 按顺序排序时间线
  sortTimelines() {
    this.timelines.sort((a, b) => {
      // 先按order排序，再按创建时间
      if (a.order !== b.order) {
        return a.order - b.order;
      }
      return a.id.localeCompare(b.id);
    });
  }

  // 获取可见时间线
  getVisibleTimelines() {
    return this.timelines.filter(t => t.visible && !t.collapsed);
  }

  // 获取时间线的渲染信息
  getTimelineRenderInfo(baseY = 0) {
    const renderInfo = [];
    
    this.timelines.forEach((timeline, index) => {
      if (!timeline.visible) return;
      
      const position = timeline.calculateYPosition(baseY, index, this.rowHeight);
      if (position.visible) {
        renderInfo.push({
          timeline,
          y: position.y,
          height: timeline.height,
          bounds: timeline.getBounds(baseY, index, this.rowHeight)
        });
      }
    });

    return renderInfo;
  }

  // 获取任务的实际渲染高度
  getRenderHeight() {
    if (this.collapsed || this.timelines.length === 0) {
      return this.rowHeight;
    }
    return this.expandedHeight || this.rowHeight;
  }

  // 获取所有里程碑（扁平化）
  getAllMilestones() {
    return this.timelines.reduce((all, timeline) => {
      if (timeline.visible && !timeline.collapsed) {
        return all.concat(timeline.milestones.map(m => ({
          ...m,
          timelineName: timeline.name,
          timelineColor: timeline.color,
          timelineY: timeline.yOffset
        })));
      }
      return all;
    }, []);
  }

  // 获取特定状态的里程碑
  getMilestonesByStatus(status) {
    return this.getAllMilestones().filter(m => m.status === status);
  }

  // 添加里程碑到指定时间线
  addMilestone(timelineId, milestoneData) {
    const timeline = this.getTimeline(timelineId);
    if (timeline) {
      return timeline.addMilestone(milestoneData);
    }
    throw new Error(`Timeline ${timelineId} not found`);
  }

  // 获取任务的整体进度（包含里程碑）
  getOverallProgress() {
    if (this.timelines.length === 0) {
      return this.progress;
    }
    
    // 考虑时间线进度
    const visibleTimelines = this.getVisibleTimelines();
    if (visibleTimelines.length === 0) {
      return this.progress;
    }
    
    const timelineProgress = visibleTimelines.reduce((sum, timeline) => {
      return sum + timeline.getProgress();
    }, 0) / visibleTimelines.length;
    
    // 任务进度和里程碑进度的加权平均
    return (this.progress * 0.7) + (timelineProgress * 0.3);
  }

  // 检查是否逾期
  isOverdue() {
    if (this.status === 'completed') return false;
    if (!this.endDate) return false;
    return this.endDate < DateUtils.today();
  }

  // 检查是否在关键路径上
  isCritical() {
    return this.priority === 'critical' || 
           this.metadata.critical === true ||
           this.timelines.some(t => t.type === 'critical');
  }

  // 添加依赖关系
  addDependency(taskId, type = 'FS', lag = 0) {
    const existing = this.dependencies.find(d => d.id === taskId);
    if (existing) {
      existing.type = type;
      existing.lag = lag;
    } else {
      this.dependencies.push({ id: taskId, type, lag });
    }
  }

  // 移除依赖关系
  removeDependency(taskId) {
    const index = this.dependencies.findIndex(d => d.id === taskId);
    if (index !== -1) {
      return this.dependencies.splice(index, 1)[0];
    }
    return null;
  }

  // 获取子任务数量
  getChildrenCount() {
    return this.children.length;
  }

  // 检查是否为父任务
  isParent() {
    return this.children.length > 0;
  }

  // 检查是否为叶子任务
  isLeaf() {
    return this.children.length === 0;
  }

  // 切换折叠状态
  toggleCollapsed() {
    this.collapsed = !this.collapsed;
    if (this.collapsed) {
      // 折叠时隐藏所有时间线
      this.timelines.forEach(t => t.collapsed = true);
    } else {
      // 展开时恢复时间线的原始状态
      this.timelines.forEach(t => t.collapsed = false);
    }
    this.updateTimelineLayout();
    return this.collapsed;
  }

  // 切换时间线显示
  toggleTimeline(timelineId) {
    const timeline = this.getTimeline(timelineId);
    if (timeline) {
      timeline.collapsed = !timeline.collapsed;
      this.updateTimelineLayout();
      return timeline.collapsed;
    }
    return false;
  }

  // 克隆任务
  clone() {
    const cloned = new TaskItem({
      ...this,
      id: this.generateId(),
      children: [...this.children],
      dependencies: this.dependencies.map(d => ({ ...d })),
      timelines: this.timelines.map(t => t.clone()),
      customFields: { ...this.customFields },
      metadata: { ...this.metadata },
      style: { ...this.style }
    });
    
    cloned.updateTimelineLayout();
    return cloned;
  }

  // 转换为 JSON
  toJSON() {
    return {
      id: this.id,
      name: this.name,
      description: this.description,
      startDate: DateUtils.formatDate(this.startDate),
      endDate: DateUtils.formatDate(this.endDate),
      duration: this.duration,
      progress: this.progress,
      level: this.level,
      parentId: this.parentId,
      children: this.children,
      collapsed: this.collapsed,
      status: this.status,
      priority: this.priority,
      type: this.type,
      assignee: this.assignee,
      team: this.team,
      resources: this.resources,
      timelines: this.timelines.map(t => t.toJSON()),
      rowHeight: this.rowHeight,
      expandedHeight: this.expandedHeight,
      timelineSpacing: this.timelineSpacing,
      showTimelineLabels: this.showTimelineLabels,
      dependencies: this.dependencies,
      constraints: this.constraints,
      customFields: this.customFields,
      metadata: this.metadata,
      style: this.style
    };
  }
}

export default TaskItem;
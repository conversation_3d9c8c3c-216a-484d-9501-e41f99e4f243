# 甘特图项目

一个功能丰富的甘特图组件，支持项目进度可视化、任务依赖关系、里程碑管理等功能。

## 功能特性

- 📊 **项目进度可视化** - 直观显示任务进度和时间线
- 🔗 **依赖关系管理** - 支持任务间的依赖关系显示
- 🎯 **里程碑管理** - 特殊标识重要节点
- 📅 **多视图模式** - 支持日、周、月视图切换
- 🔍 **缩放功能** - 支持放大缩小和适应屏幕
- 📁 **数据导入** - 支持CSV和JSON格式数据导入
- 💡 **丰富提示** - 鼠标悬停显示详细信息
- 🎨 **自定义样式** - 支持任务状态颜色配置

## 快速开始

### 1. 基本使用

```html
<!DOCTYPE html>
<html>
<head>
    <title>甘特图示例</title>
</head>
<body>
    <div id="ganttChart"></div>
    <div id="tooltip"></div>
    
    <script src="GanttChart.js"></script>
    <script>
        const gantt = new GanttChart('ganttChart');
    </script>
</body>
</html>
```

### 2. 数据格式

#### JSON格式（推荐）

```json
{
  "metadata": {
    "name": "项目名称",
    "description": "项目描述",
    "version": "1.0.0"
  },
  "project": {
    "name": "项目标题",
    "startDate": "2024-01-01",
    "endDate": "2024-02-01"
  },
  "tasks": [
    {
      "id": "task1",
      "name": "任务名称",
      "startDate": "2024-01-01",
      "endDate": "2024-01-10",
      "progress": 50,
      "status": "inProgress",
      "dependencies": [],
      "assignee": "负责人",
      "priority": "high",
      "timePoints": [
        {
          "id": "p1",
          "date": "2024-01-01",
          "name": "时间点名称",
          "type": "start"
        }
      ]
    }
  ]
}
```

#### CSV格式

```csv
任务名称,开始日期,结束日期,负责人,进度,状态,依赖关系,优先级
需求分析,2024-01-01,2024-01-10,张三,100,completed,,high
系统设计,2024-01-05,2024-01-15,李四,80,inProgress,task1,medium
```

## 数据文件

项目提供了多个示例数据文件：

### 1. 软件开发项目 (`data/sample-project.json`)
- 完整的软件开发流程
- 包含25个任务和5个里程碑
- 涵盖需求、设计、开发、测试、部署等阶段

### 2. 简单项目 (`data/simple-project.json`)
- 适合初学者的简单示例
- 包含8个任务和3个里程碑
- 展示基本的甘特图功能

## API参考

### 构造函数

```javascript
const gantt = new GanttChart(containerId, options);
```

#### 参数
- `containerId` (string): 容器元素ID
- `options` (object): 配置选项

#### 配置选项

```javascript
{
  width: 1200,              // 图表宽度
  height: 600,              // 图表高度
  taskHeight: 30,           // 任务条高度
  taskMargin: 5,            // 任务间距
  leftPanelWidth: 200,      // 左侧面板宽度
  headerHeight: 60,         // 头部高度
  pixelPerDay: 30,          // 每天像素数
  viewMode: "week",         // 视图模式: day/week/month
  showMilestones: true,     // 是否显示里程碑
  colors: {                 // 颜色配置
    completed: "#28a745",
    inProgress: "#0366d6",
    notStarted: "#6f42c1",
    milestone: "#f66a0a"
  }
}
```

### 方法

#### 数据加载

```javascript
// 从JSON文件加载数据
const result = await gantt.loadFromJSONFile('data/sample-project.json');

// 从JSON对象加载数据
const result = gantt.loadFromJSONObject(data);

// 设置数据
gantt.setData(tasks, milestones);

// 添加任务
gantt.addTask(task);

// 更新任务
gantt.updateTask(taskId, updates);

// 删除任务
gantt.removeTask(taskId);
```

#### 视图控制

```javascript
// 切换视图模式
gantt.config.viewMode = 'month';
gantt.updatePixelPerDay();
gantt.render();

// 切换里程碑显示
gantt.config.showMilestones = !gantt.config.showMilestones;
gantt.render();
```

## 任务状态

- `completed` - 已完成（绿色）
- `inProgress` - 进行中（蓝色）
- `notStarted` - 未开始（紫色）

## 时间点类型

- `start` - 开始节点
- `checkpoint` - 检查点
- `end` - 结束节点
- `milestone` - 里程碑

## 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。 
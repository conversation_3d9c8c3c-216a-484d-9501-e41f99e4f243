<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>滚动修复验证</title>
    <link rel="stylesheet" href="../../src/styles/gantt.css">
    <style>
        body {
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
        }

        .test-container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .test-header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }

        .test-header h1 {
            margin: 0 0 10px 0;
            font-size: 24px;
        }

        .test-header p {
            margin: 0;
            opacity: 0.9;
            font-size: 14px;
        }

        .test-instructions {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            border-radius: 6px;
            padding: 15px;
            margin: 20px;
            color: #0c5460;
        }

        .test-instructions h4 {
            margin: 0 0 10px 0;
            color: #0c5460;
        }

        .gantt-container {
            height: 600px;
            margin: 0;
            border-radius: 0;
            border: none;
            box-shadow: none;
        }

        .scroll-info {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(40, 167, 69, 0.9);
            color: white;
            padding: 10px 15px;
            border-radius: 6px;
            font-family: monospace;
            font-size: 12px;
            z-index: 1000;
            min-width: 200px;
        }

        .scroll-info div {
            margin-bottom: 5px;
        }

        .scroll-info div:last-child {
            margin-bottom: 0;
        }

        /* 高亮时间轴头部 */
        .gantt-timeline-header {
            border: 2px solid #28a745 !important;
            background: rgba(40, 167, 69, 0.1) !important;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>✅ 时间轴滚动修复验证</h1>
            <p>验证移除DOM滚动同步后的修复效果</p>
        </div>

        <div class="test-instructions">
            <h4>🧪 验证方法：</h4>
            <p><strong>横向滚动甘特图</strong>，观察时间轴头部是否显示当前可视区域对应的正确日期。如果修复成功，时间轴应该实时更新显示正确的日期范围。</p>
        </div>

        <div id="gantt-container" class="gantt-container"></div>
    </div>

    <div class="scroll-info" id="scrollInfo">
        <div>滚动位置: <span id="scrollPos">0</span></div>
        <div>可视刻度: <span id="scaleCount">-</span></div>
        <div>修复状态: ✅ 已应用</div>
    </div>

    <script type="module">
        import GanttChart from '../../src/GanttChart.js';

        // 生成测试数据
        function generateTestData() {
            const data = [];
            const startDate = new Date('2024-01-01');
            
            for (let i = 0; i < 15; i++) {
                const taskStartDate = new Date(startDate);
                taskStartDate.setDate(startDate.getDate() + i * 10);
                
                const taskEndDate = new Date(taskStartDate);
                taskEndDate.setDate(taskStartDate.getDate() + 7);
                
                data.push({
                    id: `task-${i + 1}`,
                    name: `任务 ${i + 1}`,
                    startDate: taskStartDate.toISOString().split('T')[0],
                    endDate: taskEndDate.toISOString().split('T')[0],
                    progress: Math.random(),
                    assignee: `用户${i + 1}`,
                    status: ['pending', 'in-progress', 'completed'][Math.floor(Math.random() * 3)],
                    level: 0
                });
            }
            
            return data;
        }

        // 初始化甘特图
        let ganttInstance;
        
        try {
            ganttInstance = new GanttChart('gantt-container', {
                data: generateTestData(),
                viewMode: 'day',
                pixelsPerDay: 50,
                taskList: {
                    width: 250,
                    columns: [
                        { key: 'name', title: '任务名称', width: 150 },
                        { key: 'assignee', title: '负责人', width: 100 }
                    ]
                },
                timeline: {
                    showWeekday: true,
                    adaptiveWidth: true,
                    weekdayFormat: 'short'
                }
            });

            console.log('甘特图初始化成功');

            // 监听滚动事件
            ganttInstance.on('scroll', (data) => {
                document.getElementById('scrollPos').textContent = Math.round(data.x);
                
                if (ganttInstance.timeScale) {
                    const viewportWidth = ganttInstance.elements.chartBody?.offsetWidth || 1000;
                    const visibleScales = ganttInstance.timeScale.getVisibleScales(data.x, viewportWidth);
                    document.getElementById('scaleCount').textContent = visibleScales.length;
                }
            });

        } catch (error) {
            console.error('甘特图初始化失败:', error);
        }
    </script>
</body>
</html>

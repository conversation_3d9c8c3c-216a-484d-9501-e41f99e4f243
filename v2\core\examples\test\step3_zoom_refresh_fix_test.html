<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Step3: 缩放刷新修复测试</title>
    <link rel="stylesheet" href="../../src/styles/gantt.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }

        .header h1 {
            margin: 0;
            font-size: 24px;
        }

        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
        }

        .test-panel {
            padding: 20px;
            background: #fef2f2;
            border-bottom: 1px solid #fecaca;
        }

        .test-controls {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .control-group {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #fecaca;
        }

        .control-group h4 {
            margin: 0 0 10px 0;
            color: #dc2626;
            font-size: 14px;
        }

        .zoom-controls {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .zoom-slider {
            width: 100%;
        }

        .zoom-buttons {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 5px;
        }

        .zoom-btn {
            padding: 8px 4px;
            border: 1px solid #dc2626;
            border-radius: 4px;
            background: white;
            color: #dc2626;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.2s ease;
        }

        .zoom-btn:hover {
            background: #dc2626;
            color: white;
        }

        .test-buttons {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 10px;
        }

        .test-btn {
            padding: 10px;
            border: 2px solid #dc2626;
            border-radius: 6px;
            background: white;
            color: #dc2626;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.2s ease;
        }

        .test-btn:hover {
            background: #dc2626;
            color: white;
        }

        .status-display {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #fecaca;
        }

        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 5px 0;
            border-bottom: 1px solid #fef2f2;
        }

        .status-item:last-child {
            border-bottom: none;
        }

        .status-label {
            font-size: 13px;
            color: #7f1d1d;
        }

        .status-value {
            font-size: 14px;
            font-weight: 600;
            color: #dc2626;
            font-family: monospace;
        }

        .gantt-container {
            height: 500px;
            position: relative;
            border: 2px solid #dc2626;
        }

        .log-panel {
            padding: 20px;
            background: #f9fafb;
            border-top: 1px solid #e5e7eb;
            max-height: 200px;
            overflow-y: auto;
        }

        .log-panel h3 {
            margin: 0 0 10px 0;
            color: #374151;
        }

        .log-entry {
            font-family: monospace;
            font-size: 12px;
            color: #6b7280;
            margin: 2px 0;
            padding: 2px 5px;
            background: white;
            border-radius: 3px;
        }

        .log-entry.error {
            color: #dc2626;
            background: #fef2f2;
        }

        .log-entry.success {
            color: #059669;
            background: #f0fdf4;
        }

        .instructions {
            background: #fffbeb;
            padding: 15px;
            border-left: 4px solid #f59e0b;
            margin-bottom: 20px;
        }

        .instructions h4 {
            margin: 0 0 10px 0;
            color: #92400e;
        }

        .instructions p {
            margin: 0;
            color: #78350f;
            font-size: 14px;
            line-height: 1.5;
        }

        /* 增强时间轴可见性以便观察变化 */
        .timeline-scale.bottom-row {
            border-right: 2px solid #dc2626 !important;
            transition: all 0.3s ease;
        }

        .timeline-scale.bottom-row:hover {
            background: rgba(220, 38, 38, 0.1) !important;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 缩放刷新修复测试</h1>
            <p>测试缩放操作后时间轴的立即刷新功能</p>
        </div>

        <div class="test-panel">
            <div class="instructions">
                <h4>测试说明</h4>
                <p>
                    之前的问题：缩放操作后需要滚动视图才能看到时间轴更新。
                    修复方案：添加强制刷新机制和requestAnimationFrame确保立即更新。
                    请测试各种缩放操作，观察时间轴是否立即响应变化。
                </p>
            </div>

            <div class="test-controls">
                <div class="control-group">
                    <h4>缩放控制</h4>
                    <div class="zoom-controls">
                        <input type="range" id="zoomSlider" class="zoom-slider" 
                               min="0.3" max="3.0" step="0.1" value="1.0">
                        <div class="zoom-buttons">
                            <button class="zoom-btn" onclick="setZoom(0.5)">50%</button>
                            <button class="zoom-btn" onclick="setZoom(1.0)">100%</button>
                            <button class="zoom-btn" onclick="setZoom(1.5)">150%</button>
                            <button class="zoom-btn" onclick="setZoom(2.0)">200%</button>
                        </div>
                    </div>
                </div>

                <div class="control-group">
                    <h4>测试操作</h4>
                    <div class="test-buttons">
                        <button class="test-btn" onclick="testRapidZoom()">快速缩放测试</button>
                        <button class="test-btn" onclick="testForceRefresh()">强制刷新测试</button>
                        <button class="test-btn" onclick="testViewModeZoom()">视图+缩放测试</button>
                        <button class="test-btn" onclick="clearLog()">清空日志</button>
                    </div>
                </div>

                <div class="control-group">
                    <h4>状态监控</h4>
                    <div class="status-display">
                        <div class="status-item">
                            <span class="status-label">当前缩放</span>
                            <span class="status-value" id="currentZoom">100%</span>
                        </div>
                        <div class="status-item">
                            <span class="status-label">刻度宽度</span>
                            <span class="status-value" id="scaleWidth">-</span>
                        </div>
                        <div class="status-item">
                            <span class="status-label">可见刻度</span>
                            <span class="status-value" id="visibleScales">-</span>
                        </div>
                        <div class="status-item">
                            <span class="status-label">刷新状态</span>
                            <span class="status-value" id="refreshStatus">正常</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="gantt-container" id="ganttContainer"></div>

        <div class="log-panel">
            <h3>操作日志</h3>
            <div id="logContainer"></div>
        </div>
    </div>

    <script type="module">
        import GanttChart from '../../src/GanttChart.js';
        import DateUtils from '../../src/utils/DateUtils.js';

        // 生成测试数据
        function generateTestData() {
            const tasks = [];
            const startDate = new Date('2024-01-01');
            
            for (let i = 0; i < 12; i++) {
                const taskStart = DateUtils.addDays(startDate, i * 5);
                const duration = 8 + Math.random() * 12;
                const taskEnd = DateUtils.addDays(taskStart, duration);
                
                tasks.push({
                    id: `refresh-test-${i}`,
                    name: `刷新测试任务 ${i + 1}`,
                    startDate: taskStart,
                    endDate: taskEnd,
                    progress: Math.random()
                });
            }
            
            return tasks;
        }

        // 初始化甘特图
        let ganttInstance = null;

        function initializeGantt() {
            const container = document.getElementById('ganttContainer');
            container.innerHTML = '';
            
            const testData = generateTestData();
            
            ganttInstance = new GanttChart('ganttContainer', {
                data: testData,
                viewMode: 'day',
                pixelsPerDay: 35,
                timeline: {
                    showWeekday: true,
                    adaptiveWidth: true,
                    minScaleWidth: 30,
                    maxScaleWidth: 100,
                    weekdayFormat: 'short'
                },
                taskList: {
                    columns: [
                        { key: 'name', title: '任务名称', width: 180 },
                        { key: 'progress', title: '进度', width: 80 }
                    ]
                }
            });

            ganttInstance.on('ready', () => {
                addLog('甘特图初始化完成', 'success');
                updateStatus();
            });

            ganttInstance.on('zoomChange', (zoomLevel) => {
                addLog(`缩放级别变化: ${Math.round(zoomLevel * 100)}%`, 'success');
                updateStatus();
            });

            ganttInstance.on('viewChange', (event) => {
                addLog(`视图模式变化: ${event.from} → ${event.to}`, 'success');
                updateStatus();
            });
        }

        // 更新状态显示
        function updateStatus() {
            if (!ganttInstance || !ganttInstance.timeScale) return;

            setTimeout(() => {
                const zoomLevel = ganttInstance.state.zoomLevel;
                const scales = ganttInstance.timeScale.getAllScales();
                const scaleWidth = scales.length > 1 ? (scales[1].x - scales[0].x) : 35;
                const visibleElements = document.querySelectorAll('.timeline-scale.bottom-row');

                document.getElementById('currentZoom').textContent = `${Math.round(zoomLevel * 100)}%`;
                document.getElementById('scaleWidth').textContent = `${scaleWidth.toFixed(1)}px`;
                document.getElementById('visibleScales').textContent = visibleElements.length;
                
                // 同步滑块
                document.getElementById('zoomSlider').value = zoomLevel;
                
                // 检查刷新状态
                const expectedScales = document.querySelectorAll('.timeline-scale.bottom-row').length;
                const refreshStatus = expectedScales > 0 ? '正常' : '需要刷新';
                document.getElementById('refreshStatus').textContent = refreshStatus;
                
            }, 100);
        }

        // 日志功能
        function addLog(message, type = 'info') {
            const logContainer = document.getElementById('logContainer');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${type}`;
            logEntry.textContent = `[${timestamp}] ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        // 测试函数
        window.setZoom = function(level) {
            addLog(`设置缩放级别: ${Math.round(level * 100)}%`);
            ganttInstance.setZoomLevel(level);
        };

        window.testRapidZoom = function() {
            addLog('开始快速缩放测试');
            const levels = [0.5, 1.5, 0.8, 2.0, 1.0];
            let index = 0;
            
            const interval = setInterval(() => {
                if (index >= levels.length) {
                    clearInterval(interval);
                    addLog('快速缩放测试完成', 'success');
                    return;
                }
                
                const level = levels[index];
                addLog(`快速缩放到: ${Math.round(level * 100)}%`);
                ganttInstance.setZoomLevel(level);
                index++;
            }, 500);
        };

        window.testForceRefresh = function() {
            addLog('测试强制刷新功能');
            ganttInstance.forceRefreshTimeline();
            addLog('强制刷新完成', 'success');
        };

        window.testViewModeZoom = function() {
            addLog('开始视图模式+缩放组合测试');
            
            // 切换到周视图并缩放
            ganttInstance.changeViewMode('week');
            setTimeout(() => {
                ganttInstance.setZoomLevel(1.5);
                setTimeout(() => {
                    // 切换回日视图并缩放
                    ganttInstance.changeViewMode('day');
                    setTimeout(() => {
                        ganttInstance.setZoomLevel(1.0);
                        addLog('视图模式+缩放测试完成', 'success');
                    }, 300);
                }, 300);
            }, 300);
        };

        window.clearLog = function() {
            document.getElementById('logContainer').innerHTML = '';
            addLog('日志已清空');
        };

        // 滑块事件
        document.getElementById('zoomSlider').addEventListener('input', (e) => {
            const zoomLevel = parseFloat(e.target.value);
            addLog(`滑块缩放: ${Math.round(zoomLevel * 100)}%`);
            ganttInstance.setZoomLevel(zoomLevel);
        });

        // 初始化
        document.addEventListener('DOMContentLoaded', () => {
            addLog('页面加载完成，初始化缩放刷新测试...');
            initializeGantt();
        });
    </script>
</body>
</html>

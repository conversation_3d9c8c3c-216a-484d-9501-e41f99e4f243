# 时间轴横向滚动虚拟化修复

## 问题描述

当前时间轴在甘特图横向滚动时，渲染存在问题，主要表现为：

1. **虚拟化计算错误**：二分查找算法的边界处理不正确
2. **刻度遗漏**：可视区域边缘的时间刻度可能被遗漏
3. **渲染滞后**：滚动时时间轴头部更新存在延迟
4. **宽度不同步**：时间轴容器宽度与内容宽度不匹配
5. **位置偏移**：刻度位置计算精度不足导致对齐问题

## 根本原因分析

### 1. TimeScale._findScaleIndex() 边界处理错误

**问题代码**：
```javascript
_findScaleIndex(x) {
  // ... 二分查找逻辑
  return Math.max(0, left - 1); // 这里可能返回错误的索引
}
```

**问题**：当目标X坐标位于刻度之间时，返回的索引可能不准确，导致虚拟化计算遗漏边缘刻度。

### 2. getVisibleScales() 范围计算不足

**问题代码**：
```javascript
for (let i = startIndex; i < actualEndIndex; i++) {
  const scale = this._scales[i];
  if (scale.x >= startX && scale.x <= endX) { // 严格的边界检查可能遗漏边缘元素
    // ...
  }
}
```

**问题**：严格的边界检查导致边缘刻度被过滤掉，特别是在快速滚动时。

### 3. 滚动防抖机制不当

**问题代码**：
```javascript
setTimeout(() => {
  this.renderTimelineHeader();
}, 16); // 固定延迟可能导致滚动时渲染滞后
```

**问题**：使用固定延迟的防抖机制在快速滚动时可能导致渲染滞后。

## 修复方案

### 1. 修复二分查找边界处理

**修复代码**：
```javascript
_findScaleIndex(x) {
  if (this._scales.length === 0) return 0;

  let left = 0;
  let right = this._scales.length - 1;

  while (left < right) {
    const mid = Math.floor((left + right) / 2);
    if (this._scales[mid].x < x) {
      left = mid + 1;
    } else {
      right = mid;
    }
  }

  // 修复边界处理：确保返回正确的起始索引
  while (left > 0 && this._scales[left].x > x) {
    left--;
  }

  return Math.max(0, Math.min(left, this._scales.length - 1));
}
```

**改进点**：
- 添加边界检查，确保返回的索引对应的刻度X坐标不大于目标X
- 防止数组越界

### 2. 改进可视区域计算

**修复代码**：
```javascript
getVisibleScales(viewportX, viewportWidth, options = {}) {
  // ... 基础逻辑

  // 向前扩展起始索引，确保不遗漏边缘刻度
  while (startIndex > 0 && this._scales[startIndex - 1].x >= startX - 50) {
    startIndex--;
  }

  // 向后扩展结束索引，确保包含所有相关刻度
  while (endIndex < this._scales.length - 1 && this._scales[endIndex + 1].x <= endX + 50) {
    endIndex++;
  }

  // 放宽边界条件
  if (scale.x >= startX - 10 && scale.x <= endX + 10) {
    // 包含刻度
  }
}
```

**改进点**：
- 扩展索引范围，确保包含边缘刻度
- 放宽边界条件，增加容错性
- 添加缓冲区以确保平滑滚动

### 3. 优化滚动防抖机制

**修复代码**：
```javascript
bindScrollEvents() {
  let timelineHeaderUpdateFrame = null;
  let lastScrollX = 0;

  const updateTimelineHeaderOptimized = () => {
    if (timelineHeaderUpdateFrame) {
      cancelAnimationFrame(timelineHeaderUpdateFrame);
    }
    
    timelineHeaderUpdateFrame = requestAnimationFrame(() => {
      const currentScrollX = this.elements.chartBody?.scrollLeft || 0;
      if (Math.abs(currentScrollX - lastScrollX) > 1) {
        this.renderTimelineHeader();
        lastScrollX = currentScrollX;
      }
      timelineHeaderUpdateFrame = null;
    });
  };
}
```

**改进点**：
- 使用requestAnimationFrame替代setTimeout
- 只在横向滚动时更新时间轴头部
- 避免不必要的重复渲染

### 4. 修复刻度宽度和位置计算

**修复代码**：
```javascript
_generateBottomRowScales(visibleScales) {
  // 修复刻度宽度计算
  let scaleWidth;
  if (nextScale) {
    scaleWidth = Math.max(1, nextScale.x - scale.x);
  } else {
    const unit = DateUtils.getTimeUnit(this.state.viewMode);
    scaleWidth = this.options.pixelsPerDay * this.state.zoomLevel * unit.duration;
  }

  // 确保刻度位置精确对齐
  const adjustedX = Math.round(scale.x * 100) / 100;

  return `
    <div style="left: ${adjustedX}px; width: ${Math.round(scaleWidth)}px;">
      <!-- 刻度内容 -->
    </div>
  `;
}
```

**改进点**：
- 改进最后一个刻度的宽度计算
- 提高位置计算精度
- 确保宽度为正数

### 5. 同步容器宽度

**修复代码**：
```javascript
renderTimelineHeader() {
  // 确保时间轴头部的总宽度与内容同步
  const totalWidth = this.timeScale.getTotalWidth();
  scalesContainer.style.width = `${totalWidth}px`;
  
  // 同时设置父容器的宽度以确保滚动正常
  if (timelineHeader) {
    timelineHeader.style.width = `${totalWidth}px`;
  }
}
```

**改进点**：
- 同步设置容器和内容宽度
- 确保滚动行为正常

## 测试验证

创建了专门的测试页面 `timeline_scroll_fix_test.html` 来验证修复效果：

### 测试用例

1. **平滑滚动测试**：验证连续滚动时时间轴渲染的流畅性
2. **快速滚动测试**：验证快速跳跃滚动时的渲染准确性
3. **边界滚动测试**：验证滚动到边界位置时的渲染正确性
4. **缩放滚动测试**：验证不同缩放级别下的滚动表现

### 性能监控

- 可视刻度数量监控
- 渲染时间统计
- 滚动位置跟踪
- 缩放级别显示

## 预期效果

修复后应该实现：

1. **准确的虚拟化**：所有可视区域内的时间刻度都能正确渲染
2. **流畅的滚动**：横向滚动时时间轴头部实时更新，无延迟
3. **精确的对齐**：时间刻度位置精确对齐，无偏移
4. **稳定的性能**：大数据量下滚动性能稳定
5. **正确的边界处理**：滚动到边界位置时渲染正常

## 相关文件

- `v2/core/src/utils/TimeScale.js` - 时间刻度计算核心逻辑
- `v2/core/src/GanttChart.js` - 甘特图主要渲染逻辑
- `v2/core/examples/test/timeline_scroll_fix_test.html` - 修复验证测试页面

## 后续优化

1. **进一步的性能优化**：考虑实现更高级的虚拟化策略
2. **缓存机制**：为频繁计算的结果添加缓存
3. **自适应缓冲区**：根据滚动速度动态调整缓冲区大小
4. **渲染批处理**：批量处理多个刻度的渲染操作

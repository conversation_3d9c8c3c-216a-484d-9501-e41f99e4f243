<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>GanttChart-Examples</title>
    <link rel="stylesheet" href="../src/styles/gantt.css" />
    <script type="module" src="../src/GanttChart.js"></script>
    <script type="module" src="../src/utils/DateUtils.js"></script>
  </head>
  <body>
    <div id="gantt-container" class="gantt-container">
      <!-- 甘特图将通过 JavaScript 初始化 -->
    </div>

    <script>
      // 示例数据生成器
      class DataGenerator {
        static generateDefaultData() {
          const today = DateUtils.today();

          return [
            {
              id: "task-1",
              name: "项目启动",
              startDate: DateUtils.formatDate(today),
              endDate: DateUtils.formatDate(DateUtils.addDays(today, 5)),
              duration: 5,
              progress: 1.0,
              assignee: "张三",
              level: 0,
              status: "completed",
              priority: "high",
              customFields: {
                department: "项目组",
                cost: "¥10,000",
              },
              timelines: [
                {
                  name: "主要里程碑",
                  type: "default",
                  milestones: [
                    {
                      name: "项目启动会",
                      date: DateUtils.formatDate(DateUtils.addDays(today, 1)),
                      type: "review",
                      status: "completed",
                    },
                  ],
                },
              ],
            },
            {
              id: "task-2",
              name: "需求分析",
              startDate: DateUtils.formatDate(DateUtils.addDays(today, 6)),
              endDate: DateUtils.formatDate(DateUtils.addDays(today, 20)),
              duration: 15,
              progress: 0.8,
              assignee: "李四",
              level: 1,
              status: "in-progress",
              priority: "high",
              customFields: {
                department: "产品组",
                cost: "¥25,000",
              },
              timelines: [
                {
                  name: "需求里程碑",
                  type: "business",
                  milestones: [
                    {
                      name: "需求调研完成",
                      date: DateUtils.formatDate(DateUtils.addDays(today, 10)),
                      type: "info",
                      status: "completed",
                    },
                    {
                      name: "需求评审",
                      date: DateUtils.formatDate(DateUtils.addDays(today, 15)),
                      type: "review",
                      status: "pending",
                    },
                  ],
                },
              ],
            },
            {
              id: "task-3",
              name: "系统设计",
              startDate: DateUtils.formatDate(DateUtils.addDays(today, 21)),
              endDate: DateUtils.formatDate(DateUtils.addDays(today, 35)),
              duration: 15,
              progress: 0.4,
              assignee: "王五",
              level: 1,
              status: "in-progress",
              priority: "medium",
              customFields: {
                department: "架构组",
                cost: "¥30,000",
              },
              timelines: [
                {
                  name: "技术设计",
                  type: "technical",
                  milestones: [
                    {
                      name: "技术方案评审",
                      date: DateUtils.formatDate(DateUtils.addDays(today, 28)),
                      type: "review",
                      status: "pending",
                    },
                  ],
                },
                {
                  name: "UI设计",
                  type: "secondary",
                  milestones: [
                    {
                      name: "UI设计完成",
                      date: DateUtils.formatDate(DateUtils.addDays(today, 30)),
                      type: "delivery",
                      status: "pending",
                    },
                  ],
                },
              ],
            },
            {
              id: "task-4",
              name: "开发实现",
              startDate: DateUtils.formatDate(DateUtils.addDays(today, 36)),
              endDate: DateUtils.formatDate(DateUtils.addDays(today, 70)),
              duration: 35,
              progress: 0.2,
              assignee: "赵六",
              level: 0,
              status: "pending",
              priority: "critical",
              customFields: {
                department: "开发组",
                cost: "¥80,000",
              },
              timelines: [
                {
                  name: "后端开发",
                  type: "technical",
                  milestones: [
                    {
                      name: "API开发完成",
                      date: DateUtils.formatDate(DateUtils.addDays(today, 50)),
                      type: "delivery",
                      status: "pending",
                    },
                  ],
                },
                {
                  name: "前端开发",
                  type: "technical",
                  milestones: [
                    {
                      name: "前端界面完成",
                      date: DateUtils.formatDate(DateUtils.addDays(today, 60)),
                      type: "delivery",
                      status: "pending",
                    },
                  ],
                },
              ],
            },
            {
              id: "task-5",
              name: "测试验收",
              startDate: DateUtils.formatDate(DateUtils.addDays(today, 71)),
              endDate: DateUtils.formatDate(DateUtils.addDays(today, 85)),
              duration: 15,
              progress: 0.0,
              assignee: "孙七",
              level: 0,
              status: "pending",
              priority: "high",
              customFields: {
                department: "测试组",
                cost: "¥15,000",
              },
              timelines: [
                {
                  name: "测试里程碑",
                  type: "default",
                  milestones: [
                    {
                      name: "功能测试完成",
                      date: DateUtils.formatDate(DateUtils.addDays(today, 78)),
                      type: "review",
                      status: "pending",
                    },
                    {
                      name: "项目验收",
                      date: DateUtils.formatDate(DateUtils.addDays(today, 83)),
                      type: "approval",
                      status: "pending",
                    },
                  ],
                },
              ],
            },
          ];
        }

        static generateComplexData() {
          // 生成包含更多时间线的复杂数据
          const data = this.generateDefaultData();

          // 为每个任务添加更多时间线
          data.forEach((task) => {
            if (task.timelines.length < 3) {
              task.timelines.push({
                name: "风险管控",
                type: "warning",
                milestones: [
                  {
                    name: "风险评估",
                    date: task.startDate,
                    type: "warning",
                    status: "pending",
                  },
                ],
              });
            }
          });

          return data;
        }

        static generateSimpleData() {
          const today = DateUtils.today();
          return [
            {
              id: "simple-1",
              name: "简单任务1",
              startDate: DateUtils.formatDate(today),
              endDate: DateUtils.formatDate(DateUtils.addDays(today, 3)),
              duration: 3,
              progress: 0.6,
              assignee: "用户A",
              status: "in-progress",
            },
            {
              id: "simple-2",
              name: "简单任务2",
              startDate: DateUtils.formatDate(DateUtils.addDays(today, 4)),
              endDate: DateUtils.formatDate(DateUtils.addDays(today, 8)),
              duration: 5,
              progress: 0.0,
              assignee: "用户B",
              status: "pending",
            },
          ];
        }

        static generateLargeData() {
          const data = [];
          const today = DateUtils.today();

          for (let i = 0; i < 50; i++) {
            const startOffset = i * 2;
            const duration = Math.floor(Math.random() * 10) + 3;

            data.push({
              id: `large-task-${i + 1}`,
              name: `批量任务 ${i + 1}`,
              startDate: DateUtils.formatDate(DateUtils.addDays(today, startOffset)),
              endDate: DateUtils.formatDate(DateUtils.addDays(today, startOffset + duration)),
              duration: duration,
              progress: Math.random(),
              assignee: `用户${String.fromCharCode(65 + (i % 26))}`,
              level: i % 3,
              status: ["pending", "in-progress", "completed"][Math.floor(Math.random() * 3)],
              priority: ["low", "normal", "high"][Math.floor(Math.random() * 3)],
            });
          }

          return data;
        }
      }

      // 全局变量
      let ganttInstance = null;
      
      document.addEventListener("DOMContentLoaded", () => {
        console.log("🚀 开始初始化甘特图演示...");
        
        // 创建甘特图实例
        ganttInstance = new GanttChart("gantt-container", {
          viewMode: "day",
          onTaskClick: (task) => {
            console.log("任务被点击:", task);
            alert(`任务: ${task.name}\n负责人: ${task.assignee}\n进度: ${Math.round(task.progress * 100)}%`);
          },
          onMilestoneClick: (milestone) => {
            console.log("里程碑被点击:", milestone);
            alert(`里程碑: ${milestone.name}\n日期: ${milestone.date}\n类型: ${milestone.type}`);
          },
          onDataChange: (data) => {
            console.log("数据变更:", data);
          },
          data: DataGenerator.generateDefaultData(),
          taskList: {
            display: true,
            columns: [
              { key: "name", title: "任务名称", width: 180 },
              { key: "assignee", title: "负责人", width: 80 },
              { key: "startDate", title: "开始", width: 80 },
              { key: "duration", title: "工期", width: 60 },
            ],
          },
        });

        // 将实例暴露到全局，便于调试
        window.ganttInstance = ganttInstance;
      });
    </script>
  </body>
</html>

<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>甘特图组件 - Step 2 集成版</title>
    <style>
      /* 基础样式重置 */
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
        background-color: #f5f5f5;
        color: #333;
      }

      /* 甘特图容器 */
      .gantt-container {
        width: 100%;
        height: 100vh;
        display: flex;
        flex-direction: column;
        background: white;
        border: 1px solid #e1e5e9;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }

      /* 头部工具栏 */
      .gantt-header {
        background: #fafbfc;
        border-bottom: 1px solid #e1e5e9;
        padding: 12px 16px;
      }

      .gantt-toolbar {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .gantt-view-controls {
        display: flex;
        gap: 4px;
      }

      .gantt-btn {
        background: white;
        border: 1px solid #d0d7de;
        border-radius: 6px;
        padding: 6px 12px;
        font-size: 14px;
        cursor: pointer;
        transition: all 0.2s ease;
      }

      .gantt-btn:hover {
        background: #f6f8fa;
        border-color: #8c959f;
      }

      .gantt-btn.active {
        background: #0969da;
        color: white;
        border-color: #0969da;
      }

      .gantt-actions {
        display: flex;
        gap: 8px;
      }

      /* 主体区域 */
      .gantt-main {
        flex: 1;
        display: flex;
        overflow: hidden;
      }

      /* 左侧面板 */
      .gantt-left-panel {
        width: 300px;
        background: white;
        border-right: 1px solid #e1e5e9;
        display: flex;
        flex-direction: column;
        overflow: hidden;
      }

      .gantt-table-header {
        background: #f6f8fa;
        border-bottom: 1px solid #e1e5e9;
        padding: 0;
        display: flex;
        align-items: center;
        height: 40px;
        font-weight: 600;
        font-size: 13px;
        color: #656d76;
      }

      .gantt-table-header-cell {
        padding: 0 12px;
        border-right: 1px solid #e1e5e9;
        display: flex;
        align-items: center;
        height: 100%;
      }

      .gantt-table-body {
        flex: 1;
        overflow-y: auto;
        overflow-x: hidden;
      }

      .gantt-table-row {
        display: flex;
        align-items: center;
        height: 40px;
        border-bottom: 1px solid #f1f3f4;
        cursor: pointer;
        transition: background-color 0.15s ease;
      }

      .gantt-table-row:hover {
        background-color: #f6f8fa;
      }

      .gantt-table-row.selected {
        background-color: #dbeafe;
      }

      .gantt-table-cell {
        padding: 0 12px;
        border-right: 1px solid #f1f3f4;
        font-size: 14px;
        color: #24292f;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        display: flex;
        align-items: center;
        height: 100%;
      }

      /* 分割器 */
      .gantt-splitter {
        width: 4px;
        background: #e1e5e9;
        cursor: col-resize;
        transition: background-color 0.2s ease;
      }

      .gantt-splitter:hover {
        background: #8c959f;
      }

      /* 右侧面板 */
      .gantt-right-panel {
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: hidden;
      }

      .gantt-timeline-header {
        background: #f6f8fa;
        border-bottom: 1px solid #e1e5e9;
        padding: 0 16px;
        height: 40px;
        display: flex;
        align-items: center;
        font-weight: 600;
        font-size: 13px;
        color: #656d76;
      }

      .gantt-chart-body {
        flex: 1;
        overflow: auto;
        position: relative;
        background: white;
      }

      .gantt-svg {
        display: block;
        width: 100%;
        min-width: 800px;
        background: white;
      }

      /* SVG 样式 */
      .gantt-grid-layer rect {
        fill: none;
        stroke: #f1f3f4;
        stroke-width: 1;
      }

      .task-bar {
        cursor: pointer;
        transition: opacity 0.2s ease;
      }

      .task-bar:hover {
        opacity: 0.8;
      }

      .task-bar.selected {
        filter: brightness(1.1);
      }

      .task-main-bar {
        filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
      }

      .task-progress-bar {
        filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.05));
      }

      .task-text {
        pointer-events: none;
        user-select: none;
      }

      .milestone-marker {
        cursor: pointer;
        transition: transform 0.2s ease;
      }

      .milestone-marker:hover {
        transform: scale(1.1);
      }

      .dependency-line {
        pointer-events: none;
      }

      .time-scales .scale-label {
        font-size: 11px;
        fill: #656d76;
      }

      /* 底部状态栏 */
      .gantt-footer {
        background: #f6f8fa;
        border-top: 1px solid #e1e5e9;
        padding: 8px 16px;
        height: 40px;
      }

      .gantt-status {
        display: flex;
        gap: 20px;
        font-size: 12px;
        color: #656d76;
      }

      /* 响应式设计 */
      @media (max-width: 768px) {
        .gantt-left-panel {
          width: 250px;
        }

        .gantt-actions {
          display: none;
        }

        .gantt-view-controls .gantt-btn {
          padding: 4px 8px;
          font-size: 12px;
        }
      }

      /* 滚动条样式 */
      .gantt-table-body::-webkit-scrollbar,
      .gantt-chart-body::-webkit-scrollbar {
        width: 8px;
        height: 8px;
      }

      .gantt-table-body::-webkit-scrollbar-track,
      .gantt-chart-body::-webkit-scrollbar-track {
        background: #f1f3f4;
      }

      .gantt-table-body::-webkit-scrollbar-thumb,
      .gantt-chart-body::-webkit-scrollbar-thumb {
        background: #c9c9c9;
        border-radius: 4px;
      }

      .gantt-table-body::-webkit-scrollbar-thumb:hover,
      .gantt-chart-body::-webkit-scrollbar-thumb:hover {
        background: #a0a0a0;
      }

      /* 加载状态 */
      .gantt-loading {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        font-size: 14px;
        color: #656d76;
      }

      /* 消息提示 */
      .gantt-message {
        position: fixed;
        top: 20px;
        right: 20px;
        background: #0969da;
        color: white;
        padding: 12px 16px;
        border-radius: 6px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        z-index: 1000;
        opacity: 0;
        transform: translateY(-20px);
        transition: all 0.3s ease;
      }

      .gantt-message.show {
        opacity: 1;
        transform: translateY(0);
      }

      /* 任务状态颜色 */
      .status-completed {
        color: #28a745;
      }
      .status-in-progress {
        color: #0969da;
      }
      .status-pending {
        color: #6c757d;
      }
      .status-overdue {
        color: #dc3545;
      }
    </style>
  </head>
  <body>
    <div id="gantt-demo"></div>

    <!-- Step 2: 数据结构和工具系统 -->
    <script>
      // DateUtils.js - 日期处理工具类
      class DateUtils {
        static FORMATS = {
          DATE: "YYYY-MM-DD",
          DATETIME: "YYYY-MM-DD HH:mm:ss",
          DISPLAY: "MM/DD",
          MONTH_YEAR: "YYYY-MM",
          YEAR: "YYYY",
        };

        static VIEW_MODES = {
          DAY: "day",
          WEEK: "week",
          MONTH: "month",
          QUARTER: "quarter",
          YEAR: "year",
        };

        static parseDate(dateInput) {
          if (!dateInput) return null;

          if (dateInput instanceof Date) {
            return isNaN(dateInput.getTime()) ? null : dateInput;
          }

          if (typeof dateInput === "string") {
            const date = new Date(dateInput);
            return isNaN(date.getTime()) ? null : date;
          }

          return null;
        }

        static formatDate(date, format = DateUtils.FORMATS.DATE) {
          if (!date || !(date instanceof Date)) return "";

          const year = date.getFullYear();
          const month = String(date.getMonth() + 1).padStart(2, "0");
          const day = String(date.getDate()).padStart(2, "0");
          const hours = String(date.getHours()).padStart(2, "0");
          const minutes = String(date.getMinutes()).padStart(2, "0");
          const seconds = String(date.getSeconds()).padStart(2, "0");

          return format
            .replace("YYYY", year)
            .replace("MM", month)
            .replace("DD", day)
            .replace("HH", hours)
            .replace("mm", minutes)
            .replace("ss", seconds);
        }

        static getDaysDiff(startDate, endDate) {
          if (!startDate || !endDate) return 0;

          const oneDay = 24 * 60 * 60 * 1000;
          const start = new Date(
            startDate.getFullYear(),
            startDate.getMonth(),
            startDate.getDate()
          );
          const end = new Date(
            endDate.getFullYear(),
            endDate.getMonth(),
            endDate.getDate()
          );

          return Math.round((end - start) / oneDay);
        }

        static addDays(date, days) {
          if (!date) return null;
          const result = new Date(date);
          result.setDate(result.getDate() + days);
          return result;
        }

        static today() {
          const now = new Date();
          now.setHours(0, 0, 0, 0);
          return now;
        }

        static generateTimeScale(startDate, endDate, viewMode) {
          if (!startDate || !endDate) return [];

          const scales = [];
          let current = new Date(startDate);

          while (current <= endDate) {
            scales.push({
              date: new Date(current),
              label: DateUtils.formatTimeLabel(current, viewMode),
              x: 0,
            });

            switch (viewMode) {
              case DateUtils.VIEW_MODES.DAY:
                current = DateUtils.addDays(current, 1);
                break;
              case DateUtils.VIEW_MODES.WEEK:
                current = DateUtils.addDays(current, 7);
                break;
              case DateUtils.VIEW_MODES.MONTH:
                current.setMonth(current.getMonth() + 1);
                break;
              default:
                current = DateUtils.addDays(current, 1);
            }
          }

          return scales;
        }

        static formatTimeLabel(date, viewMode) {
          if (!date) return "";

          switch (viewMode) {
            case DateUtils.VIEW_MODES.DAY:
              return DateUtils.formatDate(date, "MM/DD");
            case DateUtils.VIEW_MODES.WEEK:
              const weekEnd = DateUtils.addDays(date, 6);
              return `${DateUtils.formatDate(
                date,
                "MM/DD"
              )}-${DateUtils.formatDate(weekEnd, "MM/DD")}`;
            case DateUtils.VIEW_MODES.MONTH:
              return DateUtils.formatDate(date, "YYYY-MM");
            default:
              return DateUtils.formatDate(date, "MM/DD");
          }
        }

        static validateDateRange(startDate, endDate) {
          const result = { valid: true, errors: [] };

          if (!startDate) {
            result.valid = false;
            result.errors.push("开始日期不能为空");
          }

          if (!endDate) {
            result.valid = false;
            result.errors.push("结束日期不能为空");
          }

          if (startDate && endDate && startDate > endDate) {
            result.valid = false;
            result.errors.push("开始日期不能晚于结束日期");
          }

          return result;
        }
      }

      // EventEmitter.js - 事件发布订阅系统
      class EventEmitter {
        constructor() {
          this.events = new Map();
          this.onceEvents = new Map();
          this.stats = { emitted: 0, listeners: 0 };
          this.debug = false;
        }

        on(event, listener, options = {}) {
          if (typeof listener !== "function") {
            throw new TypeError("Listener must be a function");
          }

          if (!this.events.has(event)) {
            this.events.set(event, []);
          }

          const listenerInfo = {
            fn: listener,
            context: options.context || null,
            priority: options.priority || 0,
            once: false,
            id: this.generateListenerId(),
          };

          const listeners = this.events.get(event);
          const insertIndex = listeners.findIndex(
            (l) => l.priority < listenerInfo.priority
          );

          if (insertIndex === -1) {
            listeners.push(listenerInfo);
          } else {
            listeners.splice(insertIndex, 0, listenerInfo);
          }

          this.stats.listeners++;
          return this;
        }

        emit(event, ...args) {
          this.stats.emitted++;

          if (!this.events.has(event)) {
            return false;
          }

          const listeners = this.events.get(event).slice();
          let hasListeners = false;

          for (const listenerInfo of listeners) {
            try {
              hasListeners = true;
              if (listenerInfo.context) {
                listenerInfo.fn.apply(listenerInfo.context, args);
              } else {
                listenerInfo.fn(...args);
              }
            } catch (error) {
              console.error(`Error in listener for event "${event}":`, error);
            }
          }

          return hasListeners;
        }

        off(event, listener) {
          if (!this.events.has(event)) return this;

          const listeners = this.events.get(event);
          if (!listener) {
            this.stats.listeners -= listeners.length;
            this.events.delete(event);
          } else {
            const index = listeners.findIndex((l) => l.fn === listener);
            if (index !== -1) {
              listeners.splice(index, 1);
              this.stats.listeners--;
              if (listeners.length === 0) {
                this.events.delete(event);
              }
            }
          }

          return this;
        }

        generateListenerId() {
          return `listener-${Date.now()}-${Math.random()
            .toString(36)
            .substr(2, 9)}`;
        }
      }

      // SvgUtils.js - SVG 操作工具类
      class SvgUtils {
        static SVG_NS = "http://www.w3.org/2000/svg";

        static createElement(tagName, attributes = {}, textContent = "") {
          const element = document.createElementNS(SvgUtils.SVG_NS, tagName);

          Object.entries(attributes).forEach(([key, value]) => {
            if (value !== null && value !== undefined) {
              element.setAttribute(key, value);
            }
          });

          if (textContent) {
            element.textContent = textContent;
          }

          return element;
        }

        static createRect(x, y, width, height, options = {}) {
          return SvgUtils.createElement("rect", {
            x,
            y,
            width,
            height,
            fill: options.fill || "#4A90E2",
            stroke: options.stroke || "none",
            "stroke-width": options.strokeWidth || 0,
            rx: options.borderRadius || 0,
            ry: options.borderRadius || 0,
            ...options.attributes,
          });
        }

        static createText(text, x, y, options = {}) {
          return SvgUtils.createElement(
            "text",
            {
              x,
              y,
              fill: options.fill || "#333",
              "font-size": options.fontSize || 12,
              "font-family": options.fontFamily || "Arial, sans-serif",
              "font-weight": options.fontWeight || "normal",
              "text-anchor": options.textAnchor || "start",
              ...options.attributes,
            },
            text
          );
        }

        static createGroup(attributes = {}, className = "") {
          if (className) {
            attributes.class = className;
          }
          return SvgUtils.createElement("g", attributes);
        }

        static createTaskBar(x, y, width, height, options = {}) {
          const group = SvgUtils.createGroup({
            transform: `translate(${x}, ${y})`,
            class: "task-bar",
            "data-task-id": options.id || "",
            "data-task-status": options.status || "normal",
          });

          const rect = SvgUtils.createRect(0, 0, width, height, {
            fill: options.fill || "url(#taskGradient)",
            stroke: options.stroke || "none",
            borderRadius: options.borderRadius || 4,
            attributes: { class: "task-main-bar" },
          });
          group.appendChild(rect);

          if (options.progress > 0) {
            const progressWidth = width * options.progress;
            const progressRect = SvgUtils.createRect(
              0,
              0,
              progressWidth,
              height,
              {
                fill: options.progressFill || "url(#progressGradient)",
                borderRadius: options.borderRadius || 4,
                attributes: { class: "task-progress-bar", opacity: 0.8 },
              }
            );
            group.appendChild(progressRect);
          }

          if (options.showText !== false && options.text) {
            const textY = height / 2 + 4;
            const text = SvgUtils.createText(options.text, 8, textY, {
              fill: options.textColor || "#fff",
              fontSize: options.fontSize || 12,
              fontWeight: "500",
              attributes: { class: "task-text" },
            });
            group.appendChild(text);
          }

          return group;
        }

        static createMilestone(x, y, type = "diamond", options = {}) {
          const group = SvgUtils.createGroup({
            transform: `translate(${x}, ${y})`,
            class: "milestone-marker",
            "data-milestone-id": options.id || "",
            "data-milestone-type": type,
          });

          const colors = {
            review: "#FF9500",
            delivery: "#007AFF",
            approval: "#34C759",
            warning: "#FF3B30",
            info: "#5AC8FA",
          };

          const fill = colors[options.status] || colors.info;

          // 创建菱形里程碑
          const diamond = SvgUtils.createElement("polygon", {
            points: "0,-8 8,0 0,8 -8,0",
            fill,
            stroke: "#333",
            "stroke-width": 1,
          });
          group.appendChild(diamond);

          return group;
        }

        static screenToSVG(svg, clientX, clientY) {
          try {
            const pt = svg.createSVGPoint();
            pt.x = clientX;
            pt.y = clientY;
            const screenCTM = svg.getScreenCTM();
            const svgPoint = pt.matrixTransform(screenCTM.inverse());
            return { x: svgPoint.x, y: svgPoint.y };
          } catch (e) {
            const rect = svg.getBoundingClientRect();
            return { x: clientX - rect.left, y: clientY - rect.top };
          }
        }
      }

      // DataStructures.js - 数据结构定义
      class Milestone {
        constructor(data = {}) {
          this.id = data.id || this.generateId();
          this.name = data.name || "";
          this.date = DateUtils.parseDate(data.date);
          this.type = data.type || "review";
          this.status = data.status || "pending";
          this.description = data.description || "";
          this.icon = data.icon || "diamond";
          this.color = data.color || null;
          this.priority = data.priority || "normal";
          this.customFields = data.customFields || {};
          this.metadata = data.metadata || {};
          this.timelineId = data.timelineId || null;

          this.validate();
        }

        validate() {
          if (!this.name) {
            throw new Error("Milestone name is required");
          }
          if (!this.date) {
            throw new Error("Milestone date is required");
          }
        }

        generateId() {
          return `milestone-${Date.now()}-${Math.random()
            .toString(36)
            .substr(2, 9)}`;
        }

        getColor() {
          if (this.color) return this.color;

          const colors = {
            review: "#FF9500",
            delivery: "#007AFF",
            approval: "#34C759",
            warning: "#FF3B30",
            info: "#5AC8FA",
          };

          return colors[this.type] || colors.info;
        }

        isOverdue() {
          if (this.status === "completed") return false;
          return this.date < DateUtils.today();
        }
      }

      class Timeline {
        constructor(data = {}) {
          this.id = data.id || this.generateId();
          this.name = data.name || "";
          this.description = data.description || "";
          this.color = data.color || "#4A90E2";
          this.type = data.type || "default";
          this.visible = data.visible !== false;
          this.order = data.order || 0;
          this.customFields = data.customFields || {};
          this.metadata = data.metadata || {};

          this.milestones = [];
          if (data.milestones && Array.isArray(data.milestones)) {
            this.milestones = data.milestones.map((m) =>
              m instanceof Milestone
                ? m
                : new Milestone({ ...m, timelineId: this.id })
            );
          }

          this.validate();
        }

        validate() {
          if (!this.name) {
            throw new Error("Timeline name is required");
          }
        }

        generateId() {
          return `timeline-${Date.now()}-${Math.random()
            .toString(36)
            .substr(2, 9)}`;
        }

        addMilestone(milestoneData) {
          const milestone =
            milestoneData instanceof Milestone
              ? milestoneData
              : new Milestone({ ...milestoneData, timelineId: this.id });

          this.milestones.push(milestone);
          this.sortMilestones();
          return milestone;
        }

        sortMilestones() {
          this.milestones.sort((a, b) => a.date - b.date);
        }

        getProgress() {
          if (this.milestones.length === 0) return 0;
          const completed = this.milestones.filter(
            (m) => m.status === "completed"
          ).length;
          return completed / this.milestones.length;
        }
      }

      class TaskItem {
        constructor(data = {}) {
          this.id = data.id || this.generateId();
          this.name = data.name || "";
          this.description = data.description || "";
          this.startDate = DateUtils.parseDate(data.startDate);
          this.endDate = DateUtils.parseDate(data.endDate);
          this.duration = data.duration || 0;
          this.progress = Math.max(0, Math.min(1, data.progress || 0));
          this.level = data.level || 0;
          this.parentId = data.parentId || null;
          this.children = data.children || [];
          this.collapsed = data.collapsed || false;
          this.status = data.status || "pending";
          this.priority = data.priority || "normal";
          this.type = data.type || "task";
          this.assignee = data.assignee || "";
          this.team = data.team || [];
          this.resources = data.resources || [];
          this.dependencies = data.dependencies || [];
          this.constraints = data.constraints || {};
          this.customFields = data.customFields || {};
          this.metadata = data.metadata || {};
          this.style = data.style || {};

          // 多时间线支持
          this.timelines = [];
          if (data.timelines && Array.isArray(data.timelines)) {
            this.timelines = data.timelines.map((t) =>
              t instanceof Timeline ? t : new Timeline(t)
            );
          }

          // 兼容旧版本里程碑数据
          if (
            data.milestones &&
            Array.isArray(data.milestones) &&
            this.timelines.length === 0
          ) {
            const defaultTimeline = new Timeline({
              name: "主要里程碑",
              type: "default",
              milestones: data.milestones,
            });
            this.timelines.push(defaultTimeline);
          }

          this.validate();
          this.calculateDuration();
        }

        validate() {
          if (!this.name) {
            throw new Error("Task name is required");
          }

          if (this.startDate && this.endDate && this.startDate > this.endDate) {
            throw new Error("Start date cannot be later than end date");
          }
        }

        generateId() {
          return `task-${Date.now()}-${Math.random()
            .toString(36)
            .substr(2, 9)}`;
        }

        calculateDuration() {
          if (this.startDate && this.endDate) {
            this.duration =
              DateUtils.getDaysDiff(this.startDate, this.endDate) + 1;
          }
        }

        getAllMilestones() {
          return this.timelines.reduce((all, timeline) => {
            return all.concat(timeline.milestones);
          }, []);
        }

        getOverallProgress() {
          if (this.timelines.length === 0) {
            return this.progress;
          }

          const timelineProgress =
            this.timelines.reduce((sum, timeline) => {
              return sum + timeline.getProgress();
            }, 0) / this.timelines.length;

          return this.progress * 0.7 + timelineProgress * 0.3;
        }

        isOverdue() {
          if (this.status === "completed") return false;
          if (!this.endDate) return false;
          return this.endDate < DateUtils.today();
        }
      }

      // 数据验证器
      class DataValidator {
        static validateGanttData(data) {
          const errors = [];
          const warnings = [];
          const taskIds = new Set();

          if (!Array.isArray(data)) {
            errors.push("数据必须是数组格式");
            return { errors, warnings, valid: false };
          }

          data.forEach((task, index) => {
            if (taskIds.has(task.id)) {
              errors.push(`任务 ${index + 1}: ID "${task.id}" 重复`);
            } else {
              taskIds.add(task.id);
            }

            const taskResult = DataValidator.validateTask(task);
            if (taskResult.errors.length > 0) {
              errors.push(`任务 ${index + 1}: ${taskResult.errors.join(", ")}`);
            }
            warnings.push(
              ...taskResult.warnings.map((w) => `任务 ${index + 1}: ${w}`)
            );
          });

          return { errors, warnings, valid: errors.length === 0 };
        }

        static validateTask(taskData) {
          const errors = [];
          const warnings = [];

          if (!taskData.name || taskData.name.trim() === "") {
            errors.push("任务名称不能为空");
          }

          if (taskData.startDate && taskData.endDate) {
            const start = DateUtils.parseDate(taskData.startDate);
            const end = DateUtils.parseDate(taskData.endDate);

            if (start && end && start > end) {
              errors.push("开始日期不能晚于结束日期");
            }
          }

          if (taskData.progress < 0 || taskData.progress > 1) {
            errors.push("进度值必须在 0-1 之间");
          }

          return { errors, warnings, valid: errors.length === 0 };
        }
      }

      // GanttChart.js - 升级版甘特图类
      class GanttChart extends EventEmitter {
        constructor(containerId, options = {}) {
          super();

          if (!containerId) {
            throw new Error("Container element is required");
          }

          this.container = document.getElementById(containerId);
          this.options = this.mergeDefaultOptions(options);

          // 使用新的数据结构
          this.data = [];
          if (options.data) {
            this.setData(options.data);
          }

          // DOM 元素引用
          this.elements = {};

          // 状态管理
          this.state = {
            viewMode: this.options.viewMode,
            selectedTasks: new Set(),
            scrollPosition: { x: 0, y: 0 },
            timeScale: null,
          };

          this.init();
        }

        mergeDefaultOptions(options) {
          const defaults = {
            taskMapping: {
              id: "id",
              startDate: "start",
              endDate: "end",
              label: "label",
              duration: "duration",
              progress: "progress",
              type: "type",
              style: "style",
              collapsed: "collapsed",
              parentId: "parentId",
            },

            startDate: new Date("2024-01-01"),
            endDate: new Date("2024-03-31"),
            viewMode: "day",
            rowHeight: 40,

            taskList: {
              display: true,
              columns: [
                { key: "name", title: "任务名称", width: 200 },
                { key: "assignee", title: "负责人", width: 100 },
              ],
            },

            theme: "default",
            colors: {
              primary: "#4A90E2",
              success: "#7ED321",
              warning: "#F5A623",
              danger: "#D0021B",
            },

            enableVirtualScroll: true,
            enableMilestones: true,
            enableDragDrop: false,

            onTaskClick: null,
            onMilestoneClick: null,
            onDataChange: null,
          };

          return { ...defaults, ...options };
        }

        // 设置数据 - 使用新的数据结构
        setData(data) {
          // 验证数据
          const validation = DataValidator.validateGanttData(data);
          if (!validation.valid) {
            console.warn("数据验证失败:", validation.errors);
          }

          // 转换为 TaskItem 实例
          this.data = data.map((taskData) => {
            if (taskData instanceof TaskItem) {
              return taskData;
            }
            return new TaskItem(taskData);
          });

          // 触发数据变化事件
          this.emit("dataChange", this.data);

          // 重新渲染
          if (this.elements.tableBody) {
            this.renderTable();
            this.renderChart();
            this.updateStatus();
          }

          console.log("数据已更新，任务数量:", this.data.length);
          return this;
        }

        // 生成示例数据 - 使用新的数据结构
        generateSampleData() {
          return [
            new TaskItem({
              id: "task-1",
              name: "项目启动",
              // startDate: "2024-01-01",
              // endDate: "2024-01-15",
              // progress: 1.0,
              assignee: "张三",
              level: 0,
              // status: "completed",
              timelines: [
                new Timeline({
                  name: "技术里程碑",
                  type: "technical",
                  milestones: [
                    new Milestone({
                      name: "项目启动会",
                      date: "2024-01-02",
                      type: "review",
                      status: "completed",
                    }),
                    new Milestone({
                      name: "技术评审会",
                      date: "2024-01-12",
                      type: "review",
                      status: "completed",
                    }),
                  ],
                }),
                new Timeline({
                  name: "技术里程碑1",
                  type: "technical",
                  milestones: [
                    new Milestone({
                      name: "项目启动会",
                      date: "2024-01-02",
                      type: "review",
                      status: "completed",
                    }),
                    new Milestone({
                      name: "技术评审会",
                      date: "2024-01-12",
                      type: "review",
                      status: "completed",
                    }),
                  ],
                }),
              ],
            }),
            new TaskItem({
              id: "task-2",
              name: "需求分析",
              startDate: "2024-01-06",
              endDate: "2024-01-20",
              progress: 0.8,
              assignee: "李四",
              level: 1,
              status: "in-progress",
              timelines: [
                new Timeline({
                  name: "业务里程碑",
                  type: "business",
                  milestones: [
                    new Milestone({
                      name: "需求评审",
                      date: "2024-01-15",
                      type: "review",
                      status: "completed",
                    }),
                  ],
                }),
              ],
            }),
            new TaskItem({
              id: "task-3",
              name: "系统设计",
              startDate: "2024-01-21",
              endDate: "2024-02-10",
              progress: 0.6,
              assignee: "王五",
              level: 1,
              status: "in-progress",
              timelines: [
                new Timeline({
                  name: "技术里程碑",
                  milestones: [
                    new Milestone({
                      name: "架构评审",
                      date: "2024-02-05",
                      type: "review",
                      status: "pending",
                    }),
                  ],
                }),
              ],
            }),
            new TaskItem({
              id: "task-4",
              name: "开发实现",
              startDate: "2024-02-11",
              endDate: "2024-03-15",
              progress: 0.3,
              assignee: "赵六",
              level: 0,
              status: "in-progress",
              timelines: [
                new Timeline({
                  name: "交付里程碑",
                  milestones: [
                    new Milestone({
                      name: "代码评审",
                      date: "2024-03-01",
                      type: "review",
                      status: "pending",
                    }),
                    new Milestone({
                      name: "功能交付",
                      date: "2024-03-10",
                      type: "delivery",
                      status: "pending",
                    }),
                  ],
                }),
              ],
            }),
            new TaskItem({
              id: "task-5",
              name: "测试验收",
              startDate: "2024-03-16",
              endDate: "2024-03-30",
              progress: 0.0,
              assignee: "孙七",
              level: 0,
              status: "pending",
              timelines: [
                new Timeline({
                  name: "验收里程碑",
                  milestones: [
                    new Milestone({
                      name: "测试完成",
                      date: "2024-03-25",
                      type: "approval",
                      status: "pending",
                    }),
                  ],
                }),
              ],
            }),
          ];
        }

        init() {
          console.log("Initializing GanttChart with Step 2 enhancements...");

          // 如果没有数据，生成示例数据
          if (this.data.length === 0) {
            this.data = this.generateSampleData();
          }

          this.createLayout();
          this.renderTable();
          this.renderChart();
          this.bindEvents();
          this.updateStatus();

          this.emit("ready");
          console.log(
            "GanttChart initialized successfully with multi-timeline support!"
          );
        }

        createLayout() {
          this.container.innerHTML = "";
          this.container.className = "gantt-container";

          const layout = `
                    <div class="gantt-header">
                        <div class="gantt-toolbar">
                            <div class="gantt-view-controls">
                                <button class="gantt-btn ${
                                  this.state.viewMode === "day" ? "active" : ""
                                }" data-view="day">日</button>
                                <button class="gantt-btn ${
                                  this.state.viewMode === "week" ? "active" : ""
                                }" data-view="week">周</button>
                                <button class="gantt-btn ${
                                  this.state.viewMode === "month"
                                    ? "active"
                                    : ""
                                }" data-view="month">月</button>
                                <button class="gantt-btn ${
                                  this.state.viewMode === "quarter"
                                    ? "active"
                                    : ""
                                }" data-view="quarter">季度</button>
                            </div>
                            <div class="gantt-actions">
                                <button class="gantt-btn" id="gantt-zoom-in">放大</button>
                                <button class="gantt-btn" id="gantt-zoom-out">缩小</button>
                                <button class="gantt-btn" id="gantt-fit-view">适应窗口</button>
                                <button class="gantt-btn" id="gantt-refresh">刷新</button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="gantt-main">
                        <div class="gantt-left-panel">
                            <div class="gantt-table-header"></div>
                            <div class="gantt-table-body"></div>
                        </div>
                        
                        <div class="gantt-splitter"></div>
                        
                        <div class="gantt-right-panel">
                            <div class="gantt-timeline-header">时间轴 (${
                              this.state.viewMode
                            }视图) - 支持多时间线里程碑</div>
                            <div class="gantt-chart-body">
                                <svg class="gantt-svg">
                                    <defs>
                                        <linearGradient id="taskGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                                            <stop offset="0%" style="stop-color:${
                                              this.options.colors.primary
                                            };stop-opacity:1" />
                                            <stop offset="100%" style="stop-color:${
                                              this.options.colors.primary
                                            };stop-opacity:0.8" />
                                        </linearGradient>
                                        
                                        <linearGradient id="progressGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                                            <stop offset="0%" style="stop-color:${
                                              this.options.colors.success
                                            };stop-opacity:1" />
                                            <stop offset="100%" style="stop-color:${
                                              this.options.colors.success
                                            };stop-opacity:0.9" />
                                        </linearGradient>
                                        
                                        <pattern id="gridPattern" width="30" height="40" patternUnits="userSpaceOnUse">
                                            <rect width="30" height="40" fill="none" stroke="#f0f0f0" stroke-width="1"/>
                                        </pattern>
                                    </defs>
                                    
                                    <g class="gantt-grid-layer"></g>
                                    <g class="gantt-tasks-layer"></g>
                                    <g class="gantt-milestones-layer"></g>
                                    <g class="gantt-dependencies-layer"></g>
                                </svg>
                            </div>
                        </div>
                    </div>
                    
                    <div class="gantt-footer">
                        <div class="gantt-status">
                            <span>任务数: ${this.data.length}</span>
                            <span>视图: ${this.state.viewMode}</span>
                            <span>状态: 就绪</span>
                        </div>
                    </div>
                `;

          this.container.innerHTML = layout;

          // 保存关键DOM引用
          this.elements = {
            header: this.container.querySelector(".gantt-header"),
            toolbar: this.container.querySelector(".gantt-toolbar"),
            leftPanel: this.container.querySelector(".gantt-left-panel"),
            rightPanel: this.container.querySelector(".gantt-right-panel"),
            tableHeader: this.container.querySelector(".gantt-table-header"),
            tableBody: this.container.querySelector(".gantt-table-body"),
            timelineHeader: this.container.querySelector(
              ".gantt-timeline-header"
            ),
            chartBody: this.container.querySelector(".gantt-chart-body"),
            svg: this.container.querySelector(".gantt-svg"),
            footer: this.container.querySelector(".gantt-footer"),
            splitter: this.container.querySelector(".gantt-splitter"),
            status: this.container.querySelector(".gantt-status"),
          };

          console.log("Layout created with Step 2 enhancements");
        }

        renderTable() {
          // 渲染表头
          const headerHtml = this.options.taskList.columns
            .map(
              (col) =>
                `<div class="gantt-table-header-cell" style="width: ${col.width}px">${col.title}</div>`
            )
            .join("");
          this.elements.tableHeader.innerHTML = headerHtml;

          // 渲染表格行 - 支持多时间线显示
          const rowsHtml = this.data
            .map((task, index) => {
              const cellsHtml = this.options.taskList.columns
                .map((col) => {
                  let value = "";
                  let style = `width: ${col.width}px`;

                  if (col.key === "name") {
                    const indent = (task.level || 0) * 20;
                    style += `; padding-left: ${indent + 12}px`;

                    // 显示时间线数量
                    const timelineCount = task.timelines.length;
                    const milestoneCount = task.getAllMilestones().length;

                    value = `${task.name}`;
                    if (timelineCount > 0) {
                      value += ` <small class="text-muted">(${timelineCount}线 ${milestoneCount}碑)</small>`;
                    }
                  } else if (col.key === "assignee") {
                    value = task.assignee || "";
                  } else if (col.key === "startDate") {
                    value = task.startDate
                      ? DateUtils.formatDate(task.startDate, "MM/DD")
                      : "";
                  } else if (col.key === "duration") {
                    value = `${task.duration}天`;
                  } else if (col.key === "progress") {
                    const progress = Math.round(
                      task.getOverallProgress() * 100
                    );
                    value = `${progress}%`;
                  } else {
                    value = task[col.key] || "";
                  }

                  return `<div class="gantt-table-cell" style="${style}">${value}</div>`;
                })
                .join("");

              const statusClass = `status-${task.status}`;
              return `<div class="gantt-table-row ${statusClass}" data-task-id="${task.id}">${cellsHtml}</div>`;
            })
            .join("");

          this.elements.tableBody.innerHTML = rowsHtml;
          console.log("Table rendered with multi-timeline info");
        }

        renderChart() {
          const svg = this.elements.svg;

          // 清除现有内容
          const layers = [
            "gantt-grid-layer",
            "gantt-tasks-layer",
            "gantt-milestones-layer",
          ];
          layers.forEach((layerClass) => {
            const layer = svg.querySelector(`.${layerClass}`);
            if (layer) layer.innerHTML = "";
          });

          // 计算 SVG 尺寸
          const totalHeight = this.data.length * this.options.rowHeight;
          svg.style.height = `${Math.max(totalHeight, 400)}px`;

          // 生成时间刻度
          this.generateTimeScale();

          // 渲染各层
          this.renderGrid();
          this.renderTaskBars();
          this.renderMilestones();

          console.log("Chart rendered with multi-timeline support");
        }

        generateTimeScale() {
          // 计算项目的实际时间范围
          let minDate = this.options.startDate;
          let maxDate = this.options.endDate;

          this.data.forEach((task) => {
            if (task.startDate && (!minDate || task.startDate < minDate)) {
              minDate = task.startDate;
            }
            if (task.endDate && (!maxDate || task.endDate > maxDate)) {
              maxDate = task.endDate;
            }
          });

          // 生成时间刻度
          this.state.timeScale = DateUtils.generateTimeScale(
            minDate,
            maxDate,
            this.state.viewMode
          );

          // 计算每个刻度的 X 坐标
          const scaleWidth = this.getScaleWidth();
          this.state.timeScale.forEach((scale, index) => {
            scale.x = index * scaleWidth;
          });
        }

        getScaleWidth() {
          switch (this.state.viewMode) {
            case "day":
              return 30;
            case "week":
              return 100;
            case "month":
              return 120;
            case "quarter":
              return 200;
            default:
              return 30;
          }
        }

        renderGrid() {
          const gridLayer =
            this.elements.svg.querySelector(".gantt-grid-layer");
          const totalHeight = this.data.length * this.options.rowHeight;

          // 背景网格
          const background = document.createElementNS(
            "http://www.w3.org/2000/svg",
            "rect"
          );
          background.setAttribute("width", "100%");
          background.setAttribute("height", totalHeight);
          background.setAttribute("fill", "url(#gridPattern)");
          gridLayer.appendChild(background);

          // 时间刻度线
          if (this.state.timeScale) {
            this.state.timeScale.forEach((scale) => {
              const line = SvgUtils.createElement("line", {
                x1: scale.x,
                y1: 0,
                x2: scale.x,
                y2: totalHeight,
                stroke: "#e1e5e9",
                "stroke-width": 1,
              });
              gridLayer.appendChild(line);
            });
          }
        }

        renderTaskBars() {
          const tasksLayer =
            this.elements.svg.querySelector(".gantt-tasks-layer");
          const scaleWidth = this.getScaleWidth();

          this.data.forEach((task, index) => {
            const y = index * this.options.rowHeight;

            // 计算任务条位置和宽度
            let x = 50;
            let width =
              task.duration *
              (scaleWidth / (this.state.viewMode === "day" ? 1 : 7));

            // 如果有时间刻度，使用精确计算
            if (this.state.timeScale && task.startDate) {
              const startScale = this.state.timeScale.find(
                (s) =>
                  DateUtils.formatDate(s.date, "YYYY-MM-DD") ===
                  DateUtils.formatDate(task.startDate, "YYYY-MM-DD")
              );
              if (startScale) {
                x = startScale.x;
              }
            }

            // 创建任务条
            const taskBar = SvgUtils.createTaskBar(x, y + 8, width, 24, {
              id: task.id,
              status: task.status,
              progress: task.getOverallProgress(),
              text: task.name,
              fill: this.getTaskColor(task),
              progressFill: this.getProgressColor(task),
            });

            tasksLayer.appendChild(taskBar);
          });
        }

        renderMilestones() {
          const milestonesLayer = this.elements.svg.querySelector(
            ".gantt-milestones-layer"
          );
          const scaleWidth = this.getScaleWidth();

          this.data.forEach((task, index) => {
            const y = index * this.options.rowHeight;

            // 渲染任务的所有里程碑
            task.getAllMilestones().forEach((milestone) => {
              if (!milestone.date) return;

              // 计算里程碑位置
              let x = 100;

              if (this.state.timeScale) {
                const milestoneScale = this.state.timeScale.find(
                  (s) =>
                    DateUtils.formatDate(s.date, "YYYY-MM-DD") ===
                    DateUtils.formatDate(milestone.date, "YYYY-MM-DD")
                );
                if (milestoneScale) {
                  x = milestoneScale.x;
                }
              }

              // 创建里程碑标记
              const milestoneMarker = SvgUtils.createMilestone(
                x,
                y + 20,
                milestone.icon,
                {
                  id: milestone.id,
                  status: milestone.type,
                  showLine: true,
                  lineHeight: 16,
                }
              );

              milestonesLayer.appendChild(milestoneMarker);
            });
          });
        }

        getTaskColor(task) {
          const colors = {
            completed: "#7ED321",
            "in-progress": "#4A90E2",
            pending: "#9CA3AF",
            overdue: "#D0021B",
          };
          return colors[task.status] || colors["pending"];
        }

        getProgressColor(task) {
          const progress = task.getOverallProgress();
          if (progress >= 1.0) return "#7ED321";
          if (progress >= 0.5) return "#4A90E2";
          return "#F5A623";
        }

        bindEvents() {
          // 视图切换
          this.elements.toolbar.addEventListener("click", (e) => {
            if (e.target.hasAttribute("data-view")) {
              const newViewMode = e.target.getAttribute("data-view");
              this.changeViewMode(newViewMode);
            }

            // 工具栏按钮
            if (e.target.id === "gantt-refresh") {
              this.refresh();
            }
          });

          // 滚动同步
          this.elements.tableBody.addEventListener("scroll", (e) => {
            this.elements.chartBody.scrollTop = e.target.scrollTop;
            this.state.scrollPosition.y = e.target.scrollTop;
          });

          this.elements.chartBody.addEventListener("scroll", (e) => {
            this.elements.tableBody.scrollTop = e.target.scrollTop;
            this.state.scrollPosition.x = e.target.scrollLeft;
            this.state.scrollPosition.y = e.target.scrollTop;
          });

          // SVG 点击事件
          this.elements.svg.addEventListener("click", (e) => {
            const taskBar = e.target.closest("[data-task-id]");
            if (taskBar) {
              this.selectTask(taskBar.dataset.taskId);
              this.emit("taskClick", taskBar.dataset.taskId);
            }

            const milestone = e.target.closest("[data-milestone-id]");
            if (milestone) {
              this.selectMilestone(milestone.dataset.milestoneId);
              this.emit("milestoneClick", milestone.dataset.milestoneId);
            }
          });

          // 窗口大小变化
          window.addEventListener("resize", () => {
            this.handleResize();
          });

          // 分割器拖拽
          this.bindSplitterEvents();

          console.log("Events bound with Step 2 enhancements");
        }

        bindSplitterEvents() {
          let isDragging = false;

          this.elements.splitter.addEventListener("mousedown", (e) => {
            isDragging = true;
            document.body.style.cursor = "col-resize";
            e.preventDefault();
          });

          document.addEventListener("mousemove", (e) => {
            if (isDragging) {
              const containerRect = this.container.getBoundingClientRect();
              const newWidth = e.clientX - containerRect.left - 20;
              if (newWidth >= 200 && newWidth <= 600) {
                this.elements.leftPanel.style.width = `${newWidth}px`;
              }
            }
          });

          document.addEventListener("mouseup", () => {
            if (isDragging) {
              isDragging = false;
              document.body.style.cursor = "";
            }
          });
        }

        changeViewMode(newViewMode) {
          if (newViewMode === this.state.viewMode) return;

          this.state.viewMode = newViewMode;

          // 更新按钮状态
          this.elements.toolbar
            .querySelectorAll("[data-view]")
            .forEach((btn) => {
              btn.classList.toggle(
                "active",
                btn.getAttribute("data-view") === newViewMode
              );
            });

          // 更新头部显示
          this.elements.timelineHeader.textContent = `时间轴 (${newViewMode}视图) - 支持多时间线里程碑`;

          // 重新渲染图表
          this.renderChart();

          this.emit("viewChange", newViewMode);
          this.showMessage(`已切换到${newViewMode}视图`);
        }

        selectTask(taskId) {
          // 清除之前的选择
          this.elements.tableBody
            .querySelectorAll(".gantt-table-row")
            .forEach((row) => {
              row.classList.remove("selected");
            });
          this.elements.svg.querySelectorAll(".task-bar").forEach((bar) => {
            bar.classList.remove("selected");
          });

          // 添加新选择
          const tableRow = this.elements.tableBody.querySelector(
            `[data-task-id="${taskId}"]`
          );
          const taskBar = this.elements.svg.querySelector(
            `[data-task-id="${taskId}"]`
          );

          if (tableRow) tableRow.classList.add("selected");
          if (taskBar) taskBar.classList.add("selected");

          // 更新状态
          this.state.selectedTasks.clear();
          this.state.selectedTasks.add(taskId);

          console.log("Task selected:", taskId);
        }

        selectMilestone(milestoneId) {
          console.log("Milestone selected:", milestoneId);

          // 找到里程碑对应的任务
          const task = this.data.find((t) =>
            t.getAllMilestones().some((m) => m.id === milestoneId)
          );

          if (task) {
            this.selectTask(task.id);
          }
        }

        refresh() {
          console.log("Refreshing gantt chart with Step 2 data...");
          this.renderTable();
          this.renderChart();
          this.updateStatus();
          this.showMessage("甘特图已刷新");
        }

        updateStatus() {
          const completedTasks = this.data.filter(
            (task) => task.status === "completed"
          ).length;
          const inProgressTasks = this.data.filter(
            (task) => task.status === "in-progress"
          ).length;
          const pendingTasks = this.data.filter(
            (task) => task.status === "pending"
          ).length;

          // 统计里程碑信息
          const totalMilestones = this.data.reduce(
            (sum, task) => sum + task.getAllMilestones().length,
            0
          );
          const totalTimelines = this.data.reduce(
            (sum, task) => sum + task.timelines.length,
            0
          );

          this.elements.status.innerHTML = `
                    <span>总任务: ${this.data.length}</span>
                    <span>已完成: ${completedTasks}</span>
                    <span>进行中: ${inProgressTasks}</span>
                    <span>待开始: ${pendingTasks}</span>
                    <span>时间线: ${totalTimelines}</span>
                    <span>里程碑: ${totalMilestones}</span>
                    <span>视图: ${this.state.viewMode}</span>
                `;
        }

        handleResize() {
          console.log("Window resized - adjusting layout");
          setTimeout(() => {
            this.renderChart();
          }, 100);
        }

        showMessage(message) {
          // 创建消息元素
          const messageEl = document.createElement("div");
          messageEl.className = "gantt-message";
          messageEl.textContent = message;
          document.body.appendChild(messageEl);

          // 显示动画
          setTimeout(() => {
            messageEl.classList.add("show");
          }, 100);

          // 自动隐藏
          setTimeout(() => {
            messageEl.classList.remove("show");
            setTimeout(() => {
              if (messageEl.parentNode) {
                messageEl.parentNode.removeChild(messageEl);
              }
            }, 300);
          }, 3000);
        }

        // 添加任务
        addTask(taskData) {
          const task =
            taskData instanceof TaskItem ? taskData : new TaskItem(taskData);
          this.data.push(task);
          this.emit("dataChange", this.data);
          this.refresh();
          return task;
        }

        // 删除任务
        removeTask(taskId) {
          const index = this.data.findIndex((task) => task.id === taskId);
          if (index !== -1) {
            const removedTask = this.data.splice(index, 1)[0];
            this.emit("dataChange", this.data);
            this.refresh();
            return removedTask;
          }
          return null;
        }

        // 获取任务
        getTask(taskId) {
          return this.data.find((task) => task.id === taskId);
        }

        // 更新任务
        updateTask(taskId, updates) {
          const task = this.getTask(taskId);
          if (task) {
            Object.assign(task, updates);
            if (task.startDate && task.endDate) {
              task.calculateDuration();
            }
            this.emit("dataChange", this.data);
            this.refresh();
            return task;
          }
          return null;
        }

        // 添加里程碑到任务
        addMilestone(taskId, timelineId, milestoneData) {
          const task = this.getTask(taskId);
          if (task) {
            const timeline = task.getTimeline(timelineId);
            if (timeline) {
              const milestone = timeline.addMilestone(milestoneData);
              this.refresh();
              return milestone;
            }
          }
          return null;
        }

        // 添加时间线到任务
        addTimeline(taskId, timelineData) {
          const task = this.getTask(taskId);
          if (task) {
            const timeline = task.addTimeline(timelineData);
            this.refresh();
            return timeline;
          }
          return null;
        }

        // 导出数据
        exportData() {
          return this.data.map((task) => (task.toJSON ? task.toJSON() : task));
        }

        // 获取统计信息
        getStats() {
          const stats = {
            totalTasks: this.data.length,
            completedTasks: 0,
            inProgressTasks: 0,
            pendingTasks: 0,
            overdueTasks: 0,
            totalTimelines: 0,
            totalMilestones: 0,
            completedMilestones: 0,
            overdueMilestones: 0,
          };

          this.data.forEach((task) => {
            // 任务统计
            switch (task.status) {
              case "completed":
                stats.completedTasks++;
                break;
              case "in-progress":
                stats.inProgressTasks++;
                break;
              case "pending":
                stats.pendingTasks++;
                break;
            }

            if (task.isOverdue()) {
              stats.overdueTasks++;
            }

            // 时间线和里程碑统计
            stats.totalTimelines += task.timelines.length;

            task.getAllMilestones().forEach((milestone) => {
              stats.totalMilestones++;
              if (milestone.status === "completed") {
                stats.completedMilestones++;
              }
              if (milestone.isOverdue()) {
                stats.overdueMilestones++;
              }
            });
          });

          return stats;
        }

        // 销毁
        destroy() {
          // 清理事件监听器
          window.removeEventListener("resize", this.handleResize);

          // 清理DOM
          this.container.innerHTML = "";

          // 清理数据
          this.data = [];
          this.elements = {};
          this.state = null;

          // 调用父类销毁方法
          super.removeAllListeners();

          console.log("GanttChart destroyed");
        }
      }

      // 全局工具函数
      window.GanttUtils = {
        DateUtils,
        EventEmitter,
        SvgUtils,
        TaskItem,
        Timeline,
        Milestone,
        DataValidator,
      };
    </script>

    <script>
      // 初始化演示
      document.addEventListener("DOMContentLoaded", function () {
        console.log("=== 甘特图 Step 2 集成版演示 ===");

        // 创建甘特图实例
        const gantt = new GanttChart("gantt-demo", {
          viewMode: "day",
          startDate: new Date("2024-01-01"),
          endDate: new Date("2024-03-31"),
          taskList: {
            display: true,
            columns: [
              { key: "name", title: "任务名称", width: 200 },
              { key: "assignee", title: "负责人", width: 100 },
              { key: "progress", title: "进度", width: 80 },
            ],
          },
          enableMilestones: true,
          onTaskClick: (taskId) => {
            const task = gantt.getTask(taskId);
            console.log("任务点击:", task?.name);
          },
          onMilestoneClick: (milestoneId) => {
            console.log("里程碑点击:", milestoneId);
          },
        });

        // 监听事件
        gantt.on("ready", () => {
          console.log("✅ 甘特图初始化完成");
          console.log("📊 统计信息:", gantt.getStats());

          // 显示功能说明
          setTimeout(() => {
            gantt.showMessage("🎉 Step 2 完成：支持多时间线里程碑系统！");
          }, 1000);
        });

        gantt.on("viewChange", (viewMode) => {
          console.log("📅 视图已切换到:", viewMode);
        });

        gantt.on("taskClick", (taskId) => {
          const task = gantt.getTask(taskId);
          if (task) {
            console.log("🎯 选中任务:", {
              name: task.name,
              timelines: task.timelines.length,
              milestones: task.getAllMilestones().length,
              progress: Math.round(task.getOverallProgress() * 100) + "%",
            });
          }
        });

        // 演示数据操作
        setTimeout(() => {
          console.log("\n=== 演示 Step 2 新功能 ===");

          // 1. 添加新任务with多时间线
          const newTask = gantt.addTask({
            name: "演示任务",
            // startDate: "2024-02-15",
            // endDate: "2024-02-28",
            assignee: "演示用户",
            progress: 0.4,
            timelines: [
              {
                name: "技术时间线",
                type: "technical",
                milestones: [
                  {
                    name: "技术评审",
                    date: "2024-02-20",
                    type: "review",
                  },
                ],
              },
              {
                name: "业务时间线",
                type: "business",
                milestones: [
                  {
                    name: "业务验收",
                    date: "2024-02-25",
                    type: "approval",
                  },
                ],
              },
            ],
          });

          console.log("✅ 添加了新任务:", newTask.name);
          console.log("📈 更新后统计:", gantt.getStats());

          gantt.showMessage("📝 已添加演示任务（含2条时间线）");
        }, 3000);

        // 暴露到全局供调试
        window.ganttDemo = gantt;

        console.log("🔧 调试提示:");
        console.log("- 使用 window.ganttDemo 访问甘特图实例");
        console.log("- 使用 window.GanttUtils 访问工具类");
        console.log("- 点击任务条或里程碑查看详细信息");
      });
    </script>
  </body>
</html>

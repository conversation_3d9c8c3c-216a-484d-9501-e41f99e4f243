<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Step3: 甘特图风格表格头部演示</title>
    <link rel="stylesheet" href="../../src/styles/gantt.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }

        .header h1 {
            margin: 0;
            font-size: 24px;
        }

        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
        }

        .demo-info {
            padding: 20px;
            background: linear-gradient(135deg, #fef7e6 0%, #fef3c7 100%);
            border-bottom: 2px solid #f59e0b;
        }

        .demo-info h3 {
            margin: 0 0 15px 0;
            color: #92400e;
            font-size: 18px;
        }

        .color-palette {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }

        .color-item {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #f59e0b;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .color-item h4 {
            margin: 0 0 10px 0;
            color: #92400e;
            font-size: 14px;
        }

        .color-swatch {
            display: flex;
            gap: 5px;
            margin-bottom: 8px;
        }

        .color-box {
            width: 20px;
            height: 20px;
            border-radius: 4px;
            border: 1px solid rgba(0,0,0,0.1);
        }

        .color-description {
            font-size: 12px;
            color: #6b7280;
            line-height: 1.4;
        }

        .gantt-container {
            height: 600px;
            position: relative;
        }

        .controls {
            padding: 20px;
            background: #f9fafb;
            border-top: 1px solid #e5e7eb;
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }

        .control-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .control-group label {
            font-size: 12px;
            font-weight: 600;
            color: #92400e;
            text-transform: uppercase;
        }

        .control-group button,
        .control-group select {
            padding: 8px 16px;
            border: 1px solid #d97706;
            border-radius: 6px;
            background: white;
            color: #92400e;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .control-group button:hover,
        .control-group select:hover {
            background: #f59e0b;
            color: white;
        }

        .theme-toggle {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 10px 15px;
            background: #92400e;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            z-index: 1000;
        }

        .theme-toggle:hover {
            background: #78350f;
        }

        /* 深色主题切换 */
        .dark-theme {
            filter: invert(1) hue-rotate(180deg);
        }

        .dark-theme .gantt-container {
            filter: invert(1) hue-rotate(180deg);
        }

        .comparison-section {
            padding: 20px;
            background: #f8f9fa;
            border-top: 1px solid #e5e7eb;
        }

        .comparison-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 15px;
        }

        .comparison-item {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border: 2px solid #e5e7eb;
        }

        .comparison-item h4 {
            margin: 0 0 10px 0;
            color: #374151;
        }

        .comparison-item.before {
            border-color: #6b7280;
        }

        .comparison-item.after {
            border-color: #f59e0b;
        }

        .feature-highlight {
            background: linear-gradient(135deg, #fef7e6 0%, #fef3c7 100%);
            padding: 10px;
            border-radius: 6px;
            margin: 10px 0;
            border-left: 4px solid #f59e0b;
        }

        .feature-highlight h5 {
            margin: 0 0 5px 0;
            color: #92400e;
            font-size: 13px;
        }

        .feature-highlight p {
            margin: 0;
            font-size: 12px;
            color: #78350f;
        }
    </style>
</head>
<body>
    <button class="theme-toggle" onclick="toggleTheme()">🌙 切换深色主题</button>

    <div class="container">
        <div class="header">
            <h1>🎨 甘特图风格表格头部演示</h1>
            <p>展示专业的甘特图任务列表头部配色方案和视觉效果</p>
        </div>

        <div class="demo-info">
            <h3>甘特图专用配色方案</h3>
            <div class="color-palette">
                <div class="color-item">
                    <h4>主色调 (Primary)</h4>
                    <div class="color-swatch">
                        <div class="color-box" style="background: #fef7e6;"></div>
                        <div class="color-box" style="background: #fde4a7;"></div>
                        <div class="color-box" style="background: #f59e0b;"></div>
                        <div class="color-box" style="background: #92400e;"></div>
                    </div>
                    <div class="color-description">温暖的橙黄色系，体现项目管理的活力和专业性</div>
                </div>
                <div class="color-item">
                    <h4>辅助色调 (Secondary)</h4>
                    <div class="color-swatch">
                        <div class="color-box" style="background: #f8fafc;"></div>
                        <div class="color-box" style="background: #e2e8f0;"></div>
                        <div class="color-box" style="background: #94a3b8;"></div>
                        <div class="color-box" style="background: #475569;"></div>
                    </div>
                    <div class="color-description">中性灰色系，提供良好的对比度和可读性</div>
                </div>
                <div class="color-item">
                    <h4>状态色调 (Status)</h4>
                    <div class="color-swatch">
                        <div class="color-box" style="background: #10b981;"></div>
                        <div class="color-box" style="background: #f59e0b;"></div>
                        <div class="color-box" style="background: #ef4444;"></div>
                    </div>
                    <div class="color-description">成功、警告、错误状态的标准配色</div>
                </div>
            </div>
        </div>

        <div class="gantt-container" id="ganttContainer"></div>

        <div class="controls">
            <div class="control-group">
                <label>视图模式</label>
                <select id="viewModeSelect">
                    <option value="day">日视图</option>
                    <option value="week">周视图</option>
                    <option value="month">月视图</option>
                    <option value="quarter">季度视图</option>
                </select>
            </div>
            <div class="control-group">
                <label>测试排序</label>
                <button id="testSortBtn">随机排序</button>
            </div>
            <div class="control-group">
                <label>列宽调整</label>
                <button id="resetColumnsBtn">重置列宽</button>
            </div>
            <div class="control-group">
                <label>数据操作</label>
                <button id="refreshDataBtn">刷新数据</button>
            </div>
        </div>

        <div class="comparison-section">
            <h3>设计对比</h3>
            <div class="comparison-grid">
                <div class="comparison-item before">
                    <h4>优化前 - 通用蓝色主题</h4>
                    <div class="feature-highlight">
                        <h5>特点</h5>
                        <p>使用标准的蓝色系配色，缺乏甘特图的专业特色</p>
                    </div>
                    <ul style="font-size: 13px; color: #6b7280;">
                        <li>通用的蓝色渐变背景</li>
                        <li>标准的交互状态颜色</li>
                        <li>缺乏项目管理工具的视觉特征</li>
                    </ul>
                </div>
                <div class="comparison-item after">
                    <h4>优化后 - 甘特图专用主题</h4>
                    <div class="feature-highlight">
                        <h5>特点</h5>
                        <p>采用温暖的橙黄色系，体现专业的项目管理工具特色</p>
                    </div>
                    <ul style="font-size: 13px; color: #78350f;">
                        <li>专业的橙黄色渐变背景</li>
                        <li>符合甘特图特色的配色方案</li>
                        <li>增强的视觉层次和专业感</li>
                        <li>更好的品牌识别度</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script type="module">
        import GanttChart from '../../src/GanttChart.js';
        import DateUtils from '../../src/utils/DateUtils.js';

        // 生成演示数据
        function generateDemoData() {
            const tasks = [];
            const startDate = new Date('2024-01-01');
            const priorities = ['高', '中', '低'];
            const statuses = ['计划中', '进行中', '已完成', '延期'];
            const types = ['开发', '设计', '测试', '部署', '文档'];
            
            for (let i = 0; i < 20; i++) {
                const taskStart = DateUtils.addDays(startDate, i * 12);
                const duration = 8 + Math.random() * 20;
                const taskEnd = DateUtils.addDays(taskStart, duration);
                
                tasks.push({
                    id: `gantt-task-${i}`,
                    name: `甘特图项目任务 ${i + 1}`,
                    startDate: taskStart,
                    endDate: taskEnd,
                    progress: Math.random(),
                    priority: priorities[Math.floor(Math.random() * priorities.length)],
                    status: statuses[Math.floor(Math.random() * statuses.length)],
                    type: types[Math.floor(Math.random() * types.length)],
                    assignee: `项目成员${i + 1}`,
                    budget: Math.floor(Math.random() * 50000) + 10000
                });
            }
            
            return tasks;
        }

        // 初始化甘特图
        let ganttInstance = null;

        function initializeGantt() {
            const container = document.getElementById('ganttContainer');
            container.innerHTML = '';
            
            const demoData = generateDemoData();
            
            ganttInstance = new GanttChart('ganttContainer', {
                data: demoData,
                viewMode: 'day',
                pixelsPerDay: 40,
                taskList: {
                    columns: [
                        { 
                            key: 'name', 
                            title: '任务名称', 
                            width: 220,
                            minWidth: 180,
                            maxWidth: 350,
                            icon: '📋',
                            description: '项目任务的详细名称'
                        },
                        { 
                            key: 'type', 
                            title: '类型', 
                            width: 80,
                            minWidth: 60,
                            maxWidth: 120,
                            icon: '🏷️',
                            description: '任务类型分类'
                        },
                        { 
                            key: 'priority', 
                            title: '优先级', 
                            width: 90,
                            minWidth: 70,
                            maxWidth: 130,
                            icon: '⚡',
                            description: '任务优先级等级'
                        },
                        { 
                            key: 'status', 
                            title: '状态', 
                            width: 100,
                            minWidth: 80,
                            maxWidth: 140,
                            icon: '📊',
                            description: '当前执行状态'
                        },
                        { 
                            key: 'assignee', 
                            title: '负责人', 
                            width: 110,
                            minWidth: 90,
                            maxWidth: 150,
                            icon: '👤',
                            description: '任务负责人员'
                        },
                        { 
                            key: 'progress', 
                            title: '进度', 
                            width: 80,
                            minWidth: 60,
                            maxWidth: 100,
                            icon: '📈',
                            description: '完成进度百分比'
                        }
                    ]
                }
            });

            ganttInstance.on('ready', () => {
                console.log('甘特图风格演示初始化完成');
            });
        }

        // 主题切换
        function toggleTheme() {
            document.body.classList.toggle('dark-theme');
            const button = document.querySelector('.theme-toggle');
            if (document.body.classList.contains('dark-theme')) {
                button.textContent = '☀️ 切换亮色主题';
            } else {
                button.textContent = '🌙 切换深色主题';
            }
        }

        // 事件绑定
        document.getElementById('viewModeSelect').addEventListener('change', (e) => {
            ganttInstance.changeViewMode(e.target.value);
        });

        document.getElementById('testSortBtn').addEventListener('click', () => {
            const columns = ['name', 'type', 'priority', 'status'];
            const column = columns[Math.floor(Math.random() * columns.length)];
            ganttInstance._handleColumnSort(column);
        });

        document.getElementById('resetColumnsBtn').addEventListener('click', () => {
            ganttInstance.options.taskList.columns.forEach(col => {
                col.width = (col.minWidth + col.maxWidth) / 2;
            });
            ganttInstance.renderTaskListHeader();
        });

        document.getElementById('refreshDataBtn').addEventListener('click', () => {
            const newData = generateDemoData();
            ganttInstance.setData(newData);
        });

        // 全局函数
        window.toggleTheme = toggleTheme;

        // 初始化
        document.addEventListener('DOMContentLoaded', () => {
            console.log('页面加载完成，初始化甘特图风格演示...');
            initializeGantt();
        });
    </script>
</body>
</html>

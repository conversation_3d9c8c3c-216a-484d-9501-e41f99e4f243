<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Step3: 周几显示与网格对齐测试</title>
    <link rel="stylesheet" href="../../src/styles/gantt.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }

        .header h1 {
            margin: 0;
            font-size: 24px;
        }

        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
        }

        .test-controls {
            padding: 20px;
            background: #f8fafc;
            border-bottom: 1px solid #e2e8f0;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .control-group {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #e2e8f0;
        }

        .control-group h4 {
            margin: 0 0 10px 0;
            color: #374151;
            font-size: 14px;
        }

        .control-group label {
            display: block;
            margin: 8px 0 4px 0;
            font-size: 12px;
            color: #6b7280;
            font-weight: 500;
        }

        .control-group input,
        .control-group select {
            width: 100%;
            padding: 6px 8px;
            border: 1px solid #d1d5db;
            border-radius: 4px;
            font-size: 14px;
        }

        .control-group button {
            width: 100%;
            padding: 8px;
            background: #4f46e5;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-top: 8px;
        }

        .control-group button:hover {
            background: #4338ca;
        }

        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 8px;
            margin: 8px 0;
        }

        .checkbox-group input[type="checkbox"] {
            width: auto;
        }

        .gantt-container {
            height: 600px;
            position: relative;
            border: 2px solid #4f46e5;
        }

        .alignment-info {
            padding: 20px;
            background: #f1f5f9;
            border-top: 1px solid #e2e8f0;
        }

        .alignment-info h3 {
            margin: 0 0 15px 0;
            color: #374151;
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .info-item {
            background: white;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid #4f46e5;
        }

        .info-label {
            font-size: 12px;
            color: #6b7280;
            text-transform: uppercase;
            margin-bottom: 5px;
        }

        .info-value {
            font-size: 16px;
            font-weight: 600;
            color: #374151;
            font-family: monospace;
        }

        .alignment-status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 600;
        }

        .alignment-status.good {
            background: #dcfce7;
            color: #166534;
        }

        .alignment-status.warning {
            background: #fef3c7;
            color: #92400e;
        }

        .alignment-status.error {
            background: #fee2e2;
            color: #991b1b;
        }

        /* 增强网格可见性 */
        .gantt-chart-body .gantt-grid-layer .grid-line {
            stroke: #4f46e5 !important;
            stroke-width: 1px !important;
            opacity: 0.3 !important;
        }

        .gantt-chart-body .gantt-grid-layer .grid-line.major {
            stroke-width: 2px !important;
            opacity: 0.5 !important;
        }

        /* 增强时间刻度可见性 */
        .timeline-scale.bottom-row {
            border-right: 2px solid #4f46e5 !important;
            background: rgba(79, 70, 229, 0.05) !important;
        }

        .timeline-scale.bottom-row.major {
            background: rgba(79, 70, 229, 0.1) !important;
            border-right: 3px solid #4f46e5 !important;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📅 周几显示与网格对齐测试</h1>
            <p>测试时间轴日期格子宽度与甘特图网格的完美对齐</p>
        </div>

        <div class="test-controls">
            <div class="control-group">
                <h4>周几显示设置</h4>
                <div class="checkbox-group">
                    <input type="checkbox" id="showWeekdayToggle" checked>
                    <label for="showWeekdayToggle">显示周几</label>
                </div>
                <label>周几格式</label>
                <select id="weekdayFormatSelect">
                    <option value="short">中文简写 (周一)</option>
                    <option value="min">中文最简 (一)</option>
                    <option value="abbr">英文缩写 (Mon)</option>
                </select>
            </div>

            <div class="control-group">
                <h4>宽度适配设置</h4>
                <div class="checkbox-group">
                    <input type="checkbox" id="adaptiveWidthToggle" checked>
                    <label for="adaptiveWidthToggle">自适应宽度</label>
                </div>
                <label>像素密度 (pixelsPerDay)</label>
                <input type="range" id="pixelsPerDaySlider" min="15" max="60" value="30">
                <span id="pixelsPerDayValue">30px</span>
                <label>最小宽度</label>
                <input type="range" id="minWidthSlider" min="20" max="80" value="30">
                <span id="minWidthValue">30px</span>
                <label>最大宽度</label>
                <input type="range" id="maxWidthSlider" min="40" max="120" value="80">
                <span id="maxWidthValue">80px</span>
            </div>

            <div class="control-group">
                <h4>视图设置</h4>
                <label>视图模式</label>
                <select id="viewModeSelect">
                    <option value="day">日视图</option>
                    <option value="week">周视图</option>
                    <option value="month">月视图</option>
                </select>
                <label>缩放级别</label>
                <input type="range" id="zoomSlider" min="0.5" max="2.0" step="0.1" value="1.0">
                <span id="zoomValue">100%</span>
            </div>

            <div class="control-group">
                <h4>测试操作</h4>
                <button id="applySettingsBtn">应用设置</button>
                <button id="resetSettingsBtn">重置设置</button>
                <button id="testAlignmentBtn">测试对齐</button>
            </div>
        </div>

        <div class="gantt-container" id="ganttContainer"></div>

        <div class="alignment-info">
            <h3>对齐检测结果</h3>
            <div class="info-grid">
                <div class="info-item">
                    <div class="info-label">当前像素密度</div>
                    <div class="info-value" id="currentPixelsPerDay">-</div>
                </div>
                <div class="info-item">
                    <div class="info-label">刻度宽度</div>
                    <div class="info-value" id="scaleWidth">-</div>
                </div>
                <div class="info-item">
                    <div class="info-label">网格间距</div>
                    <div class="info-value" id="gridSpacing">-</div>
                </div>
                <div class="info-item">
                    <div class="info-label">对齐状态</div>
                    <div class="info-value">
                        <span class="alignment-status" id="alignmentStatus">检测中...</span>
                    </div>
                </div>
                <div class="info-item">
                    <div class="info-label">显示周几</div>
                    <div class="info-value" id="weekdayStatus">-</div>
                </div>
                <div class="info-item">
                    <div class="info-label">实际显示周几</div>
                    <div class="info-value" id="actualWeekdayStatus">-</div>
                </div>
                <div class="info-item">
                    <div class="info-label">总刻度数</div>
                    <div class="info-value" id="totalScales">-</div>
                </div>
                <div class="info-item">
                    <div class="info-label">隐藏周几数量</div>
                    <div class="info-value" id="hiddenWeekdayCount">-</div>
                </div>
            </div>
        </div>
    </div>

    <script type="module">
        import GanttChart from '../../src/GanttChart.js';
        import DateUtils from '../../src/utils/DateUtils.js';

        // 生成测试数据
        function generateTestData() {
            const tasks = [];
            const startDate = new Date('2024-01-01');
            
            for (let i = 0; i < 10; i++) {
                const taskStart = DateUtils.addDays(startDate, i * 3);
                const duration = 5 + Math.random() * 10;
                const taskEnd = DateUtils.addDays(taskStart, duration);
                
                tasks.push({
                    id: `align-test-${i}`,
                    name: `对齐测试任务 ${i + 1}`,
                    startDate: taskStart,
                    endDate: taskEnd,
                    progress: Math.random()
                });
            }
            
            return tasks;
        }

        // 初始化甘特图
        let ganttInstance = null;

        function initializeGantt(options = {}) {
            const container = document.getElementById('ganttContainer');
            container.innerHTML = '';
            
            const testData = generateTestData();
            
            const defaultOptions = {
                data: testData,
                viewMode: 'day',
                pixelsPerDay: 30,
                timeline: {
                    showWeekday: true,
                    adaptiveWidth: true,
                    minScaleWidth: 30,
                    maxScaleWidth: 80,
                    weekdayFormat: 'short'
                },
                taskList: {
                    columns: [
                        { key: 'name', title: '任务名称', width: 200 },
                        { key: 'startDate', title: '开始日期', width: 100 }
                    ]
                }
            };

            const mergedOptions = { ...defaultOptions, ...options };
            
            ganttInstance = new GanttChart('ganttContainer', mergedOptions);

            ganttInstance.on('ready', () => {
                console.log('甘特图初始化完成');
                updateAlignmentInfo();
            });
        }

        // 更新对齐信息
        function updateAlignmentInfo() {
            if (!ganttInstance || !ganttInstance.timeScale) return;

            const timelineConfig = ganttInstance.options.timeline;
            const pixelsPerDay = ganttInstance.options.pixelsPerDay * ganttInstance.state.zoomLevel;

            // 获取刻度信息
            const scales = ganttInstance.timeScale.getAllScales();
            const scaleWidth = scales.length > 1 ? (scales[1].x - scales[0].x) : pixelsPerDay;

            // 检测实际显示的周几数量
            const weekdayElements = document.querySelectorAll('.timeline-scale.bottom-row .weekday-label');
            const totalScalesInDay = ganttInstance.state.viewMode === 'day' ? scales.length : 0;
            const hiddenWeekdayCount = Math.max(0, totalScalesInDay - weekdayElements.length);

            // 更新显示
            document.getElementById('currentPixelsPerDay').textContent = `${pixelsPerDay.toFixed(1)}px`;
            document.getElementById('scaleWidth').textContent = `${scaleWidth.toFixed(1)}px`;
            document.getElementById('gridSpacing').textContent = `${pixelsPerDay.toFixed(1)}px`;
            document.getElementById('weekdayStatus').textContent = timelineConfig.showWeekday ? '是' : '否';
            document.getElementById('actualWeekdayStatus').textContent = weekdayElements.length > 0 ? `${weekdayElements.length}个` : '无';
            document.getElementById('totalScales').textContent = scales.length;
            document.getElementById('hiddenWeekdayCount').textContent = hiddenWeekdayCount;

            // 检测对齐状态
            const alignmentDiff = Math.abs(scaleWidth - pixelsPerDay);
            const statusElement = document.getElementById('alignmentStatus');

            if (alignmentDiff < 1) {
                statusElement.textContent = '完美对齐';
                statusElement.className = 'alignment-status good';
            } else if (alignmentDiff < 5) {
                statusElement.textContent = '基本对齐';
                statusElement.className = 'alignment-status warning';
            } else {
                statusElement.textContent = '未对齐';
                statusElement.className = 'alignment-status error';
            }
        }

        // 应用设置
        function applySettings() {
            const settings = {
                pixelsPerDay: parseInt(document.getElementById('pixelsPerDaySlider').value),
                timeline: {
                    showWeekday: document.getElementById('showWeekdayToggle').checked,
                    adaptiveWidth: document.getElementById('adaptiveWidthToggle').checked,
                    minScaleWidth: parseInt(document.getElementById('minWidthSlider').value),
                    maxScaleWidth: parseInt(document.getElementById('maxWidthSlider').value),
                    weekdayFormat: document.getElementById('weekdayFormatSelect').value
                }
            };

            const viewMode = document.getElementById('viewModeSelect').value;
            const zoomLevel = parseFloat(document.getElementById('zoomSlider').value);

            initializeGantt(settings);

            setTimeout(() => {
                ganttInstance.changeViewMode(viewMode);
                ganttInstance.setZoomLevel(zoomLevel);
                updateAlignmentInfo();
            }, 100);
        }

        // 事件绑定
        document.getElementById('pixelsPerDaySlider').addEventListener('input', (e) => {
            document.getElementById('pixelsPerDayValue').textContent = `${e.target.value}px`;
        });

        document.getElementById('minWidthSlider').addEventListener('input', (e) => {
            document.getElementById('minWidthValue').textContent = `${e.target.value}px`;
        });

        document.getElementById('maxWidthSlider').addEventListener('input', (e) => {
            document.getElementById('maxWidthValue').textContent = `${e.target.value}px`;
        });

        document.getElementById('zoomSlider').addEventListener('input', (e) => {
            const zoomLevel = parseFloat(e.target.value);
            document.getElementById('zoomValue').textContent = `${Math.round(zoomLevel * 100)}%`;
        });

        document.getElementById('applySettingsBtn').addEventListener('click', applySettings);

        document.getElementById('resetSettingsBtn').addEventListener('click', () => {
            document.getElementById('showWeekdayToggle').checked = true;
            document.getElementById('adaptiveWidthToggle').checked = true;
            document.getElementById('pixelsPerDaySlider').value = 30;
            document.getElementById('minWidthSlider').value = 30;
            document.getElementById('maxWidthSlider').value = 80;
            document.getElementById('weekdayFormatSelect').value = 'short';
            document.getElementById('viewModeSelect').value = 'day';
            document.getElementById('zoomSlider').value = 1.0;

            document.getElementById('pixelsPerDayValue').textContent = '30px';
            document.getElementById('minWidthValue').textContent = '30px';
            document.getElementById('maxWidthValue').textContent = '80px';
            document.getElementById('zoomValue').textContent = '100%';

            applySettings();
        });

        document.getElementById('testAlignmentBtn').addEventListener('click', () => {
            updateAlignmentInfo();
            
            // 添加视觉测试辅助
            const scales = document.querySelectorAll('.timeline-scale.bottom-row');
            scales.forEach((scale, index) => {
                scale.style.background = index % 2 === 0 ? 'rgba(79, 70, 229, 0.2)' : 'rgba(124, 58, 237, 0.2)';
            });
            
            setTimeout(() => {
                scales.forEach(scale => {
                    scale.style.background = '';
                });
            }, 2000);
        });

        // 初始化
        document.addEventListener('DOMContentLoaded', () => {
            console.log('页面加载完成，初始化对齐测试...');
            initializeGantt();
        });
    </script>
</body>
</html>

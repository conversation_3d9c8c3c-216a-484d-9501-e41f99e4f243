/**
 * 日期处理工具类
 * 提供甘特图所需的各种日期计算和格式化功能
 */
class DateUtils {
  /**
   * 日期格式常量
   */
  static FORMATS = {
    DATE: "YYYY-MM-DD",
    DATETIME: "YYYY-MM-DD HH:mm:ss",
    DISPLAY: "MM/DD",
    MONTH_YEAR: "YYYY-MM",
    YEAR: "YYYY",
  };

  /**
   * 时间视图模式常量
   */
  static VIEW_MODES = {
    DAY: "day",
    WEEK: "week",
    MONTH: "month",
    QUARTER: "quarter",
    YEAR: "year",
  };

  /**
   * 解析日期字符串为 Date 对象
   * @param {string|Date} dateInput - 日期输入
   * @returns {Date|null} Date 对象或 null
   */
  static parseDate(dateInput) {
    if (!dateInput) return null;

    if (dateInput instanceof Date) {
      return isNaN(dateInput.getTime()) ? null : dateInput;
    }

    if (typeof dateInput === "string") {
      // 支持多种日期格式
      const formats = [
        /^\d{4}-\d{2}-\d{2}$/, // YYYY-MM-DD
        /^\d{4}\/\d{2}\/\d{2}$/, // YYYY/MM/DD
        /^\d{2}\/\d{2}\/\d{4}$/, // MM/DD/YYYY
        /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}/, // ISO format
      ];

      const date = new Date(dateInput);
      return isNaN(date.getTime()) ? null : date;
    }

    return null;
  }

  /**
   * 格式化日期为字符串
   * @param {Date} date - 日期对象
   * @param {string} format - 格式字符串
   * @returns {string} 格式化后的日期字符串
   */
  static formatDate(date, format = DateUtils.FORMATS.DATE) {
    if (!date || !(date instanceof Date)) return "";

    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const day = String(date.getDate()).padStart(2, "0");
    const hours = String(date.getHours()).padStart(2, "0");
    const minutes = String(date.getMinutes()).padStart(2, "0");
    const seconds = String(date.getSeconds()).padStart(2, "0");

    return format
      .replace("YYYY", year)
      .replace("MM", month)
      .replace("DD", day)
      .replace("HH", hours)
      .replace("mm", minutes)
      .replace("ss", seconds);
  }

  /**
   * 计算两个日期之间的天数差
   * @param {Date} startDate - 开始日期
   * @param {Date} endDate - 结束日期
   * @returns {number} 天数差
   */
  static getDaysDiff(startDate, endDate) {
    if (!startDate || !endDate) return 0;

    const oneDay = 24 * 60 * 60 * 1000;
    const start = new Date(
      startDate.getFullYear(),
      startDate.getMonth(),
      startDate.getDate()
    );
    const end = new Date(
      endDate.getFullYear(),
      endDate.getMonth(),
      endDate.getDate()
    );

    return Math.round((end - start) / oneDay);
  }

  /**
   * 添加天数到日期
   * @param {Date} date - 基础日期
   * @param {number} days - 要添加的天数
   * @returns {Date} 新的日期对象
   */
  static addDays(date, days) {
    if (!date) return null;

    const result = new Date(date);
    result.setDate(result.getDate() + days);
    return result;
  }

  /**
   * 添加工作日到日期（跳过周末）
   * @param {Date} date - 基础日期
   * @param {number} workdays - 要添加的工作日数
   * @returns {Date} 新的日期对象
   */
  static addWorkDays(date, workdays) {
    if (!date || workdays === 0) return date;

    const result = new Date(date);
    let daysToAdd = workdays;

    while (daysToAdd > 0) {
      result.setDate(result.getDate() + 1);
      // 跳过周末（0=周日, 6=周六）
      if (result.getDay() !== 0 && result.getDay() !== 6) {
        daysToAdd--;
      }
    }

    return result;
  }

  /**
   * 获取日期所在周的开始日期（周一）
   * @param {Date} date - 目标日期
   * @returns {Date} 周开始日期
   */
  static getWeekStart(date) {
    if (!date) return null;

    const result = new Date(date);
    const day = result.getDay();
    const diff = day === 0 ? -6 : 1 - day; // 周日调整为上周
    result.setDate(result.getDate() + diff);
    result.setHours(0, 0, 0, 0);
    return result;
  }

  /**
   * 获取日期所在月的开始日期
   * @param {Date} date - 目标日期
   * @returns {Date} 月开始日期
   */
  static getMonthStart(date) {
    if (!date) return null;

    const result = new Date(date);
    result.setDate(1);
    result.setHours(0, 0, 0, 0);
    return result;
  }

  /**
   * 获取日期所在季度的开始日期
   * @param {Date} date - 目标日期
   * @returns {Date} 季度开始日期
   */
  static getQuarterStart(date) {
    if (!date) return null;

    const result = new Date(date);
    const quarter = Math.floor(result.getMonth() / 3);
    result.setMonth(quarter * 3, 1);
    result.setHours(0, 0, 0, 0);
    return result;
  }

  /**
   * 获取指定视图模式下的时间单位
   * @param {string} viewMode - 视图模式
   * @returns {object} 时间单位信息
   */
  static getTimeUnit(viewMode) {
    const units = {
      [DateUtils.VIEW_MODES.DAY]: {
        name: "日",
        duration: 1,
        format: "MM/DD",
        increment: (date) => DateUtils.addDays(date, 1),
      },
      [DateUtils.VIEW_MODES.WEEK]: {
        name: "周",
        duration: 7,
        format: "MM/DD",
        increment: (date) => DateUtils.addDays(date, 7),
      },
      [DateUtils.VIEW_MODES.MONTH]: {
        name: "月",
        duration: 30,
        format: "YYYY-MM",
        increment: (date) => {
          const result = new Date(date);
          result.setMonth(result.getMonth() + 1);
          return result;
        },
      },
      [DateUtils.VIEW_MODES.QUARTER]: {
        name: "季度",
        duration: 90,
        format: "YYYY-Q",
        increment: (date) => {
          const result = new Date(date);
          result.setMonth(result.getMonth() + 3);
          return result;
        },
      },
      [DateUtils.VIEW_MODES.YEAR]: {
        name: "年",
        duration: 365,
        format: "YYYY",
        increment: (date) => {
          const result = new Date(date);
          result.setFullYear(result.getFullYear() + 1);
          return result;
        },
      },
    };

    return units[viewMode] || units[DateUtils.VIEW_MODES.DAY];
  }

  /**
   * 生成时间范围内的时间刻度数组
   * @param {Date} startDate - 开始日期
   * @param {Date} endDate - 结束日期
   * @param {string} viewMode - 视图模式
   * @returns {Array} 时间刻度数组
   */
  static generateTimeScale(startDate, endDate, viewMode) {
    if (!startDate || !endDate) return [];

    const unit = DateUtils.getTimeUnit(viewMode);
    const scales = [];
    let current = new Date(startDate);

    // 根据视图模式调整起始位置
    switch (viewMode) {
      case DateUtils.VIEW_MODES.WEEK:
        current = DateUtils.getWeekStart(current);
        break;
      case DateUtils.VIEW_MODES.MONTH:
        current = DateUtils.getMonthStart(current);
        break;
      case DateUtils.VIEW_MODES.QUARTER:
        current = DateUtils.getQuarterStart(current);
        break;
    }

    while (current <= endDate) {
      scales.push({
        date: new Date(current),
        label: DateUtils.formatTimeLabel(current, viewMode),
        x: 0, // 将在渲染时计算
      });

      current = unit.increment(current);
    }

    return scales;
  }

  /**
   * 格式化时间标签
   * @param {Date} date - 日期
   * @param {string} viewMode - 视图模式
   * @returns {string} 格式化的标签
   */
  static formatTimeLabel(date, viewMode) {
    if (!date) return "";

    switch (viewMode) {
      case DateUtils.VIEW_MODES.DAY:
        return DateUtils.formatDate(date, "MM/DD");
      case DateUtils.VIEW_MODES.WEEK:
        const weekEnd = DateUtils.addDays(date, 6);
        return `${DateUtils.formatDate(date, "MM/DD")}-${DateUtils.formatDate(
          weekEnd,
          "MM/DD"
        )}`;
      case DateUtils.VIEW_MODES.MONTH:
        return DateUtils.formatDate(date, "YYYY-MM");
      case DateUtils.VIEW_MODES.QUARTER:
        const quarter = Math.floor(date.getMonth() / 3) + 1;
        return `${date.getFullYear()}-Q${quarter}`;
      case DateUtils.VIEW_MODES.YEAR:
        return DateUtils.formatDate(date, "YYYY");
      default:
        return DateUtils.formatDate(date, "MM/DD");
    }
  }

  /**
   * 检查日期是否为工作日
   * @param {Date} date - 日期
   * @returns {boolean} 是否为工作日
   */
  static isWorkDay(date) {
    if (!date) return false;
    const day = date.getDay();
    return day !== 0 && day !== 6; // 非周日和周六
  }

  /**
   * 获取两个日期之间的工作日数
   * @param {Date} startDate - 开始日期
   * @param {Date} endDate - 结束日期
   * @returns {number} 工作日数
   */
  static getWorkDays(startDate, endDate) {
    if (!startDate || !endDate) return 0;

    let workDays = 0;
    const current = new Date(startDate);

    while (current <= endDate) {
      if (DateUtils.isWorkDay(current)) {
        workDays++;
      }
      current.setDate(current.getDate() + 1);
    }

    return workDays;
  }

  /**
   * 计算日期在时间轴上的相对位置（0-1）
   * @param {Date} date - 目标日期
   * @param {Date} startDate - 时间轴开始日期
   * @param {Date} endDate - 时间轴结束日期
   * @returns {number} 相对位置（0-1）
   */
  static getRelativePosition(date, startDate, endDate) {
    if (!date || !startDate || !endDate) return 0;

    const totalDays = DateUtils.getDaysDiff(startDate, endDate);
    const daysPassed = DateUtils.getDaysDiff(startDate, date);

    return totalDays > 0 ? Math.max(0, Math.min(1, daysPassed / totalDays)) : 0;
  }

  /**
   * 验证日期范围是否合理
   * @param {Date} startDate - 开始日期
   * @param {Date} endDate - 结束日期
   * @returns {object} 验证结果
   */
  static validateDateRange(startDate, endDate) {
    const result = {
      valid: true,
      errors: [],
    };

    if (!startDate) {
      result.valid = false;
      result.errors.push("开始日期不能为空");
    }

    if (!endDate) {
      result.valid = false;
      result.errors.push("结束日期不能为空");
    }

    if (startDate && endDate && startDate > endDate) {
      result.valid = false;
      result.errors.push("开始日期不能晚于结束日期");
    }

    return result;
  }

  /**
   * 获取当前日期
   * @returns {Date} 当前日期
   */
  static today() {
    const now = new Date();
    now.setHours(0, 0, 0, 0);
    return now;
  }

  /**
   * 比较两个日期是否相等（只比较日期部分）
   * @param {Date} date1 - 日期1
   * @param {Date} date2 - 日期2
   * @returns {boolean} 是否相等
   */
  static isSameDate(date1, date2) {
    if (!date1 || !date2) return false;

    return (
      date1.getFullYear() === date2.getFullYear() &&
      date1.getMonth() === date2.getMonth() &&
      date1.getDate() === date2.getDate()
    );
  }

  /**
   * 获取日期的时间戳（毫秒）
   * @param {Date} date - 日期
   * @returns {number} 时间戳
   */
  static getTimestamp(date) {
    return date ? date.getTime() : 0;
  }
}

window.DateUtils = DateUtils;
export default DateUtils;